<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>xia-parent</artifactId>
        <groupId>cn.facilityone.xia</groupId>
        <version>1.3.2-SNAPSHOT</version>
    </parent>

    <groupId>cn.facilityone</groupId>
    <artifactId>shang</artifactId>
    <packaging>war</packaging>
    <version>1.0-SNAPSHOT</version>
    <name>shang</name>
    <url>http://maven.apache.org</url>

    <dependencies>

        <!-- Start of Production Configuration -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.3</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        
        <dependency>
		    <groupId>de.codecentric</groupId>
		    <artifactId>spring-boot-admin-starter-client</artifactId>
		    <version>1.2.4</version>
		</dependency>
        <!-- End of Production Configuration -->

        <dependency>
            <groupId>cn.facilityone.xia</groupId>
            <artifactId>xia-i18n</artifactId>
            <version>${shang.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.facilityone.xia</groupId>
            <artifactId>xia-web</artifactId>
            <version>${shang.version}</version>
        </dependency>

        <!-- used by hibernate4-maven-plugin, need to define explicitly so that 
            hibernate4-maven-plugin can scan for annotated entity classes -->
        <dependency>
            <groupId>cn.facilityone.xia</groupId>
            <artifactId>xia-core</artifactId>
            <version>${shang.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.facilityone.xia</groupId>
                    <artifactId>xia-bpm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.facilityone.xia</groupId>
            <artifactId>xia-transfer</artifactId>
            <version>${shang.version}</version>
        </dependency>


        <dependency>
            <groupId>cn.facilityone.xia</groupId>
            <artifactId>xia-message</artifactId>
            <version>${shang.version}</version>
        </dependency>
        
        <dependency>
            <groupId>cn.facilityone.xia</groupId>
            <artifactId>xia-monitor</artifactId>
            <version>${shang.version}</version>
        </dependency>

        <!-- <dependency>
            <groupId>cn.facilityone.xia</groupId>
            <artifactId>xia-cache</artifactId>
            <version>${shang.version}</version>
        </dependency> -->

        <!-- <dependency>
            <groupId>cn.facilityone.xia</groupId>
            <artifactId>xia-bpm</artifactId>
            <version>${shang.version}</version>
        </dependency> -->
        
        <dependency>
             <groupId>cn.facilityone.xia</groupId>
             <artifactId>xia-pay</artifactId>
             <version>${shang.version}</version>
         </dependency>

        <dependency>
            <groupId>cn.facilityone.xia</groupId>
            <artifactId>xia-stream</artifactId>
            <version>${shang.version}</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.29</version>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jersey.ext</groupId>
            <artifactId>jersey-bean-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.3</version><!--$NO-MVN-MAN-VER$-->
        </dependency>

        <!-- QRCode -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.0.1</version>
        </dependency>

        <!-- quartz -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz-jobs</artifactId>
            <version>2.2.1</version>
        </dependency>

        <dependency>
            <groupId>org.jscience</groupId>
            <artifactId>jscience</artifactId>
            <version>4.3.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>javax.measure</groupId>-->
            <!--<artifactId>unit-api</artifactId>-->
            <!--<version>0.8-RC2</version>-->
        <!--</dependency>-->


        <!-- To build a war file that is both executable and deployable into an 
            external container. -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <!--<scope>provided</scope>-->
        </dependency>
        
        <!--solr -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-solr</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.codehaus.woodstox</groupId>
                    <artifactId>wstx-asl</artifactId>
                </exclusion>
            </exclusions>
         </dependency>

        <!-- file uplaod -->
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.2.2</version>
        </dependency>
        
        <!-- DB initial -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
        
        <!-- TEST  -->
        <dependency>
            <artifactId>xia-test</artifactId>
            <groupId>cn.facilityone.xia</groupId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.dbunit</groupId>
            <artifactId>dbunit</artifactId>
            <version>2.5.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.springtestdbunit</groupId>
            <artifactId>spring-test-dbunit</artifactId>
            <version>1.2.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.1</version>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.5</version>
        </dependency>
        <!-- amr 转码 mp3 -->
        <dependency>
            <groupId>fo.it.sauronsoftware</groupId>
            <artifactId>jave</artifactId>
            <version>1.0.2</version>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>com.nitorcreations</groupId>-->
        <!--<artifactId>junit-runners</artifactId>-->
        <!--<version>1.2</version>-->
        <!--<scope>test</scope>-->
        <!--</dependency>-->

        <!--<dependency>-->
        <!--<groupId>com.googlecode.catch-exception</groupId>-->
        <!--<artifactId>catch-exception</artifactId>-->
        <!--<version>1.2.0</version>-->
        <!--<scope>test</scope>-->
        <!--</dependency>-->

        <!-- https://mvnrepository.com/artifact/org/jaudiotagger -->
        <dependency>
            <groupId>org</groupId>
            <artifactId>jaudiotagger</artifactId>
            <version>2.0.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.9.6</version>
        </dependency>

		<dependency>
		  <groupId>joda-time</groupId>
		  <artifactId>joda-time</artifactId>
		  <version>2.9.9</version>
		</dependency>

        <!-- cxf框架依赖  -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>3.1.7</version>
        </dependency>
        <!--dingtalk sdk-->
        <dependency>
            <groupId>dingtalk-sdk</groupId>
            <artifactId>taobao-dingtalk-sdk</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>shang</finalName>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>

                <configuration>
                    <mainClass>cn.facilityone.Bootstrap</mainClass>

                    <!-- Reslove: spring-boot jersey based application : can not start without 
                        errors using java -jar command, just for jar not for war. https://github.com/spring-projects/spring-boot/issues/1345#issuecomment-51502294 -->
                    <requiresUnpack>
                        <dependency>
                            <groupId>cn.facilityone.xia</groupId>
                            <artifactId>xia-bpm</artifactId>
                        </dependency>
                        <dependency>
                            <groupId>cn.facilityone.xia</groupId>
                            <artifactId>xia-web</artifactId>
                        </dependency>
                        <dependency>
                            <groupId>cn.facilityone.xia</groupId>
                            <artifactId>xia-cache</artifactId>
                        </dependency>
                        <dependency>
                            <groupId>cn.facilityone.xia</groupId>
                            <artifactId>xia-core</artifactId>
                        </dependency>
                        <dependency>
                            <groupId>cn.facilityone.xia</groupId>
                            <artifactId>xia-persistence</artifactId>
                        </dependency>
                        <dependency>
                            <groupId>cn.facilityone.xia</groupId>
                            <artifactId>xia-message</artifactId>
                        </dependency>
<!--                        <dependency>-->
<!--                            <groupId>cn.facilityone.xia</groupId>-->
<!--                            <artifactId>xia-stream</artifactId>-->
<!--                        </dependency>-->
                    </requiresUnpack>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <repositories>
        <repository>
            <id>facilityone-nexus-snapshotss</id>
            <url>http://192.168.1.146:8080/nexus/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>

        <repository>
            <id>facilityone-nexus-releasess</id>
            <url>http://192.168.1.146:8080/nexus/content/repositories/releases/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>

        <repository>
            <id>spring-milestone</id>
            <url>http://repo.spring.io/milestone/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <properties>
        <shang.version>1.3.2-SNAPSHOT</shang.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>
