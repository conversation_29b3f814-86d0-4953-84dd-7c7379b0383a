package cn.facilityone.shang.ai.service.impl;

import cn.facilityone.shang.ai.dto.*;
import cn.facilityone.shang.ai.properties.AiProperties;
import cn.facilityone.shang.ai.service.AiService;
import cn.facilityone.shang.api.service.ApiSpaceProjectService;
import cn.facilityone.shang.entity.organize.Building;
import cn.facilityone.shang.entity.organize.Floor;
import cn.facilityone.shang.entity.organize.Room;
import cn.facilityone.shang.organize.org004.repository.BuildingRepository;
import cn.facilityone.shang.organize.org004.repository.FloorRepository;
import cn.facilityone.shang.organize.org004.repository.RoomRepository;
import cn.facilityone.shang.projects.pro002.dto.ProjectNameDTO;

import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.security.entity.User;
import cn.facilityone.xia.security.service.SecurityService;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/19 16:21
 * @Version 1.0
 */
@Service
@Slf4j
public class AiServiceImpl implements AiService {

    @Autowired
    private AiProperties aiProperties;
    @Autowired
    private SecurityService securityService;
    @Autowired
    private RoomRepository roomRepository;
    @Autowired
    private FloorRepository floorRepository;
    @Autowired
    private BuildingRepository buildingRepository;
    @Autowired
    private ApiSpaceProjectService apiSpaceProjectService;

    @Override
    public Result getUserInfo(HttpServletRequest request, String username) {
        if (!checkToken(request)) {
            return Result.fail("认证失败");
        }
        if (StrUtil.isBlank(username)) {
            return Result.fail("用户名不能为空");
        }

        User user = securityService.getUserByLoginCode(username);
        if (user == null) {
            return Result.fail("用户不存在");
        }
        
        AiSimpleUserDTO u = new AiSimpleUserDTO();
        u.setUserName(user.getLoginCode());
        u.setRealName(user.getRealName());
        u.setPhone(user.getMobilePhone());
        u.setId(user.getId());
        u.setUserId(user.getId());

        // 获取用户授权的项目
        List<ProjectNameDTO> projects = apiSpaceProjectService.findAllProjects();
        List<AiSimpleProjectDTO> projectDTOList = projects.stream()
                .map(p -> {
                    AiSimpleProjectDTO dto = new AiSimpleProjectDTO();
                    dto.setId(p.getProjectId());
                    dto.setName(p.getProjectName());
                    return dto;
                }).collect(Collectors.toList());
        u.setProjects(projectDTOList);
        
        return Result.data(u);
    }

    @Override
    public Result getProject(HttpServletRequest request, Long projectId) {
        if (!checkToken(request)) {
            return Result.fail("认证失败");
        }
        
        List<AiSimpleProjectDTO> projectDTOList = new ArrayList<>();
        if (projectId != null && projectId > 0) {
            List<ProjectNameDTO> projects = apiSpaceProjectService.findAllProjects();
            Optional<ProjectNameDTO> project = projects.stream()
                    .filter(p -> p.getProjectId().equals(projectId))
                    .findFirst();
            if (project.isPresent()) {
                AiSimpleProjectDTO projectDTO = new AiSimpleProjectDTO();
                projectDTO.setId(project.get().getProjectId());
                projectDTO.setName(project.get().getProjectName());
                projectDTOList.add(projectDTO);
            }
        } else {
            List<ProjectNameDTO> projects = apiSpaceProjectService.findAllProjects();
            for (ProjectNameDTO project : projects) {
                AiSimpleProjectDTO projectDTO = new AiSimpleProjectDTO(project.getProjectId(), project.getProjectName());
                projectDTOList.add(projectDTO);
            }
        }
        return Result.data(projectDTOList);
    }

    @Override
    public Result getLocation(HttpServletRequest request, Long projectId, Long buildingId, Long floorId) {
        if (!checkToken(request)) {
            return Result.fail("认证失败");
        }
        
        try {
            if (projectId != null) {
                ProjectContext.setCurrentProject(projectId);
            }

            List<Map<String, Object>> locationList = new ArrayList<>();
            
            // 查询房间
            if (floorId != null) {
                List<Room> rooms = roomRepository.findAllHardly().stream()
                        .filter(room -> room.getFloor() != null && room.getFloor().getId().equals(floorId))
                        .filter(room -> projectId == null || room.getProject().equals(projectId))
                        .collect(Collectors.toList());
                
                for (Room room : rooms) {
                    Map<String, Object> roomMap = new HashMap<>();
                    roomMap.put("id", room.getId());
                    roomMap.put("name", room.getName());
                    roomMap.put("fullName", room.getFullName());
                    roomMap.put("sort", room.getSort());
                    roomMap.put("level", 3);
                    locationList.add(roomMap);
                }
            } else if (buildingId != null) {
                List<Floor> floors = floorRepository.findAllHardly().stream()
                        .filter(floor -> floor.getBuilding() != null && floor.getBuilding().getId().equals(buildingId))
                        .filter(floor -> projectId == null || floor.getProject().equals(projectId))
                        .collect(Collectors.toList());
                
                for (Floor floor : floors) {
                    Map<String, Object> floorMap = new HashMap<>();
                    floorMap.put("id", floor.getId());
                    floorMap.put("name", floor.getName());
                    floorMap.put("fullName", floor.getFullName());
                    floorMap.put("sort", floor.getSort());
                    floorMap.put("level", 2);
                    locationList.add(floorMap);
                }
            } else {
                List<Building> buildings = buildingRepository.findAllHardly().stream()
                        .filter(building -> projectId == null || building.getProject().equals(projectId))
                        .collect(Collectors.toList());
                
                for (Building building : buildings) {
                    Map<String, Object> buildingMap = new HashMap<>();
                    buildingMap.put("id", building.getId());
                    buildingMap.put("name", building.getName());
                    buildingMap.put("fullName", building.getFullName());
                    buildingMap.put("sort", building.getSort());
                    buildingMap.put("level", 1);
                    locationList.add(buildingMap);
                }
            }
            
            return Result.data(locationList);
        } catch (Exception e) {
            log.error("获取位置信息失败", e);
            return Result.fail("获取位置信息失败: " + e.getMessage());
        }
    }

    private boolean checkToken(HttpServletRequest request) {
        if (!aiProperties.getEnabled() || StrUtil.isBlank(aiProperties.getBearerToken())) {
            return false;
        }
        String authorization = request.getHeader("Authorization");
        if (StrUtil.isBlank(authorization)) {
            return false;
        }
        if (authorization.length() < 8 || !authorization.startsWith("Bearer ")) {
            return false;
        }
        authorization = authorization.substring(7);
        String bearerTokenStr = aiProperties.getBearerToken();
        String[] split = bearerTokenStr.split(",");
        for (String s : split) {
            if (StrUtil.isNotBlank(s) && Objects.equals(authorization, s)) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public Result createWorkOrder(HttpServletRequest request, AiCreateWorkOrderDTO dto) {
        if (!checkToken(request)) {
            return Result.fail("认证失败");
        }
        
        // 验证必要参数
        if (dto == null) {
            return Result.fail("参数不能为空");
        }
        
        if (StrUtil.isBlank(dto.getDescription())) {
            return Result.fail("工单描述不能为空");
        }
        
        if (dto.getProjectId() == null) {
            return Result.fail("项目ID不能为空");
        }
        
        // TODO: 实现工单创建逻辑
        // 这里需要根据实际业务逻辑实现工单创建
        log.info("创建工单请求: {}", dto);
        
        // 返回创建成功的结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        resultMap.put("message", "工单创建成功");
        
        return Result.data(resultMap);
    }
    
    @Override
    public Result searchWorkOrder(HttpServletRequest request, AiSearchWorkOrderDTO dto) {
        if (!checkToken(request)) {
            return Result.fail("认证失败");
        }
        
        // 验证必要参数
        if (dto == null) {
            return Result.fail("参数不能为空");
        }
        
        // 设置默认分页参数
        if (dto.getPageNumber() == null || dto.getPageNumber() < 1) {
            dto.setPageNumber(1);
        }
        
        if (dto.getPageSize() == null || dto.getPageSize() < 1) {
            dto.setPageSize(10);
        }
        
        // TODO: 实现工单搜索逻辑
        // 这里需要根据实际业务逻辑实现工单搜索
        log.info("搜索工单请求: {}", dto);
        
        // 返回搜索结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", 0);
        resultMap.put("records", new ArrayList<>());
        
        return Result.data(resultMap);
    }
    
    @Override
    public Result searchReqOrder(HttpServletRequest request, AiSearchReqOrderDTO dto) {
        if (!checkToken(request)) {
            return Result.fail("认证失败");
        }
        
        // 验证必要参数
        if (dto == null) {
            return Result.fail("参数不能为空");
        }
        
        // 设置默认分页参数
        if (dto.getPageNumber() == null || dto.getPageNumber() < 1) {
            dto.setPageNumber(1);
        }
        
        if (dto.getPageSize() == null || dto.getPageSize() < 1) {
            dto.setPageSize(10);
        }
        
        // TODO: 实现需求单搜索逻辑
        // 这里需要根据实际业务逻辑实现需求单搜索
        log.info("搜索需求单请求: {}", dto);
        
        // 返回搜索结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", 0);
        resultMap.put("records", new ArrayList<>());
        
        return Result.data(resultMap);
    }
}
