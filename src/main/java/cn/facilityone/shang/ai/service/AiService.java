package cn.facilityone.shang.ai.service;

import cn.facilityone.xia.core.common.Result;
import cn.facilityone.shang.ai.dto.AiCreateWorkOrderDTO;
import cn.facilityone.shang.ai.dto.AiSearchReqOrderDTO;
import cn.facilityone.shang.ai.dto.AiSearchWorkOrderDTO;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2025/3/19 16:21
 * @Version 1.0
 */
public interface AiService {

    /**
     * 获取用户信息
     *
     * @param request
     * @param username
     * @return
     */
    Result getUserInfo(HttpServletRequest request, String username);

    /**
     * 获取项目信息
     *
     * @param request
     * @param projectId
     * @return
     */
    Result getProject(HttpServletRequest request, Long projectId);

    /**
     * 获取位置信息
     *
     * @param request
     * @param projectId
     * @param buildingId
     * @param floorId
     * @return
     */
    Result getLocation(HttpServletRequest request, Long projectId, Long buildingId, Long floorId);
    
    /**
     * 创建工单
     *
     * @param request
     * @param dto
     * @return
     */
    Result createWorkOrder(HttpServletRequest request, AiCreateWorkOrderDTO dto);
    
    /**
     * 搜索工单
     *
     * @param request
     * @param dto
     * @return
     */
    Result searchWorkOrder(HttpServletRequest request, AiSearchWorkOrderDTO dto);
    
    /**
     * 搜索需求单
     *
     * @param request
     * @param dto
     * @return
     */
    Result searchReqOrder(HttpServletRequest request, AiSearchReqOrderDTO dto);
}
