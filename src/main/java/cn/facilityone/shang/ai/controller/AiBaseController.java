package cn.facilityone.shang.ai.controller;

import cn.facilityone.shang.ai.dto.AiCreateWorkOrderDTO;
import cn.facilityone.shang.ai.dto.AiSearchReqOrderDTO;
import cn.facilityone.shang.ai.dto.AiSearchWorkOrderDTO;
import cn.facilityone.shang.ai.service.AiService;
import cn.facilityone.xia.core.common.Result;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * AI 基础接口
 *
 * <AUTHOR>
 * @Date 2025/5/6 09:06
 * @Version 1.0
 */
@Path("/ai")
public class AiBaseController {

    @Autowired
    private AiService aiService;

    @Context
    private HttpServletRequest request;

    /**
     * 获取用户信息
     */
    @GET
    @Path("/getUserInfo/{username}")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result getUserInfo(@PathParam("username") String username) {
        return aiService.getUserInfo(request, username);
    }

    /**
     * 获取项目信息
     */
    @GET
    @Path("/getProjects")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result getProject(@QueryParam("projectId") Long projectId) {
        return aiService.getProject(request, projectId);
    }

    /**
     * 获取位置信息
     */
    @POST
    @Path("/getLocation")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result getLocation(JSONObject jsonObject) {
        Long projectId = jsonObject.getLong("projectId");
        Long buildingId = jsonObject.getLong("buildingId");
        Long floorId = jsonObject.getLong("floorId");
        if (null == projectId && buildingId == null && floorId == null) {
            return Result.fail("参数不能为空");
        }
        return aiService.getLocation(request, projectId, buildingId, floorId);
    }
    
    /**
     * 创建工单
     */
    @POST
    @Path("/createWorkOrder")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result createWorkOrder(AiCreateWorkOrderDTO dto) {
        return aiService.createWorkOrder(request, dto);
    }
    
    /**
     * 搜索工单
     */
    @POST
    @Path("/searchWorkOrder")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result searchWorkOrder(AiSearchWorkOrderDTO dto) {
        return aiService.searchWorkOrder(request, dto);
    }
    
    /**
     * 搜索需求单
     */
    @POST
    @Path("/searchReqOrder")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result searchReqOrder(AiSearchReqOrderDTO dto) {
        return aiService.searchReqOrder(request, dto);
    }
}
