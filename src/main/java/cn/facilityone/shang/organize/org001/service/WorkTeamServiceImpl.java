package cn.facilityone.shang.organize.org001.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.organize.Organization;
import cn.facilityone.shang.entity.organize.WorkTeam;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.organize.common.alias.WorkTeamRoleView;
import cn.facilityone.shang.organize.common.mapper.WorkTeamMapper;
import cn.facilityone.shang.organize.org001.builder.WorkTeamBuilder;
import cn.facilityone.shang.organize.org001.dto.WorkTeamDTO;
import cn.facilityone.shang.organize.org001.repository.WorkTeamRepository;
import cn.facilityone.shang.organize.org001.staticmetamodel.WorkTeam_;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.organize.org003.repository.OrganizationRepository;
import cn.facilityone.shang.patrol.common.repository.PatrolRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMStepRepository;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import cn.facilityone.shang.servicecenter.common.service.WorkOrderLaborerService;
import cn.facilityone.shang.workorder.wo003.dto.AutoDispatchingVO;
import cn.facilityone.shang.workorder.wo003.repository.WorkOrderProcessRepository;
import cn.facilityone.shang.workorder.wo005.service.SchedulingService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2015-4-9
 */
@Service
public class WorkTeamServiceImpl implements WorkTeamService {

    private static final String CHACHE_NAME = "WorkTeamServiceImpl";
    private static final Logger log = LoggerFactory.getLogger(WorkTeamServiceImpl.class);

    private final JdbcTemplate jdbcTemplate;
    private final WorkTeamRepository workTeamRepository;
    private final EmployeeRepository employeeRepository;
    private final DataTableService dataTableService;
    private final WorkTeamBuilder workTeamBuilder;
    private final SchedulingService schedulingService;

    @Autowired
    private WorkOrderLaborerService workOrderLaborerService;

    @Autowired
    private WorkOrderProcessRepository workOrderProcessRepository;

    @Autowired
    private PreventiveMaintenanceRepository preventiveMaintenanceRepository;

    @Autowired
    private PatrolRepository patrolRepository;

    @Autowired
    private PMStepRepository pmStepRepository;

    @Autowired
    private WorkTeamMapper workTeamMapper;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    public WorkTeamServiceImpl(JdbcTemplate jdbcTemplate, SchedulingService schedulingService, WorkTeamBuilder workTeamBuilder, DataTableService datable, WorkTeamRepository wt, EmployeeRepository em) {
        this.jdbcTemplate = jdbcTemplate;
        this.workTeamRepository = wt;
        this.employeeRepository = em;
        this.dataTableService = datable;
        this.workTeamBuilder = workTeamBuilder;
        this.schedulingService = schedulingService;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public WorkTeam create(WorkTeamDTO workTeamDTO) {
        WorkTeam workTeam = new WorkTeam();
        //基本信息
        workTeam.setName(workTeamDTO.getName());
        if (workTeamDTO.getOrganizations() != null && workTeamDTO.getOrganizations().length > 0) {
            workTeam.setOrganizations(organizationRepository.findByIdIn(Arrays.asList(workTeamDTO.getOrganizations())));
        }else{
            workTeam.setOrganizations(null);
        }
        workTeam.setDescription(workTeamDTO.getDescription());
        //所有关联人员获得
        List<Employee> employeeLGroup = findManagementEmployees(workTeamDTO);
        //主管拥有所有权限
        workTeamDTO = superManagerRoles(workTeamDTO);
        //人员按权限分配在工作组中
        workTeam = assignEmployees(workTeamDTO, workTeam, employeeLGroup);
        workTeam = workTeamRepository.save(workTeam);
        //mappedBy=Employee 员工维护
        List<Employee> mappedEmployeelist = workTeam.getMembers();
        addMembersRelationship(workTeam, mappedEmployeelist);
        return workTeam;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void update(WorkTeamDTO workTeamDTO) {
        WorkTeam workTeam = workTeamRepository.findOneById(workTeamDTO.getId());
        //基本信息
        workTeam.setName(workTeamDTO.getName());
        if (workTeamDTO.getOrganizations() != null && workTeamDTO.getOrganizations().length > 0) {
            workTeam.setOrganizations(organizationRepository.findByIdIn(Arrays.asList(workTeamDTO.getOrganizations())));
        }else{
            workTeam.setOrganizations(null);
        }
        workTeam.setDescription(workTeamDTO.getDescription());
        List<Employee> employeeGroup = workTeam.getMembers();
        List<Employee> employeesOriginal = new ArrayList<>();
        if (employeeGroup != null && employeeGroup.size() > 0) {
            for (Employee employee : employeeGroup) {
                employee.getId();
            }
            //工作组原员工clone至employeesOriginal
            employeesOriginal.addAll(employeeGroup);
        }
        //所有关联人员获得
        List<Employee> employeeList = findManagementEmployees(workTeamDTO);
        //主管拥有所有权限
        workTeamDTO = superManagerRoles(workTeamDTO);
        //人员按权限分配在工作组中
        workTeam = assignEmployees(workTeamDTO, workTeam, employeeList);
        List<Employee> employeeAfter = workTeam.getMembers();
        //员工合并去重
        employeeGroup.removeAll(employeeAfter);
        employeeGroup.addAll(employeeAfter);
        workTeam.setMembers(employeeGroup);
        //激活
        workTeam.setActivated(Boolean.TRUE);
        workTeam = workTeamRepository.save(workTeam);
        //mappedBy=employee
        //新添加的员工 员工维护工作组关系
        employeeGroup.remove(employeesOriginal);
        addMembersRelationship(workTeam, employeeGroup);
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void delete(Long id) {
        workTeamRepository.delete(id);

        workTeamRepository.deleteMemberInWorkTeam(id);
        workTeamRepository.deleteArchMemberInWorkTeam(id);
        workTeamRepository.deleteDisaptchMemberInWorkTeam(id);
        workTeamRepository.deleteTrackMemberInWorkTeam(id);
        workTeamRepository.deleteValidateMemberInWorkTeam(id);
        workTeamRepository.deleteSupervisorMemberInWorkTeam(id);
    }


    @Override
    @XiaTransactional(readOnly = true)
    public WorkTeam findOne(Long id) {
        WorkTeam workTeam = workTeamRepository.findOne(id);
        //LAZY LOAD

        if (workTeam != null) {
            List<WorkTeam> workTeams = new ArrayList<>();
            workTeams.add(workTeam);
            workTeamBuilder.init(workTeams).addTracers().addSuper().addDispatchers().addArchivers().addMemebers().addValidaters().addInspectors();
            workTeam.setOrganizations(workTeamRepository.findOrgsById(id));
        }

        return workTeam;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void addMembers(Long[] ids, Long workteamId) {
        WorkTeam workTeam = workTeamRepository.findOne(workteamId);
        List<Employee> employeeList = workTeam.getMembers();
        List<Employee> employeeOriginal = new ArrayList<>();
        if (employeeList != null && employeeList.size() > 0) {
            for (Employee employee : employeeList) {
                employee.getId();
            }
            //工作组原有员工clone
            employeeOriginal.addAll(employeeList);
        }
        List<Employee> employees = employeeRepository.findByIdIn(new HashSet<Long>(Arrays.asList(ids)));
        if (employeeList != null && employeeList.size() > 0) {
            employeeList.removeAll(employees);
        }
        if (employees != null && employees.size() > 0) {
            employeeList.addAll(employees);
        }
        employeeList.removeAll(employeeOriginal);
        //mappedBy employee为新添加的员工添加维护关系
        addMembersRelationship(workTeam, employeeList);

    }

    @Override
    public List<Long> findEmployeeIdsByWorkTeam(Long workTeamId) {
        return employeeRepository.findIdsByWorkTeamId(workTeamId);
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void deleteMemberInWorkTeam(Long workteamId, Long memberId) {
        workTeamRepository.deleteMemberInWorkTeam(memberId, new Long[]{workteamId});
        workTeamRepository.deleteTrackMemberInWorkTeam(memberId, new Long[]{workteamId});
        workTeamRepository.deleteDisaptchMemberInWorkTeam(memberId, new Long[]{workteamId});
        workTeamRepository.deleteValidateMemberInWorkTeam(memberId, new Long[]{workteamId});
        workTeamRepository.deleteArchMemberInWorkTeam(memberId, new Long[]{workteamId});
    }

    @Override
    @Transactional(readOnly = false)
    public void deleteMemberInWorkTeamMapper(Long workTeamId, Long memberId) {
        workTeamMapper.deleteMemberInWorkTeam(memberId, Arrays.asList(workTeamId));
        workTeamMapper.deleteTrackMemberInWorkTeam(memberId, Arrays.asList(workTeamId));
        workTeamMapper.deleteDisaptchMemberInWorkTeam(memberId, Arrays.asList(workTeamId));
        workTeamMapper.deleteValidateMemberInWorkTeam(memberId, Arrays.asList(workTeamId));
        workTeamMapper.deleteArchMemberInWorkTeam(memberId, Arrays.asList(workTeamId));
    }

    @Override
    @XiaTransactional(readOnly = true)
    public Page<WorkTeam> findPageWorkTeam(final DataTableRequest request, Pageable page) {
        if (null == page) {
            page =
                    new DataTableRequest(page.getPageNumber(), page.getOffset(), page.getPageSize(),
                            dataTableService.buildColumnSort(request.getColumns()),
                            request.getDraw());
        }
        Page<WorkTeam> result = workTeamRepository.findAll(new Specification<WorkTeam>() {
            @Override
            public Predicate toPredicate(Root<WorkTeam> root, CriteriaQuery<?> criteriaQuery,
                                         CriteriaBuilder criteriaBuilder) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();
                List<DataTableColumn> columnList = request.getColumns();
                if (columnList != null && columnList.size() > 0) {
                    List<Predicate> lps =
                            dataTableService.buildColumnSearch(columnList, root, criteriaBuilder);
                    predicatesList.addAll(lps);
                }
                criteriaQuery.distinct(true);
                criteriaQuery.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                return criteriaQuery.getRestriction();
            }

        }, page);
        workTeamBuilder.init(result.getContent()).addDispatchers().addSuper().addTracers().addInspectors();
        return result;
    }

    /**
     * Get employee role set in the Work Team
     *
     * @param employeeId
     * @param teamId
     * @return
     */
    @Override
//    @Cacheable(value = CHACHE_NAME, key = "#employee.id+#team.id+'getRoleInWorkTeam'")
    public Set<String> getRoleInWorkTeam(Long employeeId, Long teamId) {
        //TODO 性能优化
        Set<String> roles = new HashSet<>();

        //TODO Need cache or change to query by employee id and work team id
//        List<Long> archivers = team == null ? null : workTeamRepository.findArchiverById(team.getId());
//        List<Long> superviors = team == null ? null : workTeamRepository.findSuperById(team.getId());
//        List<Long> dispatchers = team == null ? null : workTeamRepository.findDispatchersById(team.getId());
//        List<Long> tracers = team == null ? null : workTeamRepository.findTracersById(team.getId());
//        List<Long> validaters = team == null ? null : workTeamRepository.findValidaterById(team.getId());

        String sql = " select  " +
                "   (SELECT count(obj.work_team_id) from work_team_dispatcher obj where   obj.work_team_id = wt.work_team_id and  obj.em_id = " + employeeId + ") as _dispatcher" +
                " , (SELECT count(obj.work_team_id) from work_team_archiver obj where   obj.work_team_id = wt.work_team_id and  obj.em_id = " + employeeId + ") as _archiver" +
                " , (SELECT count(obj.work_team_id) from work_team_supervisor obj where   obj.work_team_id = wt.work_team_id and  obj.em_id = " + employeeId + ") as _supervisor " +
                " , (SELECT count(obj.work_team_id) from work_team_tracer obj where   obj.work_team_id = wt.work_team_id and  obj.em_id = " + employeeId + ") as _tracer" +
                " , (SELECT count(obj.work_team_id) from work_team_validater obj where   obj.work_team_id = wt.work_team_id and  obj.em_id = " + employeeId + ") as _validater" +
                " from work_team  wt  " +
                " where " +
                "      wt.work_team_id = " + teamId + " and  wt.deleted = 0 ";

        Map<String, Object> map = jdbcTemplate.queryForMap(sql);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (Integer.parseInt(entry.getValue().toString()) > 0) {
                if ("_dispatcher".equals(entry.getKey())) {
                    roles.add(WorkTeam_.WORK_TEAM_ROLE_DISPATCHER);
                } else if ("_archiver".equals(entry.getKey())) {
                    roles.add(WorkTeam_.WORK_TEAM_ROLE_ARCHIVER);
                } else if ("_supervisor".equals(entry.getKey())) {
                    roles.add(WorkTeam_.WORK_TEAM_ROLE_SUPERVISOR);
                } else if ("_tracer".equals(entry.getKey())) {
                    roles.add(WorkTeam_.WORK_TEAM_ROLE_TRACKER);
                } else if ("_validater".equals(entry.getKey())) {
                    roles.add(WorkTeam_.WORK_TEAM_ROLE_VALIDATOR);
                }
            }
        }

//        if (CollectionUtils.isNotEmpty(archivers) && archivers.contains(employeeId)) {
//            roles.add(WorkTeam_.WORK_TEAM_ROLE_ARCHIVER);
//        }
//        if (CollectionUtils.isNotEmpty(superviors) && superviors.contains(employeeId)) {
//            roles.add(WorkTeam_.WORK_TEAM_ROLE_SUPERVISOR);
//        }
//        if (CollectionUtils.isNotEmpty(dispatchers) && dispatchers.contains(employeeId)) {
//            roles.add(WorkTeam_.WORK_TEAM_ROLE_DISPATCHER);
//        }
//        if (CollectionUtils.isNotEmpty(tracers) && tracers.contains(employeeId)) {
//            roles.add(WorkTeam_.WORK_TEAM_ROLE_TRACKER);
//        }
//        if (CollectionUtils.isNotEmpty(validaters) && validaters.contains(employeeId)) {
//            roles.add(WorkTeam_.WORK_TEAM_ROLE_VALIDATOR);
//        }

        return roles;
    }

    @Override
    public Map<Long, Set<String>> getRoleInWorkTeams(Long employeeId, List<WorkTeam> teams) {
        if (CollectionUtils.isNotEmpty(teams)) {
            List<Long> ids = new ArrayList<Long>();
            for (WorkTeam wt : teams) {
                ids.add(wt.getId());
            }
            return getRoleInWorkTeam(employeeId, ids);
        }
        return new HashMap<>();
    }

    /**
     * Get employee role set in the Work Team
     *
     * @param employeeId
     * @param teams
     * @return
     */
    @Override
//    @Cacheable(value = CHACHE_NAME, key = "#employee.id+#team.id+'getRoleInWorkTeam'")
    public Map<Long, Set<String>> getRoleInWorkTeam(Long employeeId, List<Long> teams) {
        //TODO 性能优化
        /*Set<String> roles = new HashSet<>();

        //TODO Need cache or change to query by employee id and work team id
        List<Long> archivers = team == null ? null : workTeamRepository.findArchiverById(team.getId());
        List<Long> superviors = team == null ? null : workTeamRepository.findSuperById(team.getId());
        List<Long> dispatchers = team == null ? null : workTeamRepository.findDispatchersById(team.getId());
        List<Long> tracers = team == null ? null : workTeamRepository.findTracersById(team.getId());
        List<Long> validaters = team == null ? null : workTeamRepository.findValidaterById(team.getId());

        if (CollectionUtils.isNotEmpty(archivers) && archivers.contains(employeeId)) {
            roles.add(WorkTeam_.WORK_TEAM_ROLE_ARCHIVER);
        }
        if (CollectionUtils.isNotEmpty(superviors) && superviors.contains(employeeId)) {
            roles.add(WorkTeam_.WORK_TEAM_ROLE_SUPERVISOR);
        }
        if (CollectionUtils.isNotEmpty(dispatchers) && dispatchers.contains(employeeId)) {
            roles.add(WorkTeam_.WORK_TEAM_ROLE_DISPATCHER);
        }
        if (CollectionUtils.isNotEmpty(tracers) && tracers.contains(employeeId)) {
            roles.add(WorkTeam_.WORK_TEAM_ROLE_TRACKER);
        }
        if (CollectionUtils.isNotEmpty(validaters) && validaters.contains(employeeId)) {
            roles.add(WorkTeam_.WORK_TEAM_ROLE_VALIDATOR);
        }*/
        List<WorkTeamRoleView> views = workTeamMapper.findByTeamAndEm(teams, employeeId);
        return WorkTeamRoleView.getRoles(views);
    }

    /**
     * check if employee in the work team
     *
     * @param team
     * @param employee
     * @return
     */
    @Override
    public boolean isEmployeeIncludedIn(Long team, Long employee) {
        if (team == null || employee == null) {
            return false;
        }
        List<WorkTeam> workTeams = workTeamRepository.findByEmployeeIdAndId(employee, team);
        return CollectionUtils.isNotEmpty(workTeams);
    }

    /**
     * convert work team to auto-dispatching vo
     *
     * @param workTeams
     * @return
     */
    @Override
    public List<AutoDispatchingVO> getAutoDispatchingVO(List<WorkTeam> workTeams) {
        if (CollectionUtils.isEmpty(workTeams)) {
            log.debug("work team in getting auto-dispatching is empty, return empty list");
            return Collections.emptyList();
        }

        workTeamBuilder.init(workTeams).addSuper().addMemebers();

        List<AutoDispatchingVO> results = new ArrayList<>();
        for (WorkTeam team : workTeams) {
            AutoDispatchingVO vo = new AutoDispatchingVO();
            vo.setWorkTeam(team);
            //get the first one as director
            vo.setDirector(team.getSupervisors().get(0).getId());

            //find on duty employee
            List<Employee> onDutyEmployee = new ArrayList<>();
            try {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Date now = format.parse(format.format(new Date()));
                onDutyEmployee = schedulingService.findOnDutyEmployees(team.getMembers(), now);
            } catch (ParseException e) {
                log.error("parse date error:", e);
            }

            if (CollectionUtils.isNotEmpty(onDutyEmployee)) {
                for (Employee employee : onDutyEmployee) {
                    vo.getOnDutyAndTaskNumber().put(employee.getId(), workOrderLaborerService.countAssignedWorkOrder(employee));
                }
            }

            onDutyEmployee.addAll(team.getSupervisors());
            for (Employee employee : onDutyEmployee) {
                vo.getOnDutyEmployeeAndDirector().put(employee.getId(), employee);
            }

            results.add(vo);
        }

        return results;
    }

    @Override
    public List<WorkTeam> findAllWorkTeam() {
        return workTeamRepository.findAll();
    }


    /**
     * 为工作组主管人员分配所有权限
     *
     * @param workTeamDTO workTeamDTO
     * @return WorkTeamDTO
     */
    private WorkTeamDTO superManagerRoles(WorkTeamDTO workTeamDTO) {
        Long[] spuerIds = workTeamDTO.getSupervisors();
        //合并数组 有重复
        Long[] trackers = (Long[]) ArrayUtils.addAll(spuerIds, workTeamDTO.getTracers());
        Long[] dispatchers = (Long[]) ArrayUtils.addAll(spuerIds, workTeamDTO.getDispatchers());
        Long[] archivers = (Long[]) ArrayUtils.addAll(spuerIds, workTeamDTO.getArchivers());
        Long[] validaters = (Long[]) ArrayUtils.addAll(spuerIds, workTeamDTO.getValidaters());
        workTeamDTO.setArchivers(archivers);
        workTeamDTO.setDispatchers(dispatchers);
        workTeamDTO.setValidaters(validaters);
        workTeamDTO.setTracers(trackers);
        return workTeamDTO;
    }

    /**
     * 工作组管理层分配人员
     *
     * @param employeeList
     * @return WorKTeam
     */
    private WorkTeam assignEmployees(WorkTeamDTO workTeamDTO, WorkTeam workTeam, List<Employee> employeeList) {
        if (employeeList != null && employeeList.size() > 0) {
            Map<Long, Employee> employeeMap = new HashMap<>();
            for (Employee employee : employeeList) {
                employeeMap.put(employee.getId(), employee);
            }
            //所有人员按照权限分配入工作组 去重
            if (workTeamDTO.getSupervisors() != null && workTeamDTO.getSupervisors().length > 0) {
                Set<Employee> employees = new HashSet<>();
                for (Long id : workTeamDTO.getSupervisors()) {
                    employees.add(employeeMap.get(id));
                }
                workTeam.setSupervisors(new ArrayList<Employee>(employees));
            } else {
                workTeam.setSupervisors(null);
            }
            if (workTeamDTO.getTracers() != null && workTeamDTO.getTracers().length > 0) {
                Set<Employee> employees = new HashSet<>();
                for (Long id : workTeamDTO.getTracers()) {
                    employees.add(employeeMap.get(id));
                }
                workTeam.setTracers(new ArrayList<Employee>(employees));
            } else {
                workTeam.setTracers(null);
            }
            if (workTeamDTO.getDispatchers() != null && workTeamDTO.getDispatchers().length > 0) {
                Set<Employee> employees = new HashSet<>();
                for (Long id : workTeamDTO.getDispatchers()) {
                    employees.add(employeeMap.get(id));
                }
                workTeam.setDispatchers(new ArrayList<Employee>(employees));
            } else {
                workTeam.setDispatchers(null);
            }
            if (workTeamDTO.getArchivers() != null && workTeamDTO.getArchivers().length > 0) {
                Set<Employee> employees = new HashSet<>();
                for (Long id : workTeamDTO.getArchivers()) {
                    employees.add(employeeMap.get(id));
                }
                workTeam.setArchivers(new ArrayList<Employee>(employees));
            } else {
                workTeam.setArchivers(null);
            }
            if (workTeamDTO.getValidaters() != null && workTeamDTO.getValidaters().length > 0) {
                Set<Employee> employees = new HashSet<>();
                for (Long id : workTeamDTO.getValidaters()) {
                    employees.add(employeeMap.get(id));
                }
                workTeam.setValidaters(new ArrayList<Employee>(employees));
            } else {
                workTeam.setValidaters(null);
            }
            if (workTeamDTO.getInspectors() != null && workTeamDTO.getInspectors().length > 0) {
                Set<Employee> employees = new HashSet<>();
                for (Long id : workTeamDTO.getInspectors()) {
                    if (employeeRepository.findOne(id) != null) {
                        employees.add(employeeRepository.findOne(id));
                    }
                }
                workTeam.setInspectors(new ArrayList<Employee>(employees));
            } else {
                workTeam.setInspectors(null);
            }
            workTeam.setMembers(new ArrayList<Employee>(employeeList));
        }
        return workTeam;
    }

    /**
     * 根据DTO获得工作组管理层的所有人员
     *
     * @return List<Employee>
     */
    private List<Employee> findManagementEmployees(WorkTeamDTO workTeamDTO) {
        Set<Long> employeeIds = new HashSet<>();
        List<Employee> employeeResult = new ArrayList<>();
        if (workTeamDTO.getSupervisors() != null && workTeamDTO.getSupervisors().length > 0) {
            List<Long> supervisors = Arrays.asList(workTeamDTO.getSupervisors());
            employeeIds.addAll(new HashSet<Long>(supervisors));
        }
        if (workTeamDTO.getDispatchers() != null && workTeamDTO.getDispatchers().length > 0) {
            List<Long> dispatchers = Arrays.asList(workTeamDTO.getDispatchers());
            employeeIds.addAll(new HashSet<Long>(dispatchers));
        }
        if (workTeamDTO.getTracers() != null && workTeamDTO.getTracers().length > 0) {
            List<Long> tracers = Arrays.asList(workTeamDTO.getTracers());
            employeeIds.addAll(new HashSet<Long>(tracers));
        }
        if (workTeamDTO.getArchivers() != null && workTeamDTO.getArchivers().length > 0) {
            List<Long> archivers = Arrays.asList(workTeamDTO.getArchivers());
            employeeIds.addAll(new HashSet<Long>(archivers));
        }
        if (workTeamDTO.getValidaters() != null && workTeamDTO.getValidaters().length > 0) {
            List<Long> validaters = Arrays.asList(workTeamDTO.getValidaters());
            employeeIds.addAll(new HashSet<Long>(validaters));
        }
        if (employeeIds != null && employeeIds.size() > 0) {
            employeeResult = employeeRepository.findByIdIn(new HashSet<Long>(employeeIds));
        }
        return employeeResult;
    }

    /**
     * 添加员工对工作的维护关系 WorkTeam mappedBy=Employee
     *
     * @return
     */
    private void addMembersRelationship(WorkTeam workTeam, List<Employee> employeeList) {
        //解决懒加载问题，在查一遍
        if (employeeList != null && employeeList.size() > 0) {
            List<Long> emIds = new ArrayList<>();
            for (Employee employee : employeeList) {
                Long id = employee.getId();
                emIds.add(id);
            }
            employeeList = employeeRepository.findNotSignEmpById(emIds);
        }
        for (Employee employee : employeeList) {
            List<WorkTeam> workTeamList = employee.getWorkTeams();
            if (workTeamList != null && workTeamList.size() > 0) {
                Set<WorkTeam> workTeamSet = new HashSet<>(workTeamList);
                workTeamSet.add(workTeam);
                employee.setWorkTeams(new ArrayList<WorkTeam>(workTeamSet));
            } else {
                workTeamList.add(workTeam);
                employee.setWorkTeams(workTeamList);
            }
        }
        employeeRepository.save(employeeList);
    }

    @Override
    public Boolean verificationSLA(Long woTeamId) {
        Long count = workOrderProcessRepository.findWoProcessByWoTeamId(woTeamId, WorkOrder.WorkOrderType.PPM);
        if (count > 0) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean verificationPPM(Long woTeamId) {
        List<PreventiveMaintenance> steps = pmStepRepository.findByWoTeamId(woTeamId);
        if (CollectionUtils.isNotEmpty(steps)) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean verificationPatrol(Long woTeamId) {
        Long count = patrolRepository.findByWoTeamId(woTeamId);
        if (count > 0) {
            return false;
        }
        return true;
    }

    @Override
    public Map<Long, WorkTeam> findByIds(Set<Long> workTeamIds) {
        Map<Long, WorkTeam> res = new HashMap<>();
        if (CollectionUtils.isNotEmpty(workTeamIds)) {
            List<WorkTeam> ems = workTeamRepository.findByIdIn(new ArrayList<>(workTeamIds));
            for (WorkTeam em : ems) {
                res.put(em.getId(), em);
            }
        }
        return res;
    }

    @Override
    public List<Employee> getWorkTeamMembers(WorkTeam workTeam, Boolean type) {
        Set<Employee> members = new HashSet<>();
        //所有员工
        Set<Employee> allMembers = new HashSet<>(workTeam.getMembers());
        //主管、排程派工、追踪、验证、存档的员工
        Set<Employee> others = new HashSet<>();
        others.addAll(workTeam.getSupervisors());
        others.addAll(workTeam.getDispatchers());
        others.addAll(workTeam.getTracers());
        others.addAll(workTeam.getValidaters());
        others.addAll(workTeam.getArchivers());
        if (!type) {
            return new ArrayList<>(others);
        }
        for (Employee employee : allMembers) {
            if (!others.contains(employee) && employee.isActivated()) {
                members.add(employee);
            }
        }
        return new ArrayList<>(members);
    }

    @Override
    public Boolean verificationReqType(Long workTeamId) {
        Long count = workTeamMapper.findWorkTeamByReqTypeWorkTeam(ProjectContext.getCurrentProject(), workTeamId);
        if (count > 0) {
            return false;
        }
        return true;
    }

    @Override
    public Result addDownloadWorkTeam(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            List<WorkTeam> workTeams = workTeamRepository.findByIdInHardly(ids, 0L);
            List<WorkTeam> all = workTeamRepository.findAll();
            Boolean sameNameFlag = false;
            for (WorkTeam workTeam : workTeams) {
                for (WorkTeam wt : all) {
                    if (wt.getName().equals(workTeam.getName())) {
                        sameNameFlag = true;
                    }
                }
            }
            if (sameNameFlag) {
                return new Result(Boolean.FALSE);
            }

            List<WorkTeam> workTeamList = new ArrayList<>();
            for (WorkTeam workTeam : workTeams) {
                WorkTeam team = new WorkTeam();
                team.setName(workTeam.getName());
                team.setDescription(workTeam.getDescription());
                team.setActivated(Boolean.FALSE);
                workTeamList.add(team);
            }
            workTeamRepository.save(workTeamList);
            return new Result(XiaMesssageResource.getMessage("server.result.success.download", null, ""));
        } else {
            return new Result(Boolean.TRUE);
        }
    }

    @Override
    public List<Long> getInspectorWorkTeam(Long empId) {
        List<WorkTeam> workTeamList = workTeamRepository.isEmployeeInspector(empId);
        List<Long> workTeamIds = workTeamList.stream().map(WorkTeam::getId).collect(Collectors.toList());

        return workTeamIds;
    }

    @Override
    public List<Long> getSupervisorWorkTeam(Long empId) {
        List<WorkTeam> workTeamList = workTeamRepository.isEmployeeASupervisor(empId);
        List<Long> workTeamIds = workTeamList.stream().map(WorkTeam::getId).collect(Collectors.toList());

        return workTeamIds;
    }
}
