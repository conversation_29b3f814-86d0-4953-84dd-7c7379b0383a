#============================================================================
# Configure Main Scheduler Properties  
#============================================================================
org.quartz.scheduler.instanceName = Scheduler
org.quartz.scheduler.instanceId = AUTO
org.quartz.scheduler.skipUpdateCheck = true
org.quartz.scheduler.classLoadHelper.class=org.quartz.simpl.CascadingClassLoadHelper
#org.quartz.scheduler.batchTriggerAcquisitionMaxCount = 15
#org.quartz.scheduler.batchTriggerAcquisitionFireAheadTimeWindow = 60000

#============================================================================
# Configure ThreadPool  
#============================================================================
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount = 15
org.quartz.threadPool.threadPriority = 8

#============================================================================
# Configure the Job Initialization Plugin
#============================================================================
org.quartz.plugin.triggHistory.class = org.quartz.plugins.history.LoggingJobHistoryPlugin

#============================================================================
# Configure the jobStore
#============================================================================
org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore
org.quartz.jobStore.misfireThreshold = 60000
#org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreCMT
#org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate
#org.quartz.jobStore.dataSource = xaDataSource
#org.quartz.jobStore.nonManagedTXDataSource=xaDataSource

#============================================================================
# Configure the dataSource
#============================================================================
#org.quartz.dataSource.myCustomDS.connectionProvider.class = cn.facilityone.sa.quartz.config.QuartzDataSource
#org.quartz.dataSource.xaDataSource.driver=com.mysql.jdbc.Driver
#org.quartz.dataSource.xaDataSource.URL=******************************************************************************************************************
#org.quartz.dataSource.xaDataSource.user=root
#org.quartz.dataSource.xaDataSource.password=root

#============================================================================  
# Configure the Job Initialization Plugin  
#============================================================================  
org.quartz.plugin.jobInitializer.class = org.quartz.plugins.xml.XMLSchedulingDataProcessorPlugin  
org.quartz.plugin.jobInitializer.fileNames = init/quartz_data_jobs.xml
org.quartz.plugin.jobInitializer.failOnFileNotFound = true  
org.quartz.plugin.jobInitializer.scanInterval = 3600  
org.quartz.plugin.jobInitializer.wrapInUserTransaction = false
#org.quartz.plugin.jobInitializer.overWriteExistingJobs = false
