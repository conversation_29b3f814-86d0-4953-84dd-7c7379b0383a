-- -------------------------------------------------------------------------------------------------
-- spring.datasource.initialize=true  初次安装时开启，再次启动需要设置为false
-- -------------------------------------------------------------------------------------------------
-- ----------------------------
-- idseq
-- ----------------------------
update idseq set next_val = 10000;

-- ----------------------------
-- hibernate_sequences 自增主键，初始值
-- ----------------------------
insert into hibernate_sequences(sequence_name,next_val) VALUES('sys_user',20);
insert into hibernate_sequences(sequence_name,next_val) VALUES('em',20);
insert into hibernate_sequences(sequence_name,next_val) VALUES('sys_role',20);
insert into hibernate_sequences(sequence_name,next_val) VALUES('unit',300);
insert into hibernate_sequences(sequence_name,next_val) VALUES('sys_project_group',20);
insert into hibernate_sequences(sequence_name,next_val) VALUES('sys_project',20);
insert into hibernate_sequences(sequence_name,next_val) VALUES('geo_site',20);
insert into hibernate_sequences(sequence_name,next_val) VALUES('requirement_type',20);
insert into hibernate_sequences(sequence_name,next_val) VALUES('message_template',100);
insert into hibernate_sequences(sequence_name,next_val) VALUES('approval_template',100);
-- ----------------------------
-- sys_security_client
-- ----------------------------
insert into sys_security_client(client_id,client_name,description,secret) VALUES('00000000','fm','','11111111');

-- ----------------------------
-- config_system
-- ----------------------------
-- insert into config_system(ms_id,version,deleted,system_zhname,system_enname) VALUES(1,0,b'0','F-ONE 设施服务管理软件','F-ONE Manage System');

-- ----------------------------
-- sys_project_group
-- ----------------------------
insert into sys_project_group(sys_project_group_id,version,deleted,description,group_name) VALUES(1,0,b'0','','默认项目组');

-- ----------------------------
-- sys_project     保持与geo_site 同步
-- ----------------------------
insert into sys_project(proj_id,version,deleted,proj_name,proj_type,sys_project_group_id,code) VALUES(1,0,b'0','默认项目','',1,'01');

-- ----------------------------
-- geo_site          保持与sys_project 同步
-- ----------------------------
insert into geo_site(site_id,version,deleted,site_code,site_name,sort,proj_id) VALUES(1,0,b'0','','默认项目',1,1);


insert into approval_template (`approval_template_id`, `version`, `created_by`, `created_date`, `modified_by`, `modified_date`, `deleted`, `description`, `step_last`, `name`, `step_next`, `type`, `proj_id`) values ( '1', '0', null, '2018-11-07 10:29:30', null, '2018-11-07 10:29:30', b'1', 'This is only for ppm approval and should be deleted once created!', null, '计划性维护审核', null, '0', '-1');
-- ----------------------------
-- sys_user  151009
-- f1auadmin f12018auadmin
-- ----------------------------
insert into sys_user(id,version,activated,email,password,real_name,user_name,`type`,deleted,proj_ids) VALUES(1,0,b'1','<EMAIL>','645a0f4a7316709365652f1135c48746652b484bf827b324','系统管理员','admin',1,0,',0,');
insert into sys_user(id,version,activated,email,password,real_name,user_name,`type`,deleted,proj_ids) VALUES(20,0,b'1','<EMAIL>','c8ad950ed33bbfbb74c0bc38ca0aab6f89775bda9dce3180','系统管理员','f1auadmin',1,0,',0,');
-- ----------------------------
-- em
-- ----------------------------
insert into em(em_id,version,activated,em_name,user_id,`type`,proj_id,deleted,phone) VALUES(1,0,b'1','admin',1,'0',0,0,'15206186877');

-- ----------------------------
-- sys_role
-- ----------------------------
-- insert into sys_role(id,version,description,name) VALUES(1,0,'管理系统','系统管理员');

-- ----------------------------
-- sys_user_role
-- ----------------------------
-- insert into sys_user_role(user_id,role_id) VALUES(1,1);

-- ----------------------------
-- sys_module
-- ----------------------------
insert into sys_module(id,version,description,name) VALUES(1,0,'','运维');

-- RequirementType
INSERT INTO `requirement_type` (reqtype_id,version,deleted,gen_wo,full_name,reqtype_name,description,wc_type,sort,proj_id,can_deleted) VALUES ('1', '0', b'0', b'0', '网络报修' ,'网络报修','专门用于网络端的报障' ,'0' ,2,'1',b'0');
INSERT INTO `requirement_type` (reqtype_id,version,deleted,gen_wo,full_name,reqtype_name,description,wc_type,sort,proj_id,can_deleted) VALUES ('2', '0', b'0', b'0', '微信咨询' ,'微信咨询','专门用于微信端的咨询' ,'1',3,'1',b'1');
INSERT INTO `requirement_type` (reqtype_id,version,deleted,gen_wo,full_name,reqtype_name,description,wc_type,sort,proj_id,can_deleted) VALUES ('3', '0', b'0', b'0', '微信投诉' ,'微信投诉','专门用于微信端的投诉' ,'2',1,'1',b'1');

-- ----------------------------
-- sys_menu
-- ----------------------------

-- Home
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('100', '0',b'0' , 's-menu-home', b'1', '/chart/dashboard/index', 'dashboard', '0', '_self', 'pc', b'0', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('110', '0',b'0' , 's-menu-home', b'1', '/chart/dashboard/index_new', 'dashboard', '0', '_self', 'pc', b'1', '1', null);


-- menu usercenter 7
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('1', '0',b'0' , 's-menu-user', b'1', '', 'ucenter', '1000', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('1100', '0',b'0', '', b'1', '/uc001', 'ucenter.profile', '100', '_self', 'pc', b'1', '1', '1');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('1200', '0',b'0', '', b'1', '/uc002', 'ucenter.message', '200', '_self', 'pc', b'1', '1', '1');

-- menu servicecenter 1
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('2', '0',b'0', 's-menu-service', b'0', '', 'servercenter', '100', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('2100', '0',b'0', '', b'0', '/service001', 'servercenter.service', '100', '_self', 'pc', b'1', '1', '2');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('2200', '0',b'0', '', b'0', '/service002', 'servercenter.search', '200', '_self', 'pc', b'1', '1', '2');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('2300', '0',b'0', '', b'0', '/service003', 'servercenter.type', '300', '_self', 'pc', b'1', '1', '2');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('2400', '0',b'0', '', b'0', '/service004', 'servercenter.message', '400', '_self', 'pc', b'1', '1','2');

-- menu workorder 2
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3', '0',b'0', 's-menu-workorder', b'0', '', 'workorder', '200', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3100', '0',b'0', '', b'0', '/wo001', 'workorder.management', '100', '_self', 'pc', b'1', '1', '3');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3200', '0',b'0', '', b'0', '/wo002', 'workorder.search', '300', '_self', 'pc', b'1', '1', '3');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3900', '0',b'0', '', b'0', '/wo009', 'workorder.approval', '200', '_self', 'pc', b'1', '1', '3');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3310', '0',b'0', '', b'0', '', 'workorder.Basics', '400', '_self', 'pc', b'1', '1', '3');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3300', '0',b'0', '', b'0', '/wo003', 'workorder.Basics.sla', '400', '_self', 'pc', b'1', '1', '3310');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3400', '0',b'0', '', b'0', '/wo004', 'workorder.Basics.eatemplate', '500', '_self', 'pc', b'1', '1', '3310');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3500', '0',b'0', '', b'0', '/wo005', 'workorder.Basics.scheduling', '600', '_self', 'pc', b'1', '1', '3310');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3600', '0',b'0', '', b'0', '/wo006', 'workorder.Basics.servicetype', '100', '_self', 'pc', b'1', '1', '3310');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3700', '0',b'0', '', b'0', '/wo007', 'workorder.Basics.priority', '200', '_self', 'pc', b'1', '1', '3310');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('3800', '0',b'0', '', b'0', '/wo008', 'workorder.Basics.satisfaction', '300', '_self', 'pc', b'1', '1', '3310');

-- menu preventive 4
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('4', '0',b'0', 's-menu-preventive', b'0', '', 'preventive', '400', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('4100', '0',b'0', '', b'0', '/pm001', 'preventive.setting', '300', '_self', 'pc', b'1', '1', '4');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('4200', '0',b'0', '', b'0', '/pm002', 'preventive.workOrder', '400', '_self', 'pc', b'1', '1', '4');

-- menu patrol 3
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('5', '0',b'0', 's-menu-patrol', b'0', '', 'patrol', '300', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('5100', '0',b'0', '', b'0', '/par001', 'patrol.spot.set', '200', '_self', 'pc', b'1', '1', '5');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('5200', '0',b'0', '', b'0', '/par002', 'patrol.set', '300', '_self', 'pc', b'1', '1', '5');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('5300', '0',b'0', '', b'0', '/par003', 'patrol.record', '400', '_self', 'pc', b'1', '1', '5');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('5400', '0',b'0', '', b'0', '/par004', 'patrol.content.module', '100', '_self', 'pc', b'1', '1', '5');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('5500', '0',b'0', '', b'0', '/par005', 'patrol.report', '500', '_self', 'pc', b'0', '1', '5');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('5600', '0',b'0', '', b'0', '/par006', 'patrol.holiday', '600', '_self', 'pc', b'1', '1', '5');

-- menu inventory
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6', '0',b'0', 's-menu-inventory', b'0', '', 'inventory', '600', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6100', '0',b'0', '', b'0', '/stock001', 'stock.warehouse', '100', '_self', 'pc', b'1', '1', '6');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6200', '0',b'0', '', b'0', '/stock002', 'stock.material', '200', '_self', 'pc', b'1', '1', '6');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6300', '0',b'0', '', b'0', '', 'stock.inventory', '300', '_self', 'pc', b'1', '1',  '6');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6310', '0',b'0', '', b'0', '/stock031', 'stock.material.in', '310', '_self', 'pc', b'1', '1', '6300');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6320', '0',b'0', '', b'0', '/stock032', 'stock.material.out', '320', '_self', 'pc', b'1', '1', '6300');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6330', '0',b'0', '', b'0', '/stock033', 'stock.material.back', '330', '_self', 'pc', b'1', '1', '6300');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6340', '0',b'0', '', b'0', '/stock034', 'stock.material.move', '340', '_self', 'pc', b'1', '1', '6300');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6350', '0',b'0', '', b'0', '/stock035', 'stock.material.reserve', '350', '_self', 'pc', b'1', '1', '6300');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6360', '0',b'0', '', b'0', '/stock036', 'stock.reserve.management', '360', '_self', 'pc', b'1', '1', '6300');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6370', '0',b'0', '', b'0', '/stock037', 'stock.my.reservation', '370', '_self', 'pc', b'1', '1', '6300');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6400', '0',b'0', '', b'0', '/stock004', 'stock.activity', '400', '_self', 'pc', b'1', '1', '6');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6500', '0',b'0', '', b'0', '/stock005', 'stock.inventory.verification', '500', '_self', 'pc', b'1', '1', '6');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6600', '0',b'0', '', b'0', '/stock006', 'stock.minimum', '600', '_self', 'pc', b'1', '1', '6');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('6700', '0',b'0', '', b'0', '/stock007', 'stock.order.query', '700', '_self', 'pc', b'1', '1', '6');

-- menu mobile
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7', '0',null, 's-menu-mobile', b'0', '', 'mobile', '5', '_self', 'mobile', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7010', '0',b'0', '', b'0', '', 'm-wo', '100', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7011', '0',b'0', '', b'0', '', 'm-wo-process', '100', '_self', 'mobile', b'1', '1', 7010);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7012', '0',b'0', '', b'0', '', 'm-wo-dispach', '200', '_self', 'mobile', b'1', '1', 7010);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7013', '0',b'0', '', b'0', '', 'm-wo-approval', '300', '_self', 'mobile', b'1', '1', 7010);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7014', '0',b'0', '', b'0', '', 'm-wo-close', '400', '_self', 'mobile', b'1', '1', 7010);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7015', '0',b'0', '', b'0', '', 'm-wo-query', '500', '_self', 'mobile', b'1', '1', 7010);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7020', '0',b'0', '', b'0', '', 'm-patrol', '200', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7021', '0',b'0', '', b'0', '', 'm-patrol-task', '100', '_self', 'mobile', b'1', '1', 7020);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7022', '0',b'0', '', b'0', '', 'm-patrol-query', '200', '_self', 'mobile', b'1', '1', 7020);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7030', '0',b'0', '', b'0', '', 'm-requirement', '300', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7031', '0',b'0', '', b'0', '', 'm-requirement-create', '100', '_self', 'mobile', b'1', '1', 7030);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7032', '0',b'0', '', b'0', '', 'm-requirement-process', '200', '_self', 'mobile', b'1', '1', 7030);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7033', '0',b'0', '', b'0', '', 'm-requirement-approval', '300', '_self', 'mobile', b'1', '1', 7030);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7034', '0',b'0', '', b'0', '', 'm-requirement-evaluate', '400', '_self', 'mobile', b'1', '1', 7030);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7035', '0',b'0', '', b'0', '', 'm-requirement-query', '500', '_self', 'mobile', b'1', '1', 7030);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7040', '0',b'0', '', b'0', '', 'm-ppm', '400', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7050', '0',b'0', '', b'0', '', 'm-asset', '500', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7060', '0',b'0', '', b'0', '', 'm-inventory', '600', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7061', '0',b'0', '', b'0', '', 'm-inventory-in', '100', '_self', 'mobile', b'1', '1', 7060);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7062', '0',b'0', '', b'0', '', 'm-inventory-out', '200', '_self', 'mobile', b'1', '1', 7060);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7063', '0',b'0', '', b'0', '', 'm-inventory-move', '300', '_self', 'mobile', b'1', '1', 7060);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7064', '0',b'0', '', b'0', '', 'm-inventory-check', '400', '_self', 'mobile', b'1', '1', 7060);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7065', '0',b'0', '', b'0', '', 'm-inventory-reserve', '500', '_self', 'mobile', b'1', '1', 7060);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7066', '0',b'0', '', b'0', '', 'm-inventory-approval', '600', '_self', 'mobile', b'1', '1', 7060);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7067', '0',b'0', '', b'0', '', 'm-inventory-query', '700', '_self', 'mobile', b'1', '1', 7060);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7068', '0',b'0', '', b'0', '', 'm-inventory-my', '800', '_self', 'mobile', b'1', '1', 7060);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7069', '0',b'0', '', b'0', '', 'm-inventory-create', '9', '_self', 'mobile', b'1', '1', 7060);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7070', '0',b'0', '', b'0', '', 'm-energy', '700', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7080', '0',b'0', '', b'0', '', 'm-sign', '800', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7090', '0',b'0', '', b'0', '', 'm-contract', '900', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7091', '0',b'0', '', b'0', '', 'm-contract-process', '100', '_self', 'mobile', b'1', '1', 7090);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7092', '0',b'0', '', b'0', '', 'm-contract-query', '200', '_self', 'mobile', b'1', '1', 7090);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7100', '0',b'0', '', b'0', '', 'm-bulletin', '1000', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7130', '0',b'0', '', b'0', '', 'm-visitor', '130', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7131', '0',b'0', '', b'0', '', 'm-visitor-create', '100', '_self', 'mobile', b'1', '1', 7130);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7132', '0',b'0', '', b'0', '', 'm-visitor-query', '200', '_self', 'mobile', b'1', '1', 7130);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7110', '0',b'0', '', b'0', '', 'm-chart', '1100', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7120', '0',b'0', '', b'0', '', 'm-payment', '120', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7140', '0',b'0', '', b'0', '', 'm-monitoring-operational', '1400', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7160', '0',b'0', '', b'0', '', 'm-inspection', '150', '_self', 'mobile', b'1', '1', 7);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7161', '0',b'0', '', b'0', '', 'm-inspection-task', '1', '_self', 'mobile', b'1', '1', 7160);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7162', '0',b'0', '', b'0', '', 'm-inspection-archive', '2', '_self', 'mobile', b'1', '1', 7160);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('7163', '0',b'0', '', b'0', '', 'm-inspection-query', '3', '_self', 'mobile', b'1', '1', 7160);

-- menu report
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8', '0',b'0', 's-menu-report', b'0', '', 'report', '700', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8100', '0',b'0', '', b'0', '', 'report.requirement', '100', '_self', 'pc', b'1', '1', '8');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8110', '0',b'0', '', b'0', '/report010', 'report.requirement.summary', '100', '_self', 'pc', b'1', '1','8100');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8120', '0',b'0', '', b'0', '/report011', 'report.requirement.mount.statistics', '200', '_self', 'pc', b'1', '1','8100');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8130', '0',b'0', '', b'0', '/report012', 'report.requirement.type.statistics', '300', '_self', 'pc', b'1', '1','8100');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8140', '0',b'0', '', b'0', '/report013', 'report.requirement.satisfaction.statistics', '400', '_self', 'pc', b'1', '1','8100');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8150', '0',b'0', '', b'0', '/report014', 'report.requirement.efficiency', '500', '_self', 'pc', b'1', '1','8100');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8200', '0',b'0', '', b'0', '', 'report.workorder', '200', '_self', 'pc', b'1', '1', '8');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8210', '0',b'0', '', b'0', '/report020', 'report.wo.summary', '100', '_self', 'pc', b'1', '1', '8200');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8220', '0',b'0', '', b'0', '/report021', 'report.workorder.mount.statistics', '200', '_self', 'pc', b'1', '1','8200');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8230', '0',b'0', '', b'0', '/report022', 'report.wo.type', '300', '_self', 'pc', b'1', '1','8200');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8240', '0',b'0', '', b'0', '/report023', 'report.wo.timelyRate', '400', '_self', 'pc', b'1', '1','8200');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8250', '0',b'0', '', b'0', '/report024', 'report.wo.efficiency', '500', '_self', 'pc', b'1', '1','8200');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8260', '0',b'0', '', b'0', '/report025', 'report.wo.kpi', '600', '_self', 'pc', b'1','1','8200');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8300', '0',b'0', '', b'0', '', 'report.patrol', '300', '_self', 'pc', b'1', '1', '8');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8310', '0',b'0', '', b'0', '/report031', 'report.patrol.total', '100', '_self', 'pc', b'1', '1','8300');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8320', '0',b'0', '', b'0', '/report032', 'report.patrol.statistics', '200', '_self', 'pc', b'1','1','8300');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8330', '0',b'0', '', b'0', '/report033', 'report.patrol.efficiency', '300', '_self', 'pc', b'1','1','8300');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8400', '0',b'0', '', b'0', '/report041', 'report.pm.statistics', '400', '_self', 'pc', b'1', '1','8');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8500', '0',b'0', '', b'0', '', 'report.equipment', '500', '_self', 'pc', b'1', '1', '8');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8510', '0',b'0', '', b'0', '/report051', 'report.statistics', '100', '_self', 'pc', b'1', '1', '8500');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8520', '0',b'0', '', b'0', '/report052', 'report.equipmentOrder', '200', '_self', 'pc', b'1', '1', '8500');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('8600', '0',b'0', '', b'0', '/report006', 'report.month', '600', '_self', 'pc', b'1', '1', '8');

-- menu knowledge
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('9', '0',b'1', 's-menu-knowledge', b'1', '', 'knowledge', '800', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('9110', '0',b'0', '', b'0', '/knowledge001', 'knowledge.classificationmanagement', '100', '_self', 'pc', b'1', '1', '9');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('9120', '0',b'0', '', b'0', '/knowledge002', 'knowledge.docmanagement', '200', '_self', 'pc', b'1', '1', '9');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('9130', '0',b'1', '', b'0', '/knowledge003', 'knowledge.classificationmanagement.proj', '300', '_self', 'pc', b'1', '1', '9');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('9140', '0',b'1', '', b'0', '/knowledge004', 'knowledge.docmanagement.proj', '400', '_self', 'pc', b'1', '1', '9');

-- menu organization 6
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('10', '0',b'0', 's-menu-organize', b'0', '', 'organization', '900', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('10100', '0',b'0', '', b'0', '/org001/workteams', 'organization.workteam', '500', '_self', 'pc', b'1', '1', '10');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('10200', '0',b'0', '', b'0', '/org002/employees', 'organization.employee', '400', '_self', 'pc', b'1', '1', '10');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('10300', '0',b'0', '', b'0', '/org003', 'organization.organization', '200', '_self', 'pc', b'1', '1', '10');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('10400', '0',b'0', '', b'0', '/org004/places', 'organization.place', '100', '_self', 'pc', b'1', '1', '10');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('10500', '0',b'0', '', b'0', '/org005', 'organization.position', '300', '_self', 'pc', b'1', '1', '10');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('10600', '0',b'0', '', b'0', '/org006', 'organization.customer', '600', '_self', 'pc', b'1', '1', '10');

-- menu system 8
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('11', '0',b'0', 's-menu-system', b'0', '', 'system', '950', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('11100', '0',b'0', '', b'0', '/sys001', 'system.user', '100', '_self', 'pc', b'1', '1', '11');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('11200', '0',b'0', '', b'0', '/sys002', 'system.role', '200', '_self', 'pc', b'1', '1', '11');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('11300', '0',b'0', '', b'0', '/sys003', 'system.permission', '300', '_self', 'pc', b'1', '1', '11');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('11400', '0',b'0', '', b'0', '/sys004', 'system.messagerecord', '500', '_self', 'pc', b'1', '1', '11');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('11500', '0',b'0', '', b'0', '/sys005', 'system.configuration', '600', '_self', 'pc', b'1', '1', '11');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('11600', '0',b'0', '', b'0', '/sys006', 'system.messageTemplate', '400', '_self', 'pc', b'1', '1', '11');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('11700', '0',b'0', '', b'0', '/common/quartz', 'common.quartz', '700', '_self', 'pc', b'0', '1', '11');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('11800', '0',b'0', '', b'0', '/sys007', 'system.import', '800', '_self', 'pc', b'1', '1', '11');

-- menu asset 5
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('12', '0',b'0', 's-menu-asset', b'0', '', 'asset', '500', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('12100', '0',b'0', '', b'0', '/asset001/equipmentsystems', 'asset.equipmentSystem', '100', '_self', 'pc', b'1', '1', '12');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('12200', '0',b'0', '', b'0', '/asset002/equipments', 'asset.equipment', '200', '_self', 'pc', b'1', '1', '12');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('12300', '0',b'0', '', b'0', '/asset003', 'asset.contractExpire', '300', '_self', 'pc', b'1', '1', '12');

-- menu project
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('13', '0',b'1', 'fa-th-large', b'0', '', 'project', '100', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('13100', '0',b'1', '', b'0', '/pro001', 'project.group', '100', '_self', 'pc', b'1', '1', 13);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('13200', '0',b'1', '', b'0', '/pro002', 'project.project', '200', '_self', 'pc', b'1', '1', 13);

-- menu energy
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('14', '0',b'0', 's-menu-energy', b'0', '', 'energy', '650', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('14100', '0',b'0', '', b'0', '/ene001', 'energy.manage', '100', '_self', 'pc', b'1', '1', 14);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('14200', '0',b'0', '', b'0', '/ene002', 'energy.search', '200', '_self', 'pc', b'1', '1', 14);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('14300', '0',b'0', '', b'0', '/ene003', 'energy.report', '300', '_self', 'pc', b'1', '1', 14);

-- menu sign
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('16', '0',b'0', 's-menu-sign', b'0', '', 'sign', '250', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('16100', '0',b'0', '', b'0', '/sign001', 'sign.signedIn', '100', '_self', 'pc', b'1', '1', 16);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('16200', '0',b'0', '', b'0', '/sign002', 'sign.record', '200', '_self', 'pc', b'1', '1', 16);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('16300', '0',b'0', '', b'0', '', 'sign.set', '300', '_self', 'pc', b'1', '1', 16);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('16310', '0',b'0', '', b'0', '/sign003', 'sign.employee', '100', '_self', 'pc', b'1', '1', 16300);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('16320', '0',b'0', '', b'0', '/sign004', 'sign.mode', '200', '_self', 'pc', b'1', '1', 16300);

-- menu bulletin
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('17', '0', b'1', 's-menu-bulletin', b'0', '', 'bulletin', '50', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('17100', '0',b'0', '', b'0', '/bul001', 'bulletin.create', '100', '_self', 'pc', b'1', '1', '17');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('17200', '0',b'0', '', b'0', '/bul002', 'bulletin.history', '300', '_self', 'pc', b'1', '1', '17');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('17300', '0',b'0', '', b'1', '/bul003', 'bulletin.mine', '500', '_self', 'pc', b'1', '1', '1');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('17400', '0',b'1', '', b'0', '/bul001', 'bulletin.create.proj', '200', '_self', 'pc', b'1', '1', '17');
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('17500', '0',b'1', '', b'0', '/bul002', 'bulletin.history.proj', '400', '_self', 'pc', b'1', '1', '17');

-- menu contract
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('18', '0',b'0', 's-menu-contract', b'0', '', 'contract', '610', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('18100', '0',b'0', '', b'0', '', 'contract.set', '100', '_self', 'pc', b'1', '1', 18);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('18110', '0',b'0', '', b'0', '/contract001', 'contract.type', '100', '_self', 'pc', b'1', '1', 18100);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('18120', '0',b'0', '', b'0', '/contract002', 'contract.other', '200', '_self', 'pc', b'1', '1', 18100);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('18200', '0',b'0', '', b'0', '/contract003', 'contract.manage', '200', '_self', 'pc', b'1', '1', 18);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('18300', '0',b'0', '', b'0', '/contract004', 'contract.remind', '300', '_self', 'pc', b'1', '1', 18);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('18400', '0',b'0', '', b'0', '/contract005', 'contract.statistic', '400', '_self', 'pc', b'1', '1', 18);

-- menu vendor
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('19', '0',b'0', 's-menu-vendor', b'0', '', 'vendor', '620', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('19100', '0',b'0', '', b'0', '/vendor001', 'vendor.manage', '100', '_self', 'pc', b'1', '1', 19);

-- menu Visitor Management
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('23', '0',b'0' , 's-menu-visitor', b'0', '', 'visitor', '20', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('23100', '0',b'0', '', b'0', '/visitor001', 'visitor.register', '100', '_self', 'pc', b'1', '1', 23);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('23200', '0',b'0', '', b'0', '/visitor002', 'visitor.record', '200', '_self', 'pc', b'1', '1', 23);

-- menu operational monitoring
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('21110', '0', b'0', '', b'0', '/monitoring001', 'asset.monitoring.sync', '2', '_self', 'pc', b'1', '1', 12);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('21120', '0', b'0', '', b'0', '/monitoring002', 'system.equipment', '920', '_self', 'pc', b'1', '1', 11);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('21310', '0', b'0', '', b'0', '/monitoring004', 'monitoring.realtime', '5', '_self', 'pc', b'1', '1', 12);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('21200', '0', b'0', '', b'0', '/monitoring003', 'monitoring.record', '6', '_self', 'pc', b'1', '1', 12);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('21300', '0', b'0', '', b'0', '', 'monitoring.operation.statistics', '8', '_self', 'pc', b'1', '1', 12);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('21330', '0', b'0', '', b'0', '/monitoring006', 'monitoring.energy.statistics', '3', '_self', 'pc', b'1', '1', 21300);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('21340', '0', b'0', '', b'0', '/monitoring007', 'monitoring.running.statistics', '4', '_self', 'pc', b'1', '1', 21300);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('21350', '0', b'0', '', b'0', '/monitoring008', 'monitoring.equipment.statistics', '5', '_self', 'pc', b'1', '1', 21300);

-- menu business statistic
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('33', '0', b'1', 's-menu-report', b'0', '', 'businessstatistic', '900', '_self', 'pc', b'1', '1', NULL );
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('33601', '0', b'1', '', b'0', '/bus001', 'businessstatistic.req', '10', '_self', 'pc', b'1', '1', '33' );
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('33602', '0', b'1', '', b'0', '/bus002', 'businessstatistic.wo', '20', '_self', 'pc', b'1', '1', '33' );
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('33603', '0', b'1', '', b'0', '/bus003', 'businessstatistic.patrol', '30', '_self', 'pc', b'1', '1', '33' );
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('33604', '0', b'1', '', b'0', '/bus004', 'businessstatistic.ppm', '40', '_self', 'pc', b'1', '1', '33' );
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('33605', '0', b'1', '', b'0', '/bus005', 'businessstatistic.eq', '50', '_self', 'pc', b'1', '1', '33' );
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('33600', '0', b'1', '', b'0', '/bus006', 'businessstatistic.month', '60', '_self', 'pc', b'1', '1', '33' );

-- menu undertake
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('34', '0',b'0', 's-menu-undertake', b'0', '', 'undertake', '450', '_self', 'pc', b'1', '1', null);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('24100', '0',b'0', '', b'0', '/under001', 'undertake.task', '100', '_self', 'pc', b'1', '1', 34);
INSERT INTO `sys_menu` (id,version,is_project,icon,is_all,link,`name`,order_number,target,`type`,visible,module_id,parent_id) VALUES ('24200', '0',b'0', '', b'0', '/under002', 'undertake.template', '200', '_self', 'pc', b'1', '1', 34);


-- menu Rapid deployment
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35', '0', 's-menu-initialize', 0, 1, '', 'initialize', '960', '_self', 'pc', 1, '1', NULL);
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35001', '0', '', 0, 1, '/init001', 'initialize.workteam', '10', '_self', 'pc', 1, '1', '35');
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35002', '0', '', 0, 1, '/init002', 'initialize.requirementType', '20', '_self', 'pc', 1, '1', '35');
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35003', '0', '', 0, 1, '/init003', 'initialize.serviceType', '30', '_self', 'pc', 1, '1', '35');
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35004', '0', '', 0, 1, '/init004', 'initialize.priority', '40', '_self', 'pc', 1, '1', '35');
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35005', '0', '', 0, 1, '/init005', 'initialize.satisfaction', '50', '_self', 'pc', 1, '1', '35');
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35006', '0', '', 0, 1, '/init006', 'initialize.sla', '60', '_self', 'pc', 1, '1', '35');
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35007', '0', '', 0, 1, '/init007', 'initialize.patrolTemplate', '70', '_self', 'pc', 1, '1', '35');
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35008', '0', '', 0, 1, '/init008', 'initialize.ppmTemplate', '80', '_self', 'pc', 1, '1', '35');
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35009', '0', '', 0, 1, '/init009', 'initialize.equipmentSystem', '90', '_self', 'pc', 1, '1', '35');
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35010', '0', '', 0, 1, '/init010', 'initialize.role', '100', '_self', 'pc', 1, '1', '35');
INSERT INTO `sys_menu` (`id`, `version`, `icon`, `is_all`, `is_project`, `link`, `name`, `order_number`, `target`, `type`, `visible`, `module_id`, `parent_id`) VALUES ('35011', '0', '', 0, 1, '/init011', 'initialize.permission', '110', '_self', 'pc', 1, '1', '35');

-- ----------------------------
-- sys_permission
-- ----------------------------
-- --- common ----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('1', '0', '', b'1', b'1', 'get', '首页', '/main/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2', '0', '', b'1', b'1', 'post', 'Datatable表数据', '/common/tabledata/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3', '0', '', b'1', b'1', 'post', '文件上传', '/common/files/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4', '0', '', b'1', b'1', 'get', '查看文件', '/common/files/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5', '0', '', b'1', b'1', 'delete', '删除文件', '/common/files/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('6', '0', '', b'1', b'1', 'post', '自动补全', '/common/tabledata/*/likesearch', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('7', '0', '', b'1', b'1', 'get', '文件导出', '/export/files/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('8', '0', '', b'1', b'1', 'get', 'TEST', '/common/test/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9', '0', '', b'1', b'1', 'post', '单位', '/common/units/query', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10', '0', '', b'1', b'1', 'post', '移动端接口', '/m/v1/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11', '0', '', b'1', b'1', 'get', '未读消息数', '/uc001/messages/unread/*', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12', '0', '', b'1', b'1', 'get', '项目组', '/uc001/projects', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13', '0', '', b'1', b'1', 'get', '项目地图', '/pro000/map/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14', '0', '', b'1', b'1', 'post', 'Dashboard', '/chart/dashboard/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('15', '0', ' ', b'1', b'1', 'get', 'dashbord', '/chart/dashboard/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17', '0', ' ', b'1', b'1', 'get', 'media', '/common/media/*', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18', '0', '', b'1', b'1', 'post', '移动端接口v2', '/m/v2/**', null);
-- 只admin可见，
-- INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('20', '0', ' ', b'1', b'1', 'get', 'releases', '/common/releases', null);
-- INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21', '0', ' ', b'1', b'1', 'get', 'releases', '/common/releases/**', null);
INSERT INTO sys_permission (`id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) VALUES ('33', 0,NULL, 1, 1, 'post', '移动端接口v4', '/m/v4/**', NULL);

-- --- common END----
-- --- ucenter ----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('1101', '0', '', b'1', b'0', 'get', '查看个人信息', '/uc001/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('1102', '0', '', b'1', b'0', 'put', '修改个人信息', '/uc001/**', null);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('1201', '0', '', b'1', b'0', 'get', '个人消息', '/uc002', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('1202', '0', '', b'1', b'0', 'post', '个人消息', '/uc002/**', null);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('1203', '0', '', b'1', b'0', 'get', '已阅', '/uc002/read/**', null);
-- --- ucenter END----

-- --- service center----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2101', '0', '', b'1', b'0', 'get', '查看需求', '/service001', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2102', '0', '', b'1', b'0', 'post', '创建需求', '/service001/requirement', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2103', '0', '', b'1', b'0', 'put', '跟进需求', '/service001/requirement/*', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2105', '0', ' ', b'1', b'0', 'put', '完成需求', '/service001/requirement/finish/*', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2106', '0', '', b'1', b'0', 'post', '满意度调查', '/service001/requirement/evaluation', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2107', '0', ' ', b'1', b'1', 'post', '需求的查询', '/service001/requirement/search', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2108', '0', ' ', b'1', b'1', 'get', '获取需求的详情', '/service001/**', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2109', '0', ' ', b'1', b'1', 'post', '获取工单设备', '/service001/woEquipment/*', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2110', '0', ' ', b'1', b'1', 'get', '获取需求类型', '/service003/*', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2111', '0', ' ', b'1', b'1', 'get', '选择需求类型', '/service003/requirementType/*', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2113', '0', ' ', b'1', b'1', 'post', '获取客户', '/service001/CustomerDTO ', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2115', '0', ' ', b'1', b'1', 'post', '获取需求类型（工单=是）', '/service001/Priority/*', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2117', '0', ' ', b'1', b'1', 'get', '获取部门', '/org003/organizations/*', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2118', '0', ' ', b'1', b'1', 'get', '获取服务类型', '/wo003/*', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2119', '0', ' ', b'1', b'0', 'post', '创建工单', '/service001/workOrder', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2120', '0', ' ', b'1', b'1', 'get', '获取含有工单的需求详情', '/wo000/**', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2121', '0', ' ', b'1', b'1', 'post', '获取含有工单的工单详情', '/wo000/**', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2122', '0', ' ', b'1', b'1', 'get', '获取关联工单', '/wo000/workorders/*/associated', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2123', '0', ' ', b'1', b'1', 'get', '需求详情', '/service000', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2124', '0', ' ', b'0', b'1', 'put', '满意度评级', '/service001/requirement/satisfaction/*', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2125', '0', ' ', b'1', b'1', 'post', '需求详情自动补全', '/service000/solr/*', '2100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2126', '0', ' ', b'1', b'1', 'put', '需求完成查询', '/service001/requirement/searchfinish/*', '2100');
INSERT INTO `sys_permission` (`id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) VALUES ('2127', '0', '', 1, 0, 'post', '微信需求审核', '/service001/check', '2100');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2201', '0', '', b'1', b'0', 'get', '需求查询', '/service002', '2200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2203', '0', ' ', b'1', b'1', 'post', '需求列表显示', '/service002/requirement/table/*', '2200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2204', '0', ' ', b'1', b'1', 'get', '需求详情页面', '/service002/requirement/*', '2200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2205', '0', '', b'1', b'1', 'get', '获取工单信息', '/wo000/**', '2200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2206', '0', '', b'1', b'1', 'post', '获取工单信息', '/wo000/**', '2200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2207', '0', '', b'1', b'1', 'get', '获取需求信息', '/service001/**', '2200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2208', '0', '', b'1', b'1', 'post', '需求导出', '/service002/requirement/export', '2200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2210', '0', '', b'1', b'1', 'post', '工单预定列表', '/stock035/wotable/*', '2200');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2301', '0', '', b'1', b'0', 'get', '查看需求类型', '/service003', '2300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2302', '0', '', b'1', b'0', 'post', '添加需求类型', '/service003/**', '2300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2303', '0', '', b'1', b'0', 'put', '修改需求类型', '/service003/**', '2300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2304', '0', '', b'1', b'0', 'delete', '删除需求类型', '/service003/**', '2300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2305', '0', ' ', b'1', b'1', 'get', '需求类型树', '/service003/*', '2300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2306', '0', ' ', b'1', b'1', 'get', '获取需求类型的详情', '/service003/nodes/*', '2300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('2307', '0', ' ', b'1', b'1', 'get', '获取服务类型', '/wo003/*', '2300');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (2401, 0, '', b'1', b'0', 'get', '获取通知页面', '/service004', 2400);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (2402, 0, '', b'1', b'1', 'get', '查看通知设置', '/service004/*', 2400);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (2403, 0, '', b'1', b'0', 'post', '修改通知设置', '/service004/**', 2400);

-- --- service center END----
-- --- workorder----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3101', '0', '', b'1', b'0', 'get', '查看工单', '/wo001', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3102', '0', '', b'1', b'1', 'post', '工单列表', '/wo001/workorders', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3103', '0', '', b'1', b'0', 'put', '工单追踪', '/wo001/workorders', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3105', '0', '', b'1', b'1', 'get', '接单', '/wo000/labours/**', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3106', '0', '', b'1', b'1', 'get', '工单任务', '/wo001/workorders/**', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3107', '0', '', b'1', b'1', 'post', '获取工单执行人、设备、物料、工具', '/wo000/**', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3109', '0', '', b'1', b'1', 'get', '工单打印', '/wo000/print/*', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3112', '0', '', b'1', b'1', 'get', '工单详情图片', '/wo000/pictures/*', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3113', '0', '', b'1', b'1', 'get', '工单详情附件', '/wo000/attachments/*', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3114', '0', '', b'1', b'1', 'get', '工单设备', '/wo000/equipments/*/check/*', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3115', '0', '', b'1', b'1', 'get', '预防性维护工单附件', '/pm001/attachments/*', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3116', '0', '', b'1', b'1', 'get', '获取需求信息', '/wo000/requirements/*', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3117', '0', '', b'1', b'1', 'get', '获取工单详情', '/wo000/details/*', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3118', '0', '', b'1', b'1', 'get', '获取需求图片附件', '/service001/**', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3119', '0', '', b'1', b'1', 'delete', '删除执行人', '/wo000/labours/**', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3120', '0', '', b'1', b'1', 'get', '审批申请', '/wo004/templates', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3121', '0', '', b'1', b'1', 'get', '获取部门', '/org003/organizations/*', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3122', '0', '', b'1', b'1', 'delete', '删除设备', '/wo000/equipments/**', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3123', '0', '', b'1', b'1', 'get', '获取设备列表', '/wo000/equipments/entire/*', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3124', '0', '', b'1', b'1', 'get', '获取审批模板', '/wo004/templates/*/parameters', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3125', '0', '', b'1', b'1', 'get', 'PM关联工单', '/wo000/workorders/*/associated', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3127', '0', '', b'1', b'1', 'delete', '删除工具', '/wo000/tools/**', '3100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3126, 0, '', b'1', b'1', 'post', '获取保管员', '/stock001/managers', 3100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3128, 0, '', b'1', b'1', 'get', '获取主管', '/stock035/supervisors/*', 3100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3129, 0, '', b'1', b'1', 'get', '获取部门', '/stock035/organizations', 3100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3130, 0, '', b'1', b'1', 'post', '预定物资', '/stock003/inventorymanage/stockreserve', 3100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3131, 0, '', b'1', b'1', 'post', '工单预定列表', '/stock035/wotable/*', 3100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3132, 0, '', b'1', b'1', 'post', '查看工单预定单', '/stock007/detailtable/*', 3100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3133, 0, '', b'1', b'0', 'post', '编辑工单预定单', '/stock035/stockreserveEdit', 3100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3134, 0, '', b'1', b'1', 'delete', '取消工单预定', '/stock035/reserve/*', 3100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3135, 0, '', b'1', b'0', 'post', '创建工单', '/wo001/workOrder/create', 3100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3136, 0, '', b'1', b'1', 'post', '获取客户', '/service001/CustomerDTO', 3100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (3137, 0, '', b'1', b'1', 'post', '获取服务类型', '/service001/Priority/*', 3100);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3201', '0', '', b'1', b'0', 'get', '工单查询', '/wo002', '3200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3202', '0', '', b'1', b'1', 'post', '获取工单表单数据', '/wo002/workorders/*', '3200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3203', '0', ' ', b'1', b'1', 'get', '获取部门组织', '/org003/organizations/*', '3200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3204', '0', ' ', b'1', b'1', 'get', '获取服务类型', '/wo003/*', '3200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3205', '0', ' ', b'1', b'1', 'get', '获取设备分类', '/asset001/equipmentsystems/*', '3200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3206', '0', ' ', b'1', b'1', 'get', '获取工单详情', '/wo000/**', '3200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3207', '0', ' ', b'1', b'1', 'get', '获取需求详情', '/service001/**', '3200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3208', '0', ' ', b'1', b'1', 'post', '工单详情', '/wo000/**', '3200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3209', '0', ' ', b'1', b'1', 'get', '获取计划性维护详情', '/pm001/attachments/*', '3200');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3301', '0', '', b'1', b'0', 'get', '查看SLA', '/wo003', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3302', '0', '', b'1', b'1', 'get', '获取服务类型', '/wo003/servicetypes', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3303', '0', '', b'1', b'1', 'get', '获取部门组织', '/wo003/organizations', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3304', '0', '', b'1', b'0', 'post', '创建SLA', '/wo003/woprocesses', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3305', '0', '', b'1', b'0', 'put', '修改SLA', '/wo003/woprocesses', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3306', '0', '', b'1', b'0', 'delete', '删除SLA', '/wo003/woprocesses/*', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3307', '0', '', b'1', b'1', 'post', '获取工单流程表单', '/wo003/woprocesses/list', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3308', '0', '', b'1', b'1', 'post', '选择一个工单流程', '/wo003/woprocesses/details', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3319', '0', ' ', b'1', b'1', 'get', '获取优先级', '/wo007/*', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3320', '0', ' ', b'0', b'1', 'get', '打印', '/wo000/print/*', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3321', '0', ' ', b'0', b'1', 'delete', '删除工具', '/wo000/tools/**', '3300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3322', '0', '', b'1', b'1', 'get', '通过工作组查询到对应员工', '/org002/employees/byWorkTeam/**', '3300');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3401', '0', '', b'1', b'0', 'get', '查看审批模板', '/wo004', '3400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3402', '0', '', b'1', b'1', 'post', '工单审批模板参数列表', '/wo004/parameters/*', '3400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3403', '0', '', b'1', b'0', 'post', '添加审批模板', '/wo004/*', '3400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3404', '0', '', b'1', b'0', 'put', '修改审批模板', '/wo004/*', '3400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3405', '0', '', b'0', b'1', 'post', '添加审批模板参数', '/wo004/parameters', '3400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3406', '0', '', b'0', b'1', 'put', '修改审批模板参数', '/wo004/parameters', '3400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3407', '0', '', b'1', b'0', 'delete', '删除审批模板', '/wo004/**', '3400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3408', '0', '', b'0', b'1', 'delete', '删除审批模板参数', '/wo004/parameters/*', '3400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3409', '0', '', b'1', b'1', 'get', '获取审批模板详情', '/wo004/templates/*', '3400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3410', '0', '', b'1', b'1', 'get', '获取审批模板参数详情', '/wo004/parameters/*', '3400');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3501', '0', '', b'1', b'0', 'get', '查看员工排班', '/wo005', '3500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3502', '0', '', b'1', b'0', 'post', '添加排班规则', '/wo005/schedulings/scheduleClasses', '3500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3503', '0', '', b'1', b'0', 'put', '修改排班规则', '/wo005/schedulings/**', '3500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3504', '0', '', b'1', b'0', 'delete', '删除排班规则', '/wo005/schedulings/**', '3500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3508', '0', '', b'1', b'1', 'post', '获取排班', '/wo005/schedulings/allData', '3500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3509', '0', '', b'1', b'1', 'get', '查看排班规则', '/wo005/schedulings/scheduleClasses/*', '3500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3510', '0', ' ', b'1', b'0', 'post', '为员工排班', '/wo005/schedulings', '3500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3511', '0', ' ', b'1', b'0', 'post', '排班导入', '/wo005/schedulings/import', '3500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3512', '0', ' ', b'1', b'0', 'post', '排班导出', '/wo005/schedulings/export', '3500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3513', '0', ' ', b'0', b'1', 'get', '主管导出权限','/wo005/schedulings/role','3500');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3601', '0', '', b'1', b'0', 'get', '查看服务类型', '/wo006', '3600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3602', '0', '', b'1', b'1', 'get', '服务类型列表', '/wo006/*', '3600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3603', '0', '', b'1', b'0', 'post', '添加服务类型', '/wo006/servicetypes', '3600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3604', '0', '', b'1', b'0', 'put', '修改服务类型', '/wo006/servicetypes/*', '3600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3605', '0', '', b'1', b'1', 'get', '获取服务类型的详情', '/wo006/servicetypes/*', '3600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3606', '0', '', b'1', b'0', 'delete', '删除服务类型', '/wo006/servicetypes/*', '3600');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3701', '0', '', b'1', b'0', 'get', '查看优先级', '/wo007', '3700');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3703', '0', '', b'1', b'0', 'post', '添加优先级', '/wo007/prioritys', '3700');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3704', '0', '', b'1', b'0', 'put', '修改优先级', '/wo007/prioritys', '3700');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3705', '0', '', b'1', b'0', 'delete', '删除优先级', '/wo007/prioritys/*', '3700');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3801', '0', '', b'1', b'0', 'get', '查看满意度', '/wo008', '3800');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3802', '0', '', b'1', b'0', 'post', '添加满意度', '/wo008/satisfactions', '3800');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3804', '0', '', b'1', b'0', 'put', '修改满意度', '/wo008/satisfactions', '3800');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3805', '0', '', b'1', b'0', 'delete', '删除满意度', '/wo008/satisfactions/*', '3800');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3901', '0', '', b'1', b'0', 'get', '查看审批', '/wo009', '3900');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3902', '0', '', b'1', b'1', 'get', '工单审批详情', '/wo009/approvals/**', '3900');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3903', '0', '', b'1', b'1', 'post', '审批申请', '/wo009/approvals', '3900');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3904', '0', '', b'1', b'0', 'post', '审批工单', '/wo009/approvals/*/result', '3900');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3905', '0', ' ', b'1', b'1', 'get', '获取工单详情', '/wo000/**', '3900');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3906', '0', ' ', b'1', b'1', 'get', '获取需求详情', '/service001/**', '3900');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3907', '0', ' ', b'1', b'1', 'post', '工单详情', '/wo000/**', '3900');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3908', '0', ' ', b'1', b'1', 'get', '获取计划性维护工单详情', '/pm001/attachments/*', '3900');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3909', '0', '', b'1', b'1', 'get', '获取审批附件', '/wo009/attachments/*', '3900');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('3910', '0', '', b'1', b'1', 'post', '工单预定列表', '/stock035/wotable/*', '3900');

-- --- workorder END----
-- --- preventive----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4101', '0', '', b'1', b'0', 'get', '查看维护设置', '/pm001/**', '4100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4102', '0', '', b'1', b'0', 'post', '添加维护设置', '/pm001/preventives/setting', '4100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4103', '0', '', b'1', b'0', 'put', '修改维护设置', '/pm001/**', '4100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4104', '0', '', b'1', b'0', 'delete', '删除维护设置', '/pm001/**', '4100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4105', '0', ' ', b'1', b'1', 'post', '维护计划列表', '/pm001/preventives/ptms', '4100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4107', '0', ' ', b'1', b'1', 'get', '获取系统分类', '/asset001/equipmentsystems/*', '4100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4108', '0', ' ', b'1', b'1', 'get', '获取空间位置', '/pm002/pmdatetodos/*', '4100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4109', '0', ' ', b'1', b'0', 'post', '复制PPM同时复制文件', '/pm001/copyfile/*', '4100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4112', '0', '', b'1', b'1', 'post', '选择全部设备', '/asset002/equipments/equipments', '4100');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4201', '0', '', b'1', b'0', 'get', '查看维护日历', '/pm002/**', '4200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4202', '0', '', b'1', b'0', 'post', '添加维护计划', '/pm002/pmdatetodos/generatetodo ', '4200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4203', '0', ' ', b'1', b'0', 'post', '手动生成工单', '/pm002/pmdatetodos/generateWo', '4200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4204', '0', '', b'1', b'0', 'delete', '删除维护计划', '/pm002/pmdatetodos', '4200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4206', '0', ' ', b'1', b'1', 'get', '系统分类的显示', '/asset001/equipmentsystems/*', '4200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4207', '0', ' ', b'1', b'1', 'get', '获取工单', '/wo000/**', '4200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4208', '0', ' ', b'1', b'1', 'post', '获取工单详情', '/wo000/**', '4200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4209', '0', ' ', b'1', b'1', 'post', '查看工单列表', '/pm002/pmdatetodo/workorders', '4200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('4210', '0', ' ', b'1', b'1', 'post', '查看日历', '/pm002/pmdatetodos/calendar', '4200');
-- --- preventive END----
-- --- patrol----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5101', '1', '', b'1', b'0', 'get', '查看巡检点位', '/par001', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5102', '1', '', b'1', b'0', 'post', '添加巡检点位', '/par001/spots/**', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5103', '1', '', b'1', b'0', 'put', '修改巡检点位', '/par001/spots', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5104', '0', '', b'1', b'1', 'get', '点位详情', '/par001/spots/**', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5105', '1', '', b'1', b'0', 'delete', '删除巡检点位', '/par001/spots/**', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5106', '0', '', b'1', b'1', 'post', '巡检点位列表', '/par001/allspot', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5107', '0', '', b'1', b'1', 'post', '添加点位巡检内容', '/par001/spotcontent', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5108', '0', '', b'1', b'1', 'get', '巡检点位详情', '/par001/spot/spotcontent/*', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5109', '0', ' ', b'1', b'1', 'put', '修改巡检内容', '/par001/spotcontent', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5110', '0', '', b'1', b'1', 'put', '点位设备上下移动', '/par001/equipment/**', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5111', '0', ' ', b'1', b'1', 'post', '查看点位任务', '/par001/spotjobs/**', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5112', '0', ' ', b'1', b'1', 'post', '添加更新巡检工作任务', '/par001/spotjobs', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5113', '0', ' ', b'1', b'1', 'delete', '删除点位的工作任务', '/par001/spotjobs/*', '5100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5135', '0', '', b'1', b'1', 'post', '选择全部设备', '/asset002/equipments/equipments', '5100');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5201', '0', '', b'1', b'0', 'get', '查看巡检计划', '/par002', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5202', '0', '', b'1', b'1', 'get', '根据id获取巡检', '/par002/*', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5203', '0', '', b'1', b'1', 'post', '获取所有的巡检', '/par002/allpatrol', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5204', '0', '', b'1', b'0', 'post', '添加巡检计划', '/par002/patrol', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5205', '0', '', b'1', b'0', 'put', '修改巡检计划', '/par002/patrol', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5206', '0', '', b'1', b'0', 'delete', '删除巡检计划', '/par002/patrol/*', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5207', '0', '', b'1', b'1', 'delete', '删除巡检点位', '/par002/spot/**', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5208', '0', '', b'1', b'1', 'post', '获取巡检点位列表', '/par002/patrols/*/spotjobs', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5209', '0', ' ', b'1', b'1', 'post', '获取巡检记录提醒', '/par003/patrolRemind/*', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5210', '0', ' ', b'1', b'1', 'put', '巡检内容向上向下', '/par002/spot/**', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5211', '0', ' ', b'1', b'1', 'post', '添加巡检点位任务', '/par002/patrol/*/spot', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5212', '0', ' ', b'1', b'1', 'delete', '删除提醒', '/par002/partrolRemind/*', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5213', '0', ' ', b'1', b'1', 'delete', '删除例外日期', '/par002/partrolException/*', '5200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5214', '0', ' ', b'1', b'1', 'post', '巡检点位任务', '/par002/spotjobs', '5200');
INSERT INTO sys_permission (id, version, description, enabled, is_hide, method, name, path, menu_id) VALUES (5215, 0, null, true, false, 'post', '导入节假日', '/par002/holidays/*', 5200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5222', '0', ' ', b'1', b'1', 'get', '添加点位任务剔除已被选中的点位', '/par002/spots/*', '5200');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5301', '0', '', b'1', b'0', 'get', '巡检记录查询', '/par003', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5302', '0', '', b'1', b'1', 'get', '查询信息：信息获取', '/par003/**', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5304', '0', '', b'1', b'1', 'post', '巡检列表', '/par003/partoldetail', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5306', '0', '', b'1', b'1', 'post', '巡检报障', '/service001/Priority/', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5307', '0', '', b'1', b'0', 'post', '报障', '/service001/workOrder', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5308', '0', '', b'0', b'1', 'post', '巡检补检', '/par003/inspection', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5309', '1', '', b'1', b'1', 'post', '巡检工单设备查询', '/par003/woEquipment/*', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5310', '1', '', b'1', b'1', 'post', 'DETAILS列表显示', '/par003/taskitems/**', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5311', '0', ' ', b'1', b'1', 'post', '', '/par003/partoldetail', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5312', '0', ' ', b'1', b'1', 'get', '巡检文档显示', '/par003/doc/*', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5313', '0', ' ', b'1', b'1', 'post', '巡检点位', '/par003/tasks/*/spots', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5315', '0', ' ', b'1', b'1', 'post', '巡检历史', '/par003/*/history', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5316', '0', ' ', b'1', b'1', 'post', '点位显示', '/par003/spotcontent/spot/*', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5317', '0', ' ', b'1', b'1', 'post', '设备点位显示', '/par003/tasks/*/spots/*/eqs', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5318', '0', ' ', b'1', b'1', 'get', '获取部门', '/org003/organizations/*', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5319', '0', ' ', b'1', b'1', 'get', '获取工单流程', '/wo003/*', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5320', '0', ' ', b'1', b'1', 'get', '获取工单中的设备', '/wo000/equipments/entire/*', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5321', '0', ' ', b'1', b'1', 'post', '点位设备详情', '/par003/spot/equipment/*/*', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5322', '0', ' ', b'1', b'0', 'post', '补检', '/par003/taskspots/*', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5323', '0', ' ', b'0', b'1', 'post', '报障', '/par003/spotresults/*/wos/*', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5324', '0', ' ', b'1', b'1', 'post', '巡检任务中巡检设备', '/par003/taskspots/*/eqs', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5325', '0', ' ', b'1', b'0', 'post', '导出', '/par003/partoldetail/export', '5300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5326', '0', ' ', b'1', b'0', 'put', '更新异常巡检处理结果', '/par003/taskspot/*/result/*', '5300');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5401', '1', '', b'1', b'0', 'get', '查看巡检模板', '/par004', '5400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5402', '1', '', b'1', b'0', 'post', '添加巡检模板', '/par004/**', '5400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5403', '1', '', b'1', b'0', 'put', '修改巡检模板', '/par004/**', '5400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5404', '1', '', b'1', b'0', 'delete', '删除巡检模板', '/par004/**', '5400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5405', '1', ' ', b'1', b'1', 'post', '获取模板详情', '/par004/spots/spotcontent/*', '5400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5406', '1', ' ', b'1', b'1', 'get', '巡检内容列表', '/par004/patrolTemplate/*', '5400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5407', '1', ' ', b'1', b'1', 'get', '巡检内容详情', '/par004/patrolcts/*', '5400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5408', '1', ' ', b'1', b'1', 'delete', '删除巡检内容', '/par004/patrolcts/*', '5400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('5409', '1', ' ', b'1', b'1', 'post', '添加巡检内容', '/par004/patrolcts', '5400');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (5601, 0, '',  b'1', b'0', 'get', '节假日页面', '/par006', 5600);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (5602, 0, '',  b'1', b'0', 'post', '新增节假日', '/par006/holidays', 5600);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (5603, 0, '',  b'1', b'0', 'put', '修改节假日', '/par006/holidays', 5600);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (5604, 0, '',  b'1', b'0', 'delete', '删除节假日', '/par006/holidays/*', 5600);
-- --- patrol END----

-- --- stock START----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6101, 0, '', b'1', b'1', 'get', '仓库设置','/stock001', 6100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6102, 0, '', b'1', b'1', 'post', '查看仓库列表','/stock001/warehouses/table', 6100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6103, 0, '', b'1', b'0', 'post', '添加仓库','/stock001/warehouses', 6100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6104, 0, '', b'1', b'0', 'put', '修改仓库','/stock001/warehouses', 6100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6105, 0, '', b'1', b'0', 'delete', '删除仓库','/stock001/warehouses/*', 6100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6106, 0, '', b'1', b'1', 'get', '查看仓库','/stock001/warehouses/*', 6100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6107, 0, '', b'1', b'1', 'post', '验证仓库操作权限','/stock001/check', 6100);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6108, 0, '', b'1', b'1', 'post', '验证仓库名称','/stock001/checkName/*', 6100);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6201, 0, '', b'1', b'1', 'get', '查看物资','/stock002', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6202, 0, '', b'1', b'0', 'post', '添加物资','/stock002/inventorys', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6203, 0, '', b'1', b'0', 'put', '修改物资','/stock002/inventorys', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6204, 0, '', b'1', b'1', 'get', '获取物资详情信息','/stock002/inventorys/detail/*', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6205, 0, '', b'1', b'0', 'delete', '删除物资','/stock002/inventorys/*', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6206, 0, '', b'1', b'1', 'post', '获取物资当前记录和移库记录','/stock002/inventorys/activityrecords/**', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6207, 0, '', b'1', b'1', 'post', '物资已存在验证','/stock002/inventorys/exit/**', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6208, 0, '', b'1', b'1', 'post', '获取供应商','/stock002/inventorys/providers/name', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6209, 0, '', b'1', b'1', 'post', '查看物资列表','/stock002/inventorys/table', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6210, 0, '', b'1', b'0', 'post', '导出物资','/stock002/inventorys/export/materials', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6211, 0, '', b'1', b'0', 'post', '下载模板','/stock002/inventorys/export/template', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6212, 0, '', b'1', b'0', 'post', '导入物资','/stock002/inventorys/inc/validate/*', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6213, 0, '', b'1', b'1', 'post', '获取物资入库记录','/stock002/inventorys/stockinrecords/*', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6214, 0, '', b'1', b'1', 'post', '获取物资出库记录','/stock002/inventorys/stockoutrecords/*', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6215, 0, '', b'1', b'1', 'post', '获取物资预定记录','/stock002/inventorys/showReserveRecords/*', 6200);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6216, 0, '', b'1', b'1', 'post', '物资覆盖验证', '/stock002/inventorys/materials/hasexit/*', 6200);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6311, 0, '', b'1', b'1', 'get', '物资入库页面权限','/stock031', 6310);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6312, 0, '', b'1', b'0', 'post', '导出仓库物资','/stock031/export', 6310);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6313, 0, '', b'1', b'0', 'post', '批量导入物资','/stock031/import/*', 6310);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6314, 0, '', b'1', b'1', 'post', '获取供应商','/stock002/inventorys/providers/name', 6310);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6315, 0, '', b'1', b'1', 'post', '根据仓库获取对应的保管员','/stock001/managers', 6310);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6316, 0, '', b'1', b'0', 'post', '物资入库','/stock003/inventorymanage/stockin', 6310);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6321, 0, '', b'1', b'1', 'get', '物资出库页面权限','/stock032', 6320);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6322, 0, '', b'1', b'1', 'post', '获取预定出库物资列表','/stock032/table', 6320);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6323, 0, '', b'1', b'0', 'post', '取消预定','/stock032/cancelorder/*', 6320);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6324, 0, '', b'1', b'1', 'post', '详情列表','/stock032/detailtable/*', 6320);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6325, 0, '', b'1', b'1', 'post', '根据仓库获取对应的保管员','/stock001/managers', 6320);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6326, 0, '', b'1', b'1', 'get', '获取领用人对应的主管','/stock035/supervisors/*', 6320);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6327, 0, '', b'1', b'1', 'get', '获取领料部门','/stock035/organizations', 6320);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6328, 0, '', b'1', b'0', 'post', '直接出库','/stock003/inventorymanage/stockout', 6320);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6329, 0, '', b'1', b'0', 'post', '预定出库','/stock003/inventorymanage/stockreserveout', 6320);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6331, 0, '', b'1', b'1', 'get', '物资退库页面权限','/stock033', 6330);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6332, 0, '', b'1', b'1', 'post', '获取物资退库列表','/stock033/table', 6330);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6333, 0, '', b'1', b'1', 'post', '获取退库物资详情','/stock033/detailtable/*', 6330);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6334, 0, '', b'1', b'1', 'post', '根据仓库获取对应的保管员','/stock001/managers', 6330);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6335, 0, '', b'1', b'1', 'get', '获取退库人对应的主管','/stock035/supervisors/*', 6330);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6336, 0, '', b'1', b'1', 'get', '获取领料部门','/stock035/organizations', 6330);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6337, 0, '', b'1', b'0', 'post', '物资退库','/stock003/inventorymanage/stockback', 6330);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6341, 0, '', b'1', b'1', 'get', '物资移库页面权限','/stock034', 6340);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6342, 0, '', b'1', b'1', 'post', '获取移库物资','/stock034/materials/*', 6340);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6343, 0, '', b'1', b'1', 'post', '根据仓库获取对应的保管员','/stock001/managers', 6340);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6344, 0, '', b'1', b'1', 'post', '获取移库人为当前登录用户','/stock034/ems', 6340);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6345, 0, '', b'1', b'0', 'post', '物资移库','/stock034/move', 6340);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6351, 0, '', b'1', b'1', 'get', '物资预定页面权限','/stock035', 6350);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6352, 0, '', b'1', b'1', 'post', '根据仓库获取对应的保管员','/stock001/managers', 6350);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6353, 0, '', b'1', b'1', 'get', '获取预定人对应的主管','/stock035/supervisors/*', 6350);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6354, 0, '', b'1', b'1', 'get', '获取领料部门','/stock035/organizations', 6350);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6355, 0, '', b'1', b'0', 'post', '物资预定','/stock003/inventorymanage/stockreserve', 6350);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6361, 0, '', b'1', b'1', 'get', '预定管理页面权限','/stock036', 6360);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6362, 0, '', b'1', b'1', 'post', '预定管理列表','/stock036/table', 6360);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6363, 0, '', b'1', b'0', 'post', '审核通过','/stock036/inventorymanage/beoutlib/*', 6360);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6364, 0, '', b'1', b'0', 'post', '审核不通过','/stock036/inventorymanage/rejected/*', 6360);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6365, 0, '', b'1', b'1', 'post', '预定详情列表','/stock036/detailtable/*', 6360);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6371, 0, '', b'1', b'1', 'get', '我的预定页面权限','/stock037', 6370);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6372, 0, '', b'1', b'1', 'post', '我的预定列表','/stock037/table', 6370);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6373, 0, '', b'1', b'1', 'post', '我的预定详情','/stock036/detailtable/*', 6370);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6374, 0, '', b'1', b'0', 'delete', '取消预定','/stock035/reserve/*', 6370);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6401, 0, '', b'1', b'1', 'get', '操作记录页面权限','/stock004', 6400);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6402, 0, '', b'1', b'1', 'post', '操作记录列表','/stock004/records/table', 6400);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6403, 0, '', b'1', b'0', 'post', '导出记录','/stock004/records/export', 6400);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6501, 0, '', b'1', b'1', 'get', '库存盘点页面权限','/stock005', 6500);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6502, 0, '', b'1', b'1', 'post', '获取物资库存列表','/stock005/inventories/verification/table', 6500);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6503, 0, '', b'1', b'0', 'post', '调整物资库存数量','/stock005/inventories/verification/adjustV2', 6500);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6504, 0, '', b'1', b'0', 'post', '导出盘点数据','/stock005/inventories/verification/export', 6500);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6505, 0, '', b'1', b'1', 'get', '获取物资盘点批次','/stock005/inventories/*', 6500);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6506, 0, '', b'1', b'0', 'post', '导入盘点数据','/stock005/inventories/inc/validate', 6500);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6601, 0, '', b'1', b'1', 'get', '最小量报告页面权限','/stock006', 6600);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6602, 0, '', b'1', b'1', 'post', '最小量物资列表','/stock006/inventories/minimum/table', 6600);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6603, 0, '', b'1', b'0', 'post', '导出最小量报告','/stock006/inventories/minimum/export', 6600);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6701, 0, '', b'1', b'1', 'get', '库存单查询页面权限','/stock007', 6700);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6702, 0, '', b'1', b'1', 'post', '库存单查询列表','/stock007/table', 6700);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (6703, 0, '', b'1', b'0', 'post', '库存单详情','/stock007/detailtable/*', 6700);
-- --- stock END ----

-- --- report ----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8111, 0, '', b'1', b'1', 'post', '需求汇总', '/report010/**', 8110);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8112, 0, '', b'1', b'1', 'get', '需求汇总', '/report010/**', 8110);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8121, 0, '', b'1', b'1', 'post', '需求量统计', '/report011/**', 8120);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8122, 0, '', b'1', b'1', 'get', '需求量统计', '/report011', 8120);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8131, 0, '', b'1', b'1', 'post', '需求类型统计', '/report012/**', 8130);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8132, 0, '', b'1', b'1', 'get', '需求类型统计', '/report012', 8130);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8141, 0, '', b'1', b'1', 'post', '需求满意度统计', '/report013/**', 8140);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8142, 0, '', b'1', b'1', 'get', '需求满意度统计', '/report013', 8140);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8151, 0, '', b'1', b'1', 'get', '需求时效', '/report014', 8150);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8152, 0, '', b'1', b'1', 'post', '需求时效', '/report014/**', 8150);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8211, 0, '', b'1', b'1', 'get', '工单汇总页面', '/report020', 8210);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8212, 0, '', b'1', b'1', 'post', '工单汇总', '/report020/**', 8210);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8213, 0, '', b'1', b'1', 'get', '工单汇总', '/report020/*', 8210);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8221, 0, '', b'1', b'1', 'post', '工单量统计', '/report021/**', 8220);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8222, 0, '', b'1', b'1', 'get', '工单量统计', '/report021', 8220);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8231, 0, '', b'1', b'1', 'get', '工单类型统计页面', '/report022', 8230);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8232, 0, '', b'1', b'1', 'post', '工单类型统计', '/report022/**', 8230);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8241, 0, '', b'1', b'1', 'get', '工单及时率', '/report023/**', 8240);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8242, 0, '', b'1', b'1', 'post', '工单及时率', '/report023/**', 8240);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8251, 0, '', b'1', b'1', 'get', '工时统计页面', '/report024', 8250);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8252, 0, '', b'1', b'1', 'post', '工时统计', '/report024/**', 8250);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8261, 0, '', b'1', b'1', 'post', '员工KPI', '/report025/**', 8260);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8262, 0, '', b'1', b'1', 'get', '员工KPI页面', '/report025', 8260);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8311, 0, '', b'1', b'1', 'post', '巡检汇总查询', '/report031/**', 8310);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8312, 0, '', b'1', b'1', 'get', '巡检汇总', '/report031', 8310);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8321, 0, '', b'1', b'1', 'post', '巡检统计查询', '/report032/**', 8320);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8322, 0, '', b'1', b'1', 'get', '巡检统计', '/report032', 8320);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8331, 0, '', b'1', b'1', 'get', '员工巡检效率', '/report033', 8330);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8332, 0, '', b'1', b'1', 'post', '员工巡检效率查询', '/report033/**', 8330);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8401, 0, '', b'1', b'1', 'get', '计划性维护统计', '/report041', 8400);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8402, 0, '', b'1', b'1', 'post', '计划性维护统计', '/report041/**', 8400);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8511, 0, '', b'1', b'1', 'post', '设备汇总', '/report051/**', 8510);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8512, 0, '', b'1', b'1', 'get', '设备汇总', '/report051', 8510);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8521, 0, '', b'1', b'1', 'post', '设备统计', '/report052/**', 8520);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8522, 0, '', b'1', b'1', 'get', '设备统计', '/report052', 8520);

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8601, 0, '', b'1', b'1', 'get', '月度报表', '/report006', 8600);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (8602, 0, '', b'1', b'1', 'get', '月度报表', '/report006/**', 8600);

-- --- report END ----

-- --- knowledge ----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9111', '0', '', b'1', b'0', 'get', '查看分类', '/knowledge001', '9110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9112', '0', '', b'1', b'1', 'get', '获取分类树形列表', '/knowledge001/topics/*', '9110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9113', '0', '', b'1', b'1', 'get', '获取单个分类详情', '/knowledge001/topic/*', '9110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9114', '0', '', b'1', b'0', 'post', '新增分类', '/knowledge001/topic', '9110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9115', '0', '', b'1', b'0', 'put', '修改分类', '/knowledge001/topic', '9110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9116', '0', '', b'1', b'0', 'delete', '删除分类', '/knowledge001/topic/*', '9110');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9120', '0', '', b'1', b'0', 'get', '查看文档', '/knowledge002', '9120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9121', '0', '', b'1', b'1', 'get', '查询时获取分类树形列表', '/knowledge001/querytopics/*', '9120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9122', '0', '', b'1', b'1', 'get', '编辑时获取分类树形列表', '/knowledge001/topics/*', '9120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9123', '0', '', b'1', b'1', 'post', '获取知识库文档列表', '/knowledge002/knowledgebase/*', '9120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9124', '0', '', b'1', b'1', 'get', '单个知识库文档详情', '/knowledge002/knowledgebase/*', '9120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9125', '0', '', b'1', b'0', 'delete', '删除文档', '/knowledge002/knowledge/*', '9120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9126', '0', '', b'1', b'1', 'get', '获取单个文档的附件', '/knowledge002/kbfiles/*', '9120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9127', '0', '', b'1', b'0', 'post', '新建文档', '/knowledge002/knowledge', '9120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9128', '0', '', b'1', b'0', 'put', '修改文档', '/knowledge002/knowledge', '9120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9129', '0', '', b'1', b'1', 'post', '获取标签下拉框列表', '/knowledge002/tag/*', '9120');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9131', '0', '', b'1', b'0', 'get', '查看分类', '/knowledge003', '9130');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9132', '0', '', b'1', b'1', 'get', '获取分类树形列表', '/knowledge003/topics/*', '9130');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9133', '0', '', b'1', b'1', 'get', '获取单个分类详情', '/knowledge003/topic/*', '9130');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9134', '0', '', b'1', b'0', 'post', '新增分类', '/knowledge003/topic', '9130');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9135', '0', '', b'1', b'0', 'put', '修改分类', '/knowledge003/topic', '9130');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9136', '0', '', b'1', b'0', 'delete', '删除分类', '/knowledge003/topic/*', '9130');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9140', '0', '', b'1', b'0', 'get', '查看文档', '/knowledge004', '9140');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9141', '0', '', b'1', b'1', 'get', '查询时获取分类树形列表', '/knowledge003/querytopics/*', '9140');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9142', '0', '', b'1', b'1', 'get', '编辑时获取分类树形列表', '/knowledge003/topics/*', '9140');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9143', '0', '', b'1', b'1', 'post', '获取知识库文档列表', '/knowledge004/knowledgebase/*', '9140');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9144', '0', '', b'1', b'1', 'get', '单个知识库文档详情', '/knowledge004/knowledgebase/*', '9140');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9145', '0', '', b'1', b'0', 'delete', '删除文档', '/knowledge004/knowledge/*', '9140');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9146', '0', '', b'1', b'1', 'get', '获取单个文档的附件', '/knowledge004/kbfiles/*', '9140');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9147', '0', '', b'1', b'0', 'post', '新建文档', '/knowledge004/knowledge', '9140');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9148', '0', '', b'1', b'0', 'put', '修改文档', '/knowledge004/knowledge', '9140');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('9149', '0', '', b'1', b'1', 'post', '获取标签下拉框列表', '/knowledge004/tag/*', '9140');
-- --- knowledge END ----

-- --- organization ----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10101', '0', '', b'1', b'0', 'get', '查看工作组', '/org001/workteams/**', '10100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10102', '0', '', b'1', b'0', 'post', '添加工作组', '/org001/workteams/**', '10100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10103', '0', '', b'1', b'0', 'put', '修改工作组', '/org001/workteams/**', '10100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10104', '0', '', b'1', b'0', 'delete', '删除工作组', '/org001/workteams/*', '10100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10105', '0', '', b'1', b'0', 'delete', '删除人员', '/org001/workteams/*/*', '10100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10108', '0', ' ', b'1', b'1', 'post', '工作组列表', '/org001/workteams/workteampage ', '10100');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10201', '0', '', b'1', b'0', 'get', '查看员工', '/org002/employees/**', '10200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10202', '0', '', b'1', b'0', 'post', '添加员工', '/org002/employees/ *', '10200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10203', '0', '', b'1', b'0', 'put', '修改员工', '/org002/employees/**', '10200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10204', '0', '', b'1', b'0', 'delete', '删除员工', '/org002/employees/**', '10200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10205', '0', ' ', b'1', b'1', 'get', '部门显示', '/org003/organizations/*', '10200');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10301', '0', '', b'1', b'0', 'get', '查看部门组织', '/org003/organizations/**', '10300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10302', '0', '', b'1', b'0', 'post', '添加部门组织', '/org003/organizations/', '10300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10303', '0', '', b'1', b'0', 'put', '修改部门组织', '/org003/organizations/*', '10300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10304', '0', '', b'1', b'0', 'delete', '删除部门组织', '/org003/organizations/*', '10300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10305', '0', '', b'1', b'1', 'get', '组织管理', '/org003', '10300');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10401', '0', '', b'1', b'0', 'get', '查看空间位置', '/org004/places', '10400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10402', '0', '', b'1', b'0', 'post', '添加空间位置', '/org004/places/*', '10400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10403', '0', '', b'1', b'0', 'put', '修改空间位置', '/org004/places/**', '10400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10404', '0', '', b'1', b'1', 'get', '获得位置树', '/org004/places/**', '10400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10405', '0', '', b'1', b'0', 'delete', '删除空间位置', '/org004/places/**', '10400');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10501', '0', '', b'1', b'0', 'get', '查看岗位', '/org005', '10500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10502', '0', '', b'1', b'0', 'post', '添加岗位', '/org005/positions', '10500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10503', '0', '', b'1', b'0', 'put', '修改岗位', '/org005/positions/*', '10500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10504', '0', '', b'1', b'1', 'get', '显示岗位信息', '/org005/positions/*', '10500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10505', '0', '', b'1', b'0', 'delete', '删除岗位', '/org005/positions/*', '10500');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10601', '0', '', b'1', b'0', 'get', '查看客户关系', '/org006', '10600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10602', '0', '', b'1', b'1', 'post', '客户列表', '/org006/customers/search', '10600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10603', '0', '', b'1', b'0', 'post', '添加客户', '/org006/customers', '10600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10604', '0', '', b'1', b'0', 'put', '修改客户', '/org006/customers', '10600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10605', '0', '', b'1', b'0', 'delete', '删除客户', '/org006/customers/*', '10600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('10606', '0', '', b'1', b'1', 'get', '显示客户信息', '/org006/customers/entire/*', '10600');

-- --- organization END----
-- --- system ----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11101', '0', '', b'1', b'0', 'get', '查看用户', '/sys001', '11100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11102', '0', '', b'1', b'0', 'post', '添加用户', '/sys001/users', '11100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11103', '0', '', b'1', b'0', 'put', '修改用户', '/sys001/users', '11100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11104', '0', '', b'1', b'0', 'delete', '删除用户', '/sys001/users/*', '11100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11105', '0', '', b'1', b'1', 'get', '获取用户', '/sys001/users/*', '11100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11106', '0', '', b'1', b'1', 'post', '用户列表', '/sys001/users/*', '11100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11107', '0', '', b'1', b'1', 'get', '获取用户角色', '/sys002/roles/*', '11100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11108', '0', '', b'1', b'1', 'get', '获取角色列表', '/sys002/roles', '11100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11109', '0', '', b'1', b'1', 'post', '员工列表', '/org002/employees/nouser', '11100');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11201', '0', '', b'1', b'1', 'get', '获取角色列表', '/sys002/roles', '11200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11202', '0', '', b'1', b'0', 'get', '查看角色', '/sys002', '11200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11203', '0', '', b'1', b'0', 'post', '添加角色', '/sys002/roles', '11200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11204', '0', '', b'1', b'0', 'put', '修改角色', '/sys002/roles', '11200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11205', '0', '', b'1', b'0', 'delete', '删除角色', '/sys002/roles/*', '11200');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11301', '0', '', b'1', b'0', 'get', '查看权限', '/sys003', '11300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11302', '0', '', b'1', b'1', 'get', '权限列表', '/sys003/permissions', '11300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11303', '0', '', b'1', b'1', 'post', '选择权限', '/sys003/permissions', '11300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11304', '0', '', b'1', b'0', 'put', '为角色分配权限', '/sys003/permissions', '11300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11306', '0', '', b'1', b'1', 'get', '菜单列表', '/sys003/menus', '11300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11308', '0', '', b'1', b'1', 'get', '根据角色获取菜单', '/sys003/menus/*', '11300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11309', '0', '', b'1', b'1', 'get', '根据角色和目录获取权限', '/sys003/permissions/**', '11300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11310', '0', '', b'1', b'1', 'get', '获取权限', '/sys003/permissions/roles/*', '11300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11311', '0', '  ', b'1', b'1', 'get', '角色列表', '/sys002/roles', '11300');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11401', '0', '', b'1', b'0', 'get', '查看消息记录', '/sys004', '11400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11402', '0', '', b'1', b'1', 'post', '消息记录', '/sys004/message/**', '11400');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11501', '0', '', b'1', b'0', 'get', '查看配置信息', '/sys005/**', '11500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11502', '0', '', b'1', b'0', 'put', '修改配置信息', '/sys005/**', '11500');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11601', '0', '', b'1', b'1', 'get', '获取消息模板', '/sys006/groups/*', '11600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11602', '0', '  ', b'1', b'0', 'get', '查看消息模板', '/sys006', '11600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11603', '0', '', b'1', b'1', 'post', '获取模板组', '/sys006/messageTemplateGroup', '11600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11604', '0', '', b'1', b'1', 'post', '根据模板组获取参数', '/sys006/*/messageTemplateParams', '11600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11605', '0', '', b'1', b'0', 'post', '修改消息模板', '/sys006/groups/*/mt', '11600');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11701', '0', '', b'1', b'0', 'get', 'job-schedule', '/common/quartz', '11700');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11702', '0', '', b'1', b'0', 'get', '所有schedule', '/common/quartz/triggers/**', '11700');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11703', '0', '', b'1', b'0', 'post', '添加任务', '/common/quartz/jobs', '11700');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11704', '0', '', b'1', b'0', 'delete', 'job-schedule', '/common/quartz/**', '11700');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11705', '0', '', b'1', b'0', 'post', 'job-schedule', '/common/quartz/**', '11700');

INSERT INTO `sys_permission`(id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11801', '0', '', b'1', b'0', 'get', '基础数据导入', '/sys007/**', '11800');
INSERT INTO `sys_permission`(id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('11802', '0', '', b'1', b'1', 'post', '获取模块列表', '/sys007/**', '11800');
-- --- system END----
-- --- asset----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12101', '0', '  ', b'1', b'1', 'get', '设备分类树', '/asset001/equipmentsystems/*', '12100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12102', '0', ' ', b'1', b'1', 'post', '参数列表', '/asset001/extracolumns/extracolumnpage/*', '12100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12103', '0', ' ', b'1', b'1', 'get', '参数详情', '/asset001/extracolumns/*', '12100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12107', '0', '', b'1', b'0', 'get', '查看设备分类', '/asset001/equipmentsystems', '12100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12110', '0', '', b'1', b'0', 'post', '添加设备分类', '/asset001/equipmentsystems', '12100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12111', '0', '', b'1', b'0', 'put', '修改设备分类', '/asset001/equipmentsystems', '12100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12112', '0', '', b'1', b'0', 'delete', '删除设备分类', '/asset001/equipmentsystems/*', '12100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12113', '0', '', b'1', b'1', 'post', '添加设备参数', '/asset001/extracolumns', '12100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12114', '0', '', b'1', b'1', 'put', '修改设备参数', '/asset001/extracolumns', '12100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12115', '0', '', b'1', b'1', 'delete', '删除设备参数', '/asset001/extracolumns/*', '12100');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12201', '0', '', b'1', b'0', 'get', '台账清册', '/asset002/equipments/**', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12202', '0', '', b'1', b'0', 'post', '添加设备', '/asset002/equipments/**', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12204', '0', '', b'1', b'0', 'put', '修改设备', '/asset002/equipments/*', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12205', '0', '', b'1', b'0', 'delete', '删除设备', '/asset002/equipments/*', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12206', '0', '  ', b'1', b'1', 'post', '维修记录', '/asset002/equipments/page/wo/warranty/* ', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12207', '0', ' ', b'1', b'1', 'post', '维保记录', '/asset002/equipments/page/wo/maintenance/*', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12208', '0', ' ', b'1', b'1', 'get', '系统分类', '/asset001/equipmentsystems/*', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12209', '0', ' ', b'1', b'1', 'get', '选择系统分类', '/asset001/extracolumns/pkey/**', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12210', '0', ' ', b'1', b'1', 'post', '设备名称', '/asset002/equipments/codeexit/code', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12211', '0', ' ', b'1', b'0', 'post', '合同附件管理', '/asset003/attachment/auth', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12215', '0', ' ', b'1', b'1', 'post', '获取知识库列表', '/knowledge002/knowledgebase/*', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12216', '0', ' ', b'1', b'1', 'get', '获取单个文档的附件', '/knowledge004/kbfiles/*', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12217', '0', ' ', b'1', b'1', 'get', '单个知识库文档详情', '/knowledge004/knowledgebase/*', '12200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12218', '0', '', b'1', b'1', 'post', '合同查询', '/contract003/contract/query', '12200');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12301', '0', '', b'1', b'0', 'get', '查看到期提醒', '/asset003/**', '12300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12302', '0', '', b'1', b'0', 'post', '添加到期提醒', '/asset003/**', '12300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12303', '0', '', b'1', b'0', 'put', '修改到期提醒', '/asset003/**', '12300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12304', '0', '', b'1', b'0', 'delete', '删除到期提醒', '/asset003/**', '12300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('12305', '0', ' ', b'1', b'1', 'post', '设备列表', '/asset002/equipments/page', '12300');

-- --- asset END----

-- --- projects----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13101', '0', '', b'1', b'1', 'get', '页面', '/pro001', '13100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13102', '0', '', b'1', b'0', 'get', '查看', '/pro001/pgroup/*', '13100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13103', '0', '', b'1', b'0', 'post', '添加', '/pro001/pgroup', '13100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13104', '0', '', b'1', b'0', 'put', '修改', '/pro001/pgroup/*', '13100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13105', '0', '', b'1', b'0', 'delete', '删除', '/pro001/pgroup/*', '13100');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13201', '0', '', b'1', b'1', 'get', '页面', '/pro002', '13200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13202', '0', '', b'1', b'0', 'get', '查看', '/pro002/projects/**', '13200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13203', '0', '', b'1', b'0', 'post', '添加', '/pro002/projects', '13200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13204', '0', '', b'1', b'0', 'put', '修改', '/pro002/projects/*', '13200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13205', '0', '', b'1', b'0', 'delete', '删除', '/pro002/projects/*', '13200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('13206', '0', '', b'1', b'0', 'post', '项目查询', '/pro002/pfmProject', '13200');
-- --- projects END----


-- --- energy----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14101', '0', '', b'1', b'1', 'get', '页面', '/ene001', '14100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14102', '0', '', b'1', b'0', 'get', '列表查看', '/ene001/meterreads/*', '14100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14103', '0', '', b'1', b'0', 'post', '添加任务', '/ene001/meterreads/*', '14100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14104', '0', '', b'1', b'0', 'put', '修改任务', '/ene001/meterreads', '14100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14105', '0', '', b'1', b'0', 'delete', '删除任务', '/ene001/meterreads/*', '14100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14106', '0', '', b'1', b'1', 'post', '列表', '/ene001/meterreads', '14100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14107', '0', '', b'1', b'0', 'get', '添加项', '/ene001/meters/*', '14100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14108', '0', '', b'1', b'0', 'put', '修改项', '/ene001/meters', '14100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14109', '0', '', b'1', b'0', 'delete', '删除项', '/ene001/meters/*', '14100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14110', '0', '', b'1', b'1', 'post', '列表', '/ene001/meters', '14100');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14201', '0', '', b'1', b'1', 'get', '页面', '/ene002', '14200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14202', '0', '', b'1', b'0', 'put', '修改', '/ene002/metertasks', '14200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14203', '0', '', b'1', b'0', 'post', '抄表录入', '/ene002/metertasks/save', '14200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14204', '0', '', b'1', b'1', 'get', '获取', '/ene002/meterread/*', '14200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14205', '0', '', b'1', b'0', 'delete', '删除', '/ene002/metertasks/*', '14200');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('14301', '0', '', b'1', b'1', 'get', '页面', '/ene003', '14300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method, name, path,menu_id) VALUES ('14302', '0', '', b'1', b'0', 'post', '查询能源报表曲线', '/ene003/report/*', 14300);
-- --- neergys END----

-- --- sign----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16101', '0', '', b'1', b'1', 'get', '员工在岗查询', '/sign001', '16100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16102', '0', '', b'1', b'0', 'post', '查看员工在岗状态', '/sign001/signedInQuery/table', '16100');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16201', '0', '', b'1', b'1', 'get', '签到记录查询', '/sign002', '16200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16202', '0', '', b'1', b'0', 'post', '查看签到记录', '/sign002/signRecordQuery/table', '16200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16203', '0', '', b'1', b'0', 'post', '导出', '/sign002/signRecords/export', '16200');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16301', '0', '', b'1', b'1', 'get', '签到人员', '/sign003', '16310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16302', '0', '', b'1', b'0', 'post', '查看签到人员', '/sign003/signPersonnel/table', '16310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16303', '0', '', b'1', b'1', 'get', '获取签到管理员', '/sign003/admin/', '16310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16304', '0', '', b'1', b'0', 'post', '设置签到管理员', '/sign003/admins/update', '16310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16305', '0', '', b'1', b'0', 'post', '添加签到人员', '/sign003/emp/add', '16310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16306', '0', '', b'1', b'0', 'delete', '删除签到人员', '/sign003/personnel/delete/*', '16310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16307', '0', '', b'1', b'1', 'post', '查看未设置签到的人员', '/sign003/emp/table', '16310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16308', '0', '', b'1', b'0', 'delete', '批量删除签到人员', '/sign003/personnel/delete', '16310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16309', '0', '', b'1', b'1', 'post', '检查被添加管理员是否存在', '/sign003/emp/check', '16310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16310', '0', '', b'1', b'1', 'post', '检查被添加签到人员是否存在', '/sign003/admins/check', '16310');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16401', '0', '', b'1', b'1', 'get', '签到方式', '/sign004', '16320');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16402', '0', '', b'1', b'1', 'get', '获取当前有效范围', '/sign004/range', '16320');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16403', '0', '', b'1', b'1', 'put', '更新有效范围', '/sign004/range/*', '16320');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16404', '0', '', b'1', b'0', 'post', '查看签到方式', '/sign004/signMode/table/*', '16320');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16405', '0', '', b'1', b'1', 'post', '检查签到方式是否已存在', '/sign004/signMode/exist', '16320');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16406', '0', '', b'1', b'0', 'post', '编辑地理位置', '/sign004/signMode/loc/*', '16320');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16407', '0', '', b'1', b'0', 'post', '编辑Wi-Fi，蓝牙', '/sign004/signMode/*', '16320');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16408', '0', '', b'1', b'0', 'post', '重设考勤方式的状态', '/sign004/signMode/changeState/*', '16320');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('16409', '0', '', b'1', b'0', 'delete', '删除考勤方式', '/sign004/signMode/delete/*', '16320');

-- --- sign END----

-- --- bulletin----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17101', '0', '', b'1', b'1', 'get', '创建公告页面权限', '/bul001', '17100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17102', '0', '', b'1', b'0', 'post', '创建公告', '/bul001/create', '17100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17103', '0', '', b'1', b'1', 'post', '获取并选择通知人', '/bul001/person/*', '17100');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17201', '0', '', b'1', b'1', 'get', '历史公告页面权限', '/bul002', '17200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17202', '0', '', b'1', b'1', 'post', '获取历史公告列表', '/bul002/list/all', '17200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17203', '0', '', b'1', b'0', 'put', '修改公告或操作置顶', '/bul002/**', '17200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17204', '0', '', b'1', b'0', 'delete', '删除历史公告', '/bul002/bulletin/*', '17200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17205', '0', '', b'1', b'1', 'get', '查看或下载附件和图片', '/bul002/files/*', '17200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17206', '0', '', b'1', b'0', 'post', '查看公告详情', '/bul002/detail/*', '17200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17207', '0', '', b'1', b'1', 'post', '查看可编辑的公告详情', '/bul002/edit/detail/*', '17200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17208', '0', '', b'1', b'1', 'get', '获取已读未读人员信息', '/bul003/readstatus/**', '17200');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17301', '0', '', b'1', b'1', 'get', '页面权限', '/bul003', '17300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17302', '0', '', b'1', b'0', 'post', '获取我的公告列表', '/bul003/list/my', '17300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17303', '0', '', b'1', b'0', 'post', '获取公告详情', '/bul003/detail/*', '17300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17304', '0', '', b'1', b'0', 'get', '查看或下载附件和图片(我的公告)', '/bul002/files/*', '17300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17305', '0', '', b'1', b'1', 'get', '获取已读未读人员信息', '/bul003/readstatus/**', '17300');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17401', '0', '', b'1', b'1', 'get', '创建公告页面权限(项目主页)', '/bul001', '17400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17402', '0', '', b'1', b'0', 'post', '创建公告(项目主页)', '/bul001/create', '17400');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17403', '0', '', b'1', b'1', 'post', '获取并选择通知人(项目主页)', '/bul001/person/*', '17400');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17501', '0', '', b'1', b'1', 'get', '历史公告页面权限(项目主页)', '/bul002', '17500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17502', '0', '', b'1', b'1', 'post', '获取历史公告列表(项目主页)', '/bul002/list/all', '17500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17503', '0', '', b'1', b'0', 'put', '修改公告或操作置顶(项目主页)', '/bul002/**', '17500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17504', '0', '', b'1', b'0', 'delete', '删除历史公告(项目主页)', '/bul002/bulletin/*', '17500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17505', '0', '', b'1', b'1', 'get', '查看或下载附件和图片(项目主页)', '/bul002/files/*', '17500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17506', '0', '', b'1', b'0', 'post', '查看公告详情(项目主页)', '/bul002/detail/*', '17500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17507', '0', '', b'1', b'1', 'post', '查看可编辑的公告详情(项目主页)', '/bul002/edit/detail/*', '17500');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('17508', '0', '', b'1', b'1', 'get', '获取已读未读人员信息', '/bul003/readstatus/**', '17500');
-- --- bulletin END----

-- --- contract----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18101', '0', '', b'1', b'1', 'get', '合同分类', '/contract001', '18110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18102', '0', '', b'1', b'0', 'post', '添加合同分类', '/contract001/contractType', '18110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18103', '0', '', b'1', b'0', 'put', '修改合同分类', '/contract001/contractType', '18110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18104', '0', '', b'1', b'0', 'delete', '删除合同分类', '/contract001/contractType/*', '18110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18105', '0', '', b'1', b'0', 'post', '添加分类字段', '/contract001/customField', '18110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18106', '0', '', b'1', b'0', 'put', '修改分类字段', '/contract001/customField', '18110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18107', '0', '', b'1', b'0', 'delete', '删除分类字段', '/contract001/customField/*', '18110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18108', '0', '', b'1', b'1', 'post', '获取合同分类自定义字段列表', '/contract001/customField/contractType/*', '18110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18109', '0', '', b'1', b'1', 'get', '获取合同分类自定义字段详情', '/contract001/customField/*/selects', '18110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18110', '0', '', b'1', b'1', 'get', '验证合同分类名称唯一性', '/contract001/validateTypeName', '18110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18111', '0', '', b'1', b'1', 'get', '验证合同分类自定义字段名称唯一性', '/contract001/validateCustomFieldName', '18110');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18201', '0', '', b'1', b'1', 'get', '合同其他设置', '/contract002', '18120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18202', '0', '', b'1', b'1', 'get', '获取合同其他设置', '/contract002/setting', '18120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18203', '0', '', b'1', b'1', 'put', '更新合同其他设置', '/contract002/setting', '18120');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18301', '0', '', b'1', b'1', 'get', '合同管理', '/contract003', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18302', '0', '', b'1', b'1', 'get', '获取各状态合同的个数', '/contract003/statusNums', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18303', '0', '', b'1', b'0', 'post', '合同查询', '/contract003/contract/query', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18304', '0', '', b'1', b'0', 'post', '导出合同', '/contract003/contract/export', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18305', '0', '', b'1', b'0', 'post', '删除合同', '/contract003/contract/delete', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18306', '0', '', b'1', b'1', 'get', '获取合同详情', '/contract003/contract/details/*', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18307', '0', '', b'1', b'0', 'put', '更新合同', '/contract003/contract', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18308', '0', '', b'1', b'1', 'post', '获取合同待绑定的设备', '/contract003/toBeBindingEquipment', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18309', '0', '', b'1', b'1', 'post', '获取选择的待绑定的设备的详情', '/contract003/equipment/binding', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18310', '0', '', b'1', b'0', 'post', '验证合同', '/contract003/contract/validate', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18311', '0', '', b'1', b'0', 'post', '终止合同', '/contract003/contract/terminate', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18312', '0', '', b'1', b'0', 'put', '恢复合同', '/contract003/contract/recovery/*', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18313', '0', '', b'1', b'0', 'put', '存档合同', '/contract003/contract/close/*', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18314', '0', '', b'1', b'0', 'get', '打印合同', '/contract003/contract/print/*', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18315', '0', '', b'1', b'0', 'post', '创建合同', '/contract003/contract', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18316', '0', '', b'1', b'1', 'get', '根据合同分类获取自定义字段', '/contract003/contractTypeCustomField/*', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18317', '0', '', b'1', b'1', 'get', '获取业务员的部门', '/contract003/emp/*', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18318', '0', '', b'1', b'1', 'get', '获取供应商联系人和电话', '/contract003/vendor/*', '18200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18319', '0', '', b'1', b'1', 'post', '验证合同编码存在性', '/contract003/checkCode', '18200');


INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18401', '0', '', b'1', b'1', 'get', '页面权限', '/contract004', '18300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18402', '0', '', b'1', b'1', 'post', '表格展示', '/contract004/remind', '18300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18403', '0', '', b'1', b'1', 'put', '全部标记为已读', '/contract004/remind/markRead', '18300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18404', '0', '', b'1', b'1', 'put', '单个标记为已读', '/contract004/remind/markRead/*', '18300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18405', '0', '', b'1', b'1', 'get', '合同详情', '/contract004/contract/detail/*', '18300');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('18406', '0', '', b'1', b'1', 'get', '获取合同其他设置', '/contract002/setting', '18300');

INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (18501, 0, '', b'1', b'1', 'get', '页面', '/contract005', 18400);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (18502, 0, '', b'1', b'1', 'get', '报表数据', '/contract005/contracts/echarts', 18400);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (18503, 0, '', b'1', b'1', 'post', '获取合同分类', '/contract005/contracts/types', 18400);
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES (18504, 0, '', b'1', b'1', 'get', '分类汇总数据', '/contract005/contracts/summary', 18400);
-- --- contract END----

-- --- vendor----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('19101', '0', '', b'1', b'1', 'get', '页面权限', '/vendor001', '19100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('19102', '0', '', b'1', b'1', 'post', '供应商列表', '/vendor001/suppliers', '19100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('19103', '0', '', b'1', b'0', 'post', '添加供应商', '/vendor001/vendors', '19100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('19104', '0', '', b'1', b'1', 'get', '获取供应商信息', '/vendor001/vendor/*', '19100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('19105', '0', '', b'1', b'0', 'put', '修改供应商', '/vendor001/vendor', '19100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('19106', '0', '', b'1', b'0', 'delete', '删除供应商', '/vendor001/vendors', '19100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('19107', '0', '', b'1', b'0', 'post', '导出供应商', '/vendor001/vendors/export', '19100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('19108', '0', '', b'1', b'0', 'post', '导入供应商', '/vendor001/vendors/inc', '19100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('19109', '0', '', b'1', b'0', 'post', '下载供应商模板', '/vendor001/vendors/template', '19100');
-- --- vendor END----

-- --- Visitor Management----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('23101', '0', '', b'1', b'1', 'get', '访客登记', '/visitor001', '23100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('23102', '0', '', b'1', b'1', 'post', '创建访客登记', '/visitor001/register', '23100');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('23201', '0', '', b'1', b'1', 'get', '访客记录', '/visitor002', '23200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('23202', '0', '', b'1', b'1', 'post', '获取访客记录列表', '/visitor002/record/query', '23200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('23203', '0', '', b'1', b'1', 'get', '获取访客记录详情', '/visitor002/record/detail/*', '23200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('23204', '0', '', b'1', b'0', 'put', '关闭访客记录', '/visitor002/record/close/*', '23200');
-- --- monitoring----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21111', '0', '', b'1', b'1', 'get', '页面权限', '/monitoring001', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21112', '0', '', b'1', b'1', 'get', '获取最新同步结果', '/monitoring001/sync/*', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21113', '0', '', b'1', b'1', 'post', '获取传感器列表', '/monitoring001/table', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21114', '0', '', b'1', b'0', 'get', '同步传感器基础数据', '/monitoring001/sync', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21115', '0', '', b'1', b'1', 'get', '获取传感器详情', '/monitoring001/device/*', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21116', '0', '', b'1', b'1', 'post', '获取参数列表', '/monitoring001/table/params', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21117', '0', '', b'1', b'1', 'get', '获取提醒人备选值', '/monitoring001/notifiers', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21118', '0', '', b'1', b'1', 'post', '获取SLA备选列表', '/monitoring001/sla', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21119', '0', '', b'1', b'0', 'put', '保存传感器配置', '/monitoring001', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21120', '0', '', b'1', b'1', 'get', '根据设备编码获取设备', '/monitoring001/eq/*', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21124', '0', '', b'1', b'1', 'get', '获取设置传感器参数的权限', '/monitoring001/set/permission', '21110');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21121', '0', '', b'1', b'1', 'get', '页面权限', '/monitoring002', '21120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21122', '0', '', b'1', b'1', 'get', '获取系统设置', '/monitoring002/setting', '21120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21123', '0', '', b'1', b'0', 'put', '更新系统设置', '/monitoring002/setting', '21120');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21201', '0', '', b'1', b'1', 'get', '页面权限', '/monitoring003', '21200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21202', '0', '', b'1', b'1', 'get', '获取最近更新时间', '/monitoring003/recentUpdateTime', '21200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21203', '0', '', b'1', b'1', 'get', '检查工单是否存在', '/monitoring003/wo/*', '21200');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21311', '0', '', b'1', b'1', 'get', '页面权限', '/monitoring004', '21310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21312', '0', '', b'1', b'1', 'get', '获取系统设置', '/monitoring002/setting', '21310');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21331', '0', '', b'1', b'1', 'get', '页面权限', '/monitoring006', '21330');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21332', '0', '', b'1', b'1', 'get', '获取系统设置', '/monitoring002/setting', '21330');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21341', '0', '', b'1', b'1', 'get', '页面权限', '/monitoring007', '21340');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21342', '0', '', b'1', b'1', 'get', '获取系统设置', '/monitoring002/setting', '21340');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21351', '0', '', b'1', b'1', 'get', '页面权限', '/monitoring008', '21350');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('21352', '0', '', b'1', b'1', 'get', '获取系统设置', '/monitoring002/setting', '21350');
-- --- monitoring END----

-- --- business statistic ----
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('33601', '0', '', b'1', b'1', 'get', '月度运营统计', '/bus006', '33600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('33602', '0', '', b'1', b'1', 'post', '查看月度运营统计报表', '/bus006/**', '33600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('33603', '0', '', b'1', b'1', 'get', '查看月度运营统计报表', '/bus006/**', '33600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('33604', '0', '', b'1', b'1', 'get', '月度运营统计', '/bus007', '33600');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('33605', '0', '', b'1', b'1', 'get', '需求统计', '/bus001', '33601');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('33606', '0', '', b'1', b'1', 'get', '工单统计', '/bus002', '33602');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('33607', '0', '', b'1', b'1', 'get', '巡检统计', '/bus003', '33603');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('33608', '0', '', b'1', b'1', 'get', '计划性维护统计', '/bus004', '33604');
INSERT INTO `sys_permission` (id,version,description,enabled,is_hide,method,`name`,path,menu_id) VALUES ('33609', '0', '', b'1', b'1', 'get', '设备统计', '/bus005', '33605');

-- --- undertake ---
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24100', '0', '', b'1', b'1', 'get', '承接查验页面', '/under001', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24101', '0', '', b'1', b'0', 'post', '承接查验新增', '/under001/undertake', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24102', '0', '', b'1', b'1', 'post', '查验任务列表', '/under001/undertake/table', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24103', '0', '', b'1', b'1', 'get', '查验任务详情', '/under001/undertake/{id}', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24104', '0', '', b'1', b'1', 'post', '获取设备', '/under001/undertake/equipment*', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24105', '0', '', b'1', b'1', 'post', '获取位置', '/under001/undertake/space', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24106', '0', '', b'1', b'1', 'post', '获取sla', '/under001/undertake/sla', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24107', '0', '', b'1', b'1', 'post', '获取查验位置', '/under001/undertake/space/table/*', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24108', '0', '', b'1', b'1', 'post', '获取查验设备', '/under001/undertake/equipment/table/*', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24109', '0', '', b'1', b'1', 'post', '获取工作组人员', '/under001/undertake/workteam/employees', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24110', '0', '', b'1', b'1', 'post', '获取内容列表', '/under001/undertake/content/**', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24111', '0', '', b'1', b'1', 'post', '获取验收记录列表', '/under001/undertake/accept/activity/*', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24112', '0', '', b'1', b'1', 'get', '根据id 表名称 类型获得图片信息', '/under001/undertake/doc/**', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24113', '0', '', b'1', b'1', 'get', '查验模板页面', '/under002', '24200');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24114', '0', '', b'1', b'1', 'post', '查验模板列表', '/under002/templates', '24200');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24115', '0', '', b'1', b'1', 'get', '查验模板详情列表', '/under002/template/*', '24200');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24116', '0', '', b'1', b'0', 'put', '新增查验模板', '/under002/template/save', '24200');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24117', '0', '', b'1', b'0', 'delete', '删除模板', '/under002/template/*', '24200');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24118', '0', '', b'1', b'0', 'post', '下载模板', '/under002/upload/template', '24200');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24119', '0', '', b'1', b'0', 'post', '导入', '/under002/template/import/*', '24200');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24120', '0', '', b'1', b'1', 'post', '导出', '/under002/template/export', '24200');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24121', '0', '', b'1', b'1', 'post', '同名检查', '/under002/checkName', '24200');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24122', '0', '', b'1', b'1', 'get', '获取工作组', '/org001/workteams/proWorkTeam', '24100');
insert into `sys_permission` ( `id`, `version`, `description`, `enabled`, `is_hide`, `method`, `name`, `path`, `menu_id`) values ( '24123', '0', '', b'1', b'1', 'get', '获取员工', '/org002/employees/proWorkers', '24100');

-- ----------------------------
-- sys_role_menu
-- ----------------------------
-- insert into `sys_role_menu`(role_id,menu_id) select 1,id from sys_menu;

-- ----------------------------
-- sys_role_permission
-- ----------------------------
-- insert into `sys_role_permission`(role_id,permission_id) select 1,id from sys_permission where menu_id is not null;

-- ----------------------------
-- unit
-- ----------------------------

alter table `unit` change symbol symbol varchar(20) binary;
-- -- 长度
-- 公制 长度
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(1,'Length','km','千米','kilometre','千米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(2,'Length','m','米','metre','米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(3,'Length','dm','分米','decimetre','分米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(4,'Length','cm','厘米','centimetre','厘米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(5,'Length','mm','毫米','millimetre','毫米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(6,'Length','µm','微米','millimetre','微米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(7,'Length','nm','纳米','nanometre','纳米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(8,'Length','fm','飞米','femtometre','飞米','');
-- 英制 长度
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(9,'Length','in','英寸','inch','英寸','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(10,'Length','ft','英尺','foot','英尺','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(11,'Length','yd','码','yard','码','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(12,'Length','mi','英里','mile','英里','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(13,'Length','nmi','海里','nautical mile','海里','');

-- -- 面积
-- 公制 面积
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(21,'Area','km²','平方千米','square kilometre','平方千米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(22,'Area','ha','公顷','hectare','公顷','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(23,'Area','a','亩','mu','亩','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(24,'Area','m²','平方米','square metre','平方米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(25,'Area','dm²','平方分米','square decimetre','平方分米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(26,'Area','cm²','平方厘米','square centimetre','平方厘米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(27,'Area','mm²','平方毫米','square millimetre','平方毫米','');
-- 英制 面积
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(28,'Area','in²','平方英寸','square inch','平方英寸','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(29,'Area','ft²','平方英尺','square foot','平方英尺','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(30,'Area','yd²','平方码','square yard','平方码','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(31,'Area','mi²','平方英里','square mile','平方英里','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(32,'Area','nmi²','平方海里','square nautical mile','平方海里','');

-- -- 体积、容量
-- 公制 体积、容量
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(41,'Volume','m³','立方米','cubic meter','立方米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(42,'Volume','dm³','立方分米','decaliter','立方分米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(43,'Volume','cm³','立方厘米','cubic centimetre','立方厘米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(44,'Volume','mm³','立方毫米','cubic millimeter','立方毫米','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(45,'Volume','L','升','litre','升','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(46,'Volume','dL','分升','decilitre','分升','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(47,'Volume','mL','毫升','millilitre','毫升','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(48,'Volume','cL','厘升','centilitre','厘升','');
-- 英制 体积、容量
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(49,'Volume','in³','立方英寸','cubic inch','立方英寸','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(50,'Volume','ft³','立方英尺','cubic foot','立方英尺','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(51,'Volume','yd³','立方码','cubic yard','立方码','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(52,'Volume','gallon_uk','英制加仑','UK gallon','英制加仑','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(53,'Volume','oz_uk','英制液量盎司','UK fluid ounces','英制液量盎司','');
-- 美制 体积、容量
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(54,'Volume','gal','美制加仑','US gallon','美制加仑','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(55,'Volume','oz-us','美制液量盎司','US fluid ounces','美制液量盎司','');

-- -- 质量
-- 公制
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(61,'Mass','kg','千克','kilogram','千克','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(62,'Mass','g','克','gram','克','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(63,'Mass','mg','毫克','milligram','毫克','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(64,'Mass','t','吨','ton','吨','');
-- 英制
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(65,'Mass','lb','磅','','磅','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(66,'Mass','oz','盎司','','盎司','');

-- -- 温度
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(81,'Temperature','°C','摄氏度','celsius degree','摄氏度','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(82,'Temperature','°F','华氏度','fahrenheit degree','华氏度','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(83,'Temperature','°R','兰氏度','gram degree','兰氏度','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(84,'Temperature','K','开氏度','kelvin degree','开氏度','');

-- -- 压力
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(91,'Pressure','Pa','帕斯卡','pascal','帕斯卡','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(92,'Pressure','kPa','千帕','kilopascal','千帕','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(93,'Pressure','hPa','百帕','hectopascal','百帕','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(94,'Pressure','atm','标准大气压','atmosphere','标准大气压','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(95,'Pressure','mmHg','毫米汞柱','millimeter of mercury','毫米汞柱','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(96,'Pressure','inHg','英寸汞柱','inch of mercury','英寸汞柱','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(97,'Pressure','bar','巴','bar','巴','');

-- -- 功率
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(111,'Power','W','瓦','watt','瓦','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(112,'Power','kW','千瓦','kilowatt','千瓦','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(113,'Power','hp','英制马力','horsepower','英制马力','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(114,'Power','J/s','焦耳/秒','joules/sec','焦耳/秒','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(115,'Power','N·m/s','牛顿·米/秒','newton·meters/sec','牛顿·米/秒','');

-- -- 功/能/热 （BTU,CAL缺失）
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(121,'Energy','J','焦耳','joule','焦耳','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(122,'Energy','kW·h','千瓦·时','Kilowatt·hour','千瓦·时','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(123,'Energy','hp·h','英制马力·时','horsepower·hour','英制马力·时','');

-- -- 力
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(141,'Force','N','牛','newton','牛','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(142,'Force','kN','千牛','kilonewton','千牛','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(143,'Force','kgf','千克力','Kilogram force','千克力','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(144,'Force','lbf','磅力','pounds of force','磅力','');

-- -- 速度
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(161,'Force','m/s','米/秒','m/sec','米/秒','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(162,'Force','km/s','千米/秒','km/sec','千米/秒','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(163,'Force','km/h','千米/时','km/hour','千米/时','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(164,'Force','in/s','英寸/秒','inch/sec','英寸/秒','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(165,'Force','mi/h','英里/时','mile/hour','英里/时','');

-- -- 电压
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(171,'ElectricPotential','MV','兆伏','megavolt','兆伏','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(172,'ElectricPotential','kV','千伏','kilovolt','千伏','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(173,'ElectricPotential','V','伏特','volt','伏特','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(174,'ElectricPotential','mV','毫伏','millivolt','毫伏','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(175,'ElectricPotential','µV','微伏','microvolt','微伏','');

-- -- 电流
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(181,'ElectricCurrent','kA','千安','kiloampere','千安','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(182,'ElectricCurrent','A','安培','ampere','安培','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(183,'ElectricCurrent','mA','毫安','milliampere','微安','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(184,'ElectricCurrent','µA','微安','microampere','微安','');

-- -- 欧姆
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(191,'ElectricResistance','MΩ','兆欧','megohm','兆欧','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(192,'ElectricResistance','kΩ','千欧','kilo-ohm','千欧','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(193,'ElectricResistance','Ω','欧姆','ohm','欧姆','');

-- -- 频率
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(201,'Frequency','GHz','吉赫','','吉赫','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(202,'Frequency','MHz','兆赫','','兆赫','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(203,'Frequency','kHz','千赫','','千赫','');
insert into `unit` (`unit_id`,`type`,`symbol`,`name`,`name_en`,`name_zh`,`description`) VALUES(204,'Frequency','Hz','赫兹','','赫兹','');


-- ----------------------------
-- province
-- ----------------------------
insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(1,'北京','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,11,'东城区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,12,'西城区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,13,'海淀区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,14,'朝阳区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,15,'丰台区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,16,'石景山区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,17,'门头沟区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,18,'房山区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,19,'通州区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,110,'顺义区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,111,'昌平区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,112,'大兴区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,113,'怀柔区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,114,'平谷区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,115,'密云县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(1,116,'延庆县',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(2,'天津','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,21,'和平区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,22,'河东区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,23,'河西区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,24,'南开区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,25,'河北区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,26,'红桥区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,27,'塘沽区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,28,'汉沽区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,29,'大港区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,210,'东丽区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,211,'西青区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,212,'津南区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,213,'北辰区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,214,'武清区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,215,'宝坻区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,216,'滨海新区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,217,'宁河县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,218,'静海县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(2,219,'蓟县',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(3,'上海','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,31,'黄浦区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,32,'卢湾区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,33,'徐汇区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,34,'长宁区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,35,'静安区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,36,'普陀区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,37,'闸北区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,38,'虹口区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,39,'杨浦区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,310,'闵行区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,311,'宝山区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,312,'嘉定区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,313,'浦东新区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,314,'金山区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,315,'松江区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,316,'青浦区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,317,'南汇区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,318,'奉贤区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(3,319,'崇明县',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(4,'重庆','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,41,'万州区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,42,'涪陵区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,43,'渝中区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,44,'大渡口区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,45,'江北区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,46,'沙坪坝区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,47,'九龙坡区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,48,'南岸区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,49,'北碚区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,410,'万盛区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,411,'双桥区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,412,'渝北区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,413,'巴南区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,414,'黔江区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,415,'长寿区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,416,'綦江区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,417,'潼南县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,418,'铜梁区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,419,'大足区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,420,'荣昌县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,421,'璧山区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,422,'梁平县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,423,'城口县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,424,'丰都县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,425,'垫江县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,426,'武隆县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,427,'忠县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,428,'开县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,429,'云阳县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,430,'奉节县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,431,'巫山县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,432,'巫溪县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,433,'石柱土家族自治县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,434,'秀山土家族苗族自治县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,435,'酉阳土家族苗族自治县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,436,'彭水苗族土家族自治县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,437,'江津区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,438,'合川区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,439,'永川区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(4,440,'南川区',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(5,'河北','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,51,'石家庄市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,52,'唐山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,53,'秦皇岛市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,54,'邯郸市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,55,'邢台市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,56,'保定市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,57,'张家口市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,58,'承德市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,59,'沧州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,510,'廊坊市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(5,511,'衡水市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(6,'河南','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,61,'郑州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,62,'开封市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,63,'洛阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,64,'平顶山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,65,'安阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,66,'鹤壁市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,67,'新乡市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,68,'焦作市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,69,'濮阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,610,'许昌市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,611,'漯河市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,612,'三门峡市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,613,'南阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,614,'商丘市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,615,'信阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,616,'周口市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,617,'驻马店市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(6,618,'济源市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(7,'云南','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,71,'昆明市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,72,'曲靖市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,73,'玉溪市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,74,'保山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,75,'昭通市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,76,'楚雄彝族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,77,'红河哈尼族彝族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,78,'文山壮族苗族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,79,'思茅市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,710,'西双版纳傣族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,711,'大理白族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,712,'德宏傣族景颇族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,713,'丽江市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,714,'怒江傈僳族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,715,'迪庆藏族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(7,716,'临沧市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(8,'辽宁','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,81,'沈阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,82,'大连市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,83,'鞍山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,84,'抚顺市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,85,'本溪市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,86,'丹东市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,87,'锦州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,88,'营口市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,89,'阜新市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,810,'辽阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,811,'盘锦市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,812,'铁岭市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,813,'朝阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(8,814,'葫芦岛市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(9,'黑龙江','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,91,'哈尔滨市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,92,'齐齐哈尔市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,93,'鸡西市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,94,'鹤岗市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,95,'双鸭山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,96,'大庆市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,97,'伊春市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,98,'佳木斯市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,99,'七台河市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,910,'牡丹江市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,911,'黑河市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,912,'绥化市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(9,913,'大兴安岭地区',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(10,'湖南','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,101,'长沙市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,102,'株洲市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,103,'湘潭市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,104,'衡阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,105,'邵阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,106,'岳阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,107,'常德市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,108,'张家界市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,109,'益阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,1010,'郴州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,1011,'永州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,1012,'怀化市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,1013,'娄底市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(10,1014,'湘西土家族苗族自治州',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(11,'安徽','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1101,'合肥市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1102,'芜湖市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1103,'蚌埠市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1104,'淮南市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1105,'马鞍山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1106,'淮北市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1107,'铜陵市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1108,'安庆市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1109,'黄山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1110,'滁州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1111,'阜阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1112,'宿州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1113,'巢湖市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1114,'六安市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1115,'亳州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1116,'池州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(11,1117,'宣城市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(12,'山东','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,121,'济南市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,122,'青岛市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,123,'淄博市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,124,'枣庄市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,125,'东营市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,126,'烟台市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,127,'潍坊市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,128,'济宁市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,129,'泰安市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,1210,'威海市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,1211,'日照市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,1212,'莱芜市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,1213,'临沂市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,1214,'德州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,1215,'聊城市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,1216,'滨州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(12,1217,'菏泽市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(13,'新疆','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,131,'乌鲁木齐市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,132,'克拉玛依市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,133,'西河子市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,134,'阿拉尔市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,135,'图木舒克市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,136,'五家渠市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,137,'北屯市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,138,'铁门关市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,139,'双河市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1310,'吐鲁番地区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1311,'哈密地区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1312,'昌吉回族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1313,'博尔塔拉蒙古自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1314,'巴音郭楞蒙古自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1315,'阿克苏地区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1316,'克孜勒苏柯尔克孜自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1317,'喀什地区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1318,'和田地区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1319,'伊犁哈萨克自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1320,'塔城地区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(13,1321,'阿勒泰地区',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(14,'江苏','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,141,'南京市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,142,'无锡市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,143,'徐州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,144,'常州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,145,'苏州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,146,'南通市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,147,'连云港市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,148,'淮安市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,149,'盐城市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,1410,'扬州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,1411,'镇江市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,1412,'泰州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(14,1413,'宿迁市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(15,'浙江','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,151,'杭州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,152,'宁波市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,153,'温州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,154,'嘉兴市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,155,'湖州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,156,'绍兴市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,157,'金华市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,158,'衢州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,159,'舟山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,1510,'台州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(15,1511,'丽水市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(16,'江西','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,161,'南昌市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,162,'景德镇市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,163,'萍乡市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,164,'九江市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,165,'新余市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,166,'鹰潭市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,167,'赣州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,168,'吉安市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,169,'宜春市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,1610,'抚州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(16,1611,'上饶市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(17,'湖北','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,171,'武汉市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,172,'黄石市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,173,'十堰市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,174,'宜昌市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,175,'襄阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,176,'鄂州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,177,'荆门市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,178,'孝感市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,179,'荆州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,1710,'黄冈市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,1711,'咸宁市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,1712,'随州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,1713,'仙桃市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,1714,'潜江市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,1715,'天门市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,1716,'神农架林区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(17,1717,'恩施土家族苗族自治州',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(18,'广西','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,181,'南宁市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,182,'柳州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,183,'桂林市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,184,'梧州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,185,'北海市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,186,'防城港市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,187,'钦州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,188,'贵港市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,189,'玉林市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,1810,'百色市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,1811,'贺州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,1812,'河池市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,1813,'南宁市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(18,1814,'崇左市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(19,'甘肃','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,191,'兰州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,192,'嘉峪关市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,193,'金昌市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,194,'白银市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,195,'天水市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,196,'武威市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,197,'张掖市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,198,'平凉市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,199,'酒泉市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,1910,'庆阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,1911,'定西市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,1912,'陇南市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,1913,'临夏回族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(19,1914,'甘南藏族自治州',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(20,'山西','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,201,'太原市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,202,'大同市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,203,'阳泉市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,204,'长治市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,205,'晋城市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,206,'朔州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,207,'晋中市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,208,'运城市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,209,'忻州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,2010,'临汾市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(20,2011,'吕梁市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(21,'内蒙古','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2101,'呼和浩特市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2102,'包头市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2103,'乌海市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2104,'赤峰市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2105,'通辽市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2106,'鄂尔多斯市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2107,'呼伦贝尔市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2108,'兴安盟',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2109,'锡林郭勒盟',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2110,'乌兰察布市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2111,'巴彦淖尔市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(21,2112,'阿拉善盟',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(22,'陕西','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(22,221,'西安市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(22,222,'铜川市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(22,223,'宝鸡市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(22,224,'咸阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(22,225,'渭南市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(22,226,'延安市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(22,227,'汉中市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(22,228,'榆林市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(22,229,'安康市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(22,2210,'商洛市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(23,'吉林','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(23,231,'长春市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(23,232,'吉林市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(23,233,'四平市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(23,234,'辽源市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(23,235,'通化市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(23,236,'白山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(23,237,'松原市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(23,238,'白城市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(23,239,'延边朝鲜族自治州',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(24,'福建','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(24,241,'福州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(24,242,'厦门市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(24,243,'莆田市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(24,244,'三明市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(24,245,'泉州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(24,246,'漳州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(24,247,'南平市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(24,248,'龙岩市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(24,249,'宁德市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(25,'贵州','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(25,251,'贵阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(25,252,'六盘水市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(25,253,'遵义市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(25,254,'安顺市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(25,255,'铜仁市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(25,256,'黔西南布依族苗族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(25,257,'毕节市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(25,258,'黔东南苗族侗族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(25,259,'黔南布依族苗族自治州',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(26,'广东','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,261,'广州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,262,'韶关市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,263,'深圳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,264,'珠海市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,265,'汕头市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,266,'佛山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,267,'江门市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,268,'湛江市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,269,'茂名市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2610,'肇庆市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2611,'惠州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2612,'梅州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2613,'汕尾市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2614,'河源市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2615,'阳江市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2616,'清远市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2617,'东莞市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2618,'中山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2619,'潮州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2620,'揭阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(26,2621,'云浮市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(27,'青海','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(27,271,'西宁市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(27,272,'海东市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(27,273,'海北藏族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(27,274,'黄南藏族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(27,275,'海南藏族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(27,276,'果洛藏族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(27,277,'玉树藏族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(27,278,'海西蒙古族藏族自治州',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(28,'西藏','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(28,281,'拉萨市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(28,282,'昌都市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(28,283,'山南地区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(28,284,'日喀则市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(28,285,'那曲地区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(28,286,'阿里地区',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(28,287,'林芝地区',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(29,'四川','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,291,'成都市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,292,'自贡市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,293,'攀枝花市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,294,'泸州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,295,'德阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,296,'绵阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,297,'广元市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,298,'遂宁市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,299,'内江市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2910,'乐山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2911,'南充市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2912,'眉山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2913,'宜宾市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2914,'广安市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2915,'达州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2916,'雅安市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2917,'巴中市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2918,'资阳市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2919,'阿坝藏族羌族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2920,'甘孜藏族自治州',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(29,2921,'凉山彝族自治州',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(30,'宁夏','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(30,301,'银川市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(30,302,'石嘴山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(30,303,'吴忠市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(30,304,'固原市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(30,305,'中卫市',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(31,'海南','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3101,'海口市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3102,'三亚市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3103,'三沙市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3104,'五指山市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3105,'琼海市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3106,'儋州市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3107,'文昌市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3108,'万宁市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3109,'东方市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3110,'安定市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3111,'屯昌市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3112,'澄迈市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3113,'临高市',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3114,'白沙黎族自治县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3115,'昌江黎族自治县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3116,'乐东黎族自治县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3117,'陵水黎族自治县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3118,'保亭黎族苗族自治县',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(31,3119,'琼中黎族苗族自治县',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(32,'台湾','','');

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(33,'香港','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,331,'中西區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,332,'灣仔區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,333,'東區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,334,'南區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,335,'油尖旺區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,336,'深水埗區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,337,'九龍城區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,338,'黃大仙區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,339,'觀塘區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,3310,'荃灣區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,3311,'屯門區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,3312,'元朗區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,3313,'北區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,3314,'大埔區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,3315,'西貢區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,3316,'沙田區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,3317,'葵青區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(33,3318,'離島區',8);

insert into `geo_province` (`province_id`,`province_name`,`latitude`,`longitude`) VALUES(34,'澳门','','');
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(34,341,'花地瑪堂區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(34,342,'花王堂區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(34,343,'望德堂區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(34,344,'大堂區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(34,345,'風順堂區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(34,346,'嘉模堂區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(34,347,'路氹區',8);
insert into `geo_city` (`province_id`,`city_id`,`city_name`,`timezone`) VALUES(34,348,'聖方濟各堂區',8);

-- ----------------------------
-- message_template
-- ----------------------------

-- change-password
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('1', '0', b'0','0', '尊敬的$!{change-password-user-name}用户，您设置的新密码为：$!{change-password-password}', 'Dear $!{change-password-user-name}, you new password is : $!{change-password-password}', null, '', 'sms', '尊敬的$!{change-password-user-name}用户，您设置的新密码为：$!{change-password-password}', null, (select id from message_template_group where code='change-password'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('2', '0', b'0','0', '尊敬的$!{change-password-user-name}用户，您设置的新密码为：$!{change-password-password}', 'Dear $!{change-password-user-name}, you new password is : $!{change-password-password}', null, '', 'site', '尊敬的$!{change-password-user-name}用户，您设置的新密码为：$!{change-password-password}', null, (select id from message_template_group where code='change-password'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('3', '0', b'0','0', '尊敬的$!{change-password-user-name}用户，您设置的新密码为：$!{change-password-password}', 'Dear $!{change-password-user-name}, you new password is : $!{change-password-password}', null, '', 'mpush', '尊敬的$!{change-password-user-name}用户，您设置的新密码为：$!{change-password-password}', null, (select id from message_template_group where code='change-password'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('4', '1', b'0','0', '尊敬的$!{change-password-user-name}用户，<blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><div>您设置的新密码为：$!{change-password-password}</div></blockquote>', 'Dear $!{change-password-user-name},&nbsp;<blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><div>you new password is : $!{change-password-password}</div></blockquote>', 'Reset Password', '重置密码', 'email', '尊敬的$!{change-password-user-name}用户，<blockquote style=\"margin: 0 0 0 40px; border: none; padding: 0px;\"><div>您设置的新密码为：$!{change-password-password}</div></blockquote>', '重置密码', (select id from message_template_group where code='change-password'));

-- patrol-remind
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('31', '2', b'0','0', '巡检：$!{patrol-name}  计划开始时间：$!{todo-date}', 'Patrol: $!{patrol-name}  Planned Start Time: $!{todo-date}', null, '', 'sms', '巡检：$!{patrol-name}  计划开始时间：$!{todo-date}', null, (select id from message_template_group where code='patrol-remind'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('32', '2', b'0','0', '巡检：$!{patrol-name}  计划开始时间：$!{todo-date}', 'Patrol: $!{patrol-name}  Planned Start Time: $!{todo-date}', null, '', 'site', '巡检：$!{patrol-name}  计划开始时间：$!{todo-date}', null, (select id from message_template_group where code='patrol-remind'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('33', '0', b'0','0', '巡检：$!{patrol-name}  提前天数：$!{ahead-day}  计划开始时间：$!{todo-date}', 'Patrol: $!{patrol-name}  Ahead Time: $!{ahead-day}  Due To Start Time: $!{todo-date}', null, '', 'mpush', '巡检：$!{patrol-name}  提前天数：$!{ahead-day}  计划开始时间：$!{todo-date}', null, (select id from message_template_group where code='patrol-remind'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('34', '2', b'0','0', '巡检：$!{patrol-name} &nbsp;<div><span style=\"line-height: 1.42857;\">计划开始时间：$!{todo-date}</span><br></div>', 'Patrol: $!{patrol-name} &nbsp;<div>Planned Start Time: $!{todo-date}</div>', 'Patrol Remind', '巡检提醒', 'email', '巡检：$!{patrol-name} &nbsp;<span style=\"line-height: 1.42857;\">&nbsp;</span><div>计划开始时间：$!{todo-date}</div>', '巡检提醒', (select id from message_template_group where code='patrol-remind'));

-- pm-remind
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('41', '1', b'0','0', '#if($!{wo-code} != '''') 计划性维护$!{pm-name}关联工单 $!{wo-code}于$!{todo-date}开始，请提前准备。#else 计划性维护$!{pm-name}于$!{todo-date}开始，请提前准备。#end', '#if($!{wo-code} != '''') Preventive Maintenance $!{pm-name} generated work order $!{wo-code} ,start Time$!{todo-date} #else Preventive Maintenance  $!{pm-name} ,start Time$!{todo-date} #end', null, '', 'sms', '#if($!{wo-code} != '''') 计划性维护$!{pm-name}关联工单 $!{wo-code}于$!{todo-date}开始，请提前准备。#else 计划性维护$!{pm-name}于$!{todo-date}开始，请提前准备。#end', null, (select id from message_template_group where code='pm-remind'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('42', '2', b'0','0', '#if($!{wo-code} != '''') 计划性维护$!{pm-name}关联工单 $!{wo-code}于$!{todo-date}开始，请提前准备。#else 计划性维护$!{pm-name}于$!{todo-date}开始，请提前准备。#end', '#if($!{wo-code} != '''') Preventive Maintenance $!{pm-name} generated work order $!{wo-code} ,start Time$!{todo-date} #else Preventive Maintenance  $!{pm-name} ,start Time$!{todo-date} #end', null, '', 'site', '#if($!{wo-code} != '''') 计划性维护$!{pm-name}关联工单 $!{wo-code}于$!{todo-date}开始，请提前准备。#else 计划性维护$!{pm-name}于$!{todo-date}开始，请提前准备。#end', null, (select id from message_template_group where code='pm-remind'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('43', '0', b'0','0', '#if($!{wo-code} != '''') 计划性维护$!{pm-name}关联工单 $!{wo-code}于$!{todo-date}开始，请提前准备。#else 计划性维护$!{pm-name}于$!{todo-date}开始，请提前准备。#end', '#if($!{wo-code} != '''') Preventive Maintenance $!{pm-name} generated work order $!{wo-code} ,start Time$!{todo-date} #else Preventive Maintenance  $!{pm-name} ,start Time$!{todo-date} #end', null, '', 'mpush', '#if($!{wo-code} != '''') 计划性维护$!{pm-name}关联工单 $!{wo-code}于$!{todo-date}开始，请提前准备。#else 计划性维护$!{pm-name}于$!{todo-date}开始，请提前准备。#end', null, (select id from message_template_group where code='pm-remind'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('44', '2', b'0','0', '#if($!{wo-code} != '''') 计划性维护$!{pm-name}关联工单 $!{wo-code}于$!{todo-date}开始，请提前准备。#else 计划性维护$!{pm-name}于$!{todo-date}开始，请提前准备。#end', '#if($!{wo-code} != '''') Preventive Maintenance $!{pm-name} generated work order $!{wo-code} ,start Time$!{todo-date} #else Preventive Maintenance  $!{pm-name} ,start Time$!{todo-date} #end', 'PM Remind', '计划性维护提醒', 'email', '#if($!{wo-code} != '''') 计划性维护$!{pm-name}关联工单 $!{wo-code}于$!{todo-date}开始，请提前准备。#else 计划性维护$!{pm-name}于$!{todo-date}开始，请提前准备。#end', '计划性维护提醒', (select id from message_template_group where code='pm-remind'));

-- pm-uncomplete
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('51', '1', b'0','0', '计划性维护：$!{pm-name}，工单号：$!{wo-code}，计划开始时间：$!{todo-date}', 'Preventive Maintenance: $!{pm-name}, WO Code: $!{wo-code}，Planned Start Time: $!{todo-date}', null, '', 'sms', '计划性维护：$!{pm-name}，工单号：$!{wo-code}，计划开始时间：$!{todo-date}', null, (select id from message_template_group where code='pm-uncomplete'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('52', '1', b'0','0', '计划性维护：$!{pm-name}，工单号：$!{wo-code}，计划开始时间：$!{todo-date}', 'Preventive Maintenance: $!{pm-name}, WO Code: $!{wo-code}，Planned Start Time: $!{todo-date}', null, '', 'site', '计划性维护：$!{pm-name}，工单号：$!{wo-code}，计划开始时间：$!{todo-date}', null, (select id from message_template_group where code='pm-uncomplete'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('53', '0', b'0','0', '计划性维护：$!{pm-name}，工单号：$!{wo-code}，计划开始时间：$!{todo-date}', 'Preventive Maintenance: $!{pm-name}, WO Code: $!{wo-code}，Planned Start Time: $!{todo-date}', null, '', 'mpush', '计划性维护：$!{pm-name}，工单号：$!{wo-code}，计划开始时间：$!{todo-date}', null, (select id from message_template_group where code='pm-uncomplete'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('54', '1', b'0','0', '计划性维护：$!{pm-name}<div>工单号：$!{wo-code}</div><div>计划开始时间：$!{todo-date}</div>', 'Preventive Maintenance: $!{pm-name},<div>WO Code: $!{wo-code}</div><div>Planned Start Time: $!{todo-date}</div>', 'Remind of PM Not Completed In Time', '计划性维护未及时完成提醒', 'email', '计划性维护：$!{pm-name}<div>工单号：$!{wo-code}</div><div>计划开始时间：$!{todo-date}</div>', '计划性维护未及时完成响应提醒', (select id from message_template_group where code='pm-uncomplete'));

-- process-notice
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('61', '1', b'0','0', '工单号：$!{workorder-code}， 所处步骤：$!{workorder-notice-step}，工单描述：$!{workorder-description}', 'WO Code: $!{workorder-code}, The Step: $!{workorder-notice-step}, The Description: $!{workorder-description}', null, '', 'sms', '工单号：$!{workorder-code}， 所处步骤：$!{workorder-notice-step}，工单描述：$!{workorder-description}', null, (select id from message_template_group where code='process-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('62', '1', b'0','0', '工单号：$!{workorder-code}， 所处步骤：$!{workorder-notice-step}，工单描述：$!{workorder-description}', 'WO Code: $!{workorder-code}, The Step: $!{workorder-notice-step}, The Description: $!{workorder-description}', null, '', 'site', '工单号：$!{workorder-code}， 所处步骤：$!{workorder-notice-step}，工单描述：$!{workorder-description}', null, (select id from message_template_group where code='process-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('63', '0', b'0','0', '工单号：$!{workorder-code}， 所处步骤：$!{workorder-notice-step}，工单描述：$!{workorder-description}', '工单号：$!{workorder-code}， 所处步骤：$!{workorder-notice-step}，工单描述：$!{workorder-description}', null, '', 'mpush', '工单号：$!{workorder-code}， 所处步骤：$!{workorder-notice-step}，工单描述：$!{workorder-description}', null, (select id from message_template_group where code='process-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('64', '2', b'0','0', '工单号：$!{workorder-code}<div>所处步骤：$!{workorder-notice-step}</div><div>工单描述：$!{workorder-description}</div>', 'WO Code: $!{workorder-code}<div>The Step: $!{workorder-notice-step}</div><div>The Description: $!{workorder-description}</div>', 'Remind of WO Process', '工单流程提醒', 'email', '工单号：$!{workorder-code}<div>所处步骤：$!{workorder-notice-step}</div><div>工单描述：$!{workorder-description}</div>', '工单流程提醒', (select id from message_template_group where code='process-notice'));

-- workorder-escalation
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('71', '1', b'0','0', '工单号：$!{workorder-escalation-workorder-code}，升级类型：$!{workorder-escalation-type}，工单描述：$!{workorder-escalation-workorder-description}', 'WO Code: $!{workorder-escalation-workorder-code}, Escalation Type: $!{workorder-escalation-type}, WO Description: $!{workorder-escalation-workorder-description}', null, '', 'sms', '工单号：$!{workorder-escalation-workorder-code}，升级类型：$!{workorder-escalation-type}，工单描述：$!{workorder-escalation-workorder-description}', null, (select id from message_template_group where code='workorder-escalation'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('72', '1', b'0','0', '工单号：$!{workorder-escalation-workorder-code}，升级类型：$!{workorder-escalation-type}，工单描述：$!{workorder-escalation-workorder-description}', 'WO Code: $!{workorder-escalation-workorder-code}, Escalation Type: $!{workorder-escalation-type}, WO Description: $!{workorder-escalation-workorder-description}', null, '', 'site', '工单号：$!{workorder-escalation-workorder-code}，升级类型：$!{workorder-escalation-type}，工单描述：$!{workorder-escalation-workorder-description}', null, (select id from message_template_group where code='workorder-escalation'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('73', '0', b'0','0', '工单号：$!{workorder-escalation-workorder-code}，升级类型：$!{workorder-escalation-type}，工单描述：$!{workorder-escalation-workorder-description}', '工单号：$!{workorder-escalation-workorder-code}，升级类型：$!{workorder-escalation-type}，工单描述：$!{workorder-escalation-workorder-description}', null, '', 'mpush', '工单号：$!{workorder-escalation-workorder-code}，升级类型：$!{workorder-escalation-type}，工单描述：$!{workorder-escalation-workorder-description}', null, (select id from message_template_group where code='workorder-escalation'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('74', '2', b'0','0', '工单号：$!{workorder-escalation-workorder-code}<div>升级类型：$!{workorder-escalation-type}</div><div>工单描述：$!{workorder-escalation-workorder-description}</div>', 'WO Code: $!{workorder-escalation-workorder-code}<div>Escalation Type: $!{workorder-escalation-type}</div><div>WO Description: $!{workorder-escalation-workorder-description}</div>', 'Remind of Escalation', '工单升级提醒', 'email', '工单号：$!{workorder-escalation-workorder-code}<div>升级类型：$!{workorder-escalation-type}</div><div>工单描述：$!{workorder-escalation-workorder-description}</div>', '工单升级提醒', (select id from message_template_group where code='workorder-escalation'));

-- email_to_requester
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('81', '2', b'0','0', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', null, '', 'sms', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', null, (select id from message_template_group where code='email_to_requester'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('82', '2', b'0','0', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', null, '', 'site', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', null, (select id from message_template_group where code='email_to_requester'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('83', '0', b'0','0', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', null, '', 'mpush', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', null, (select id from message_template_group where code='email_to_requester'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('84', '2', b'0','0', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', '您报障的问题已经解决', '您报障的问题已经解决', 'email', '尊敬的客户，您好，您报障的问题已经解决，需求描述为 $!{requirement_description}；关联工单为 $!{order_code}；工单完成时间为 $!{finished_time}', '您报障的问题已经解决', (select id from message_template_group where code='email_to_requester'));

-- bulletin-notice
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('91', '0', b'0','0', '您有一条新的公告：$!{bulletin-title}，请及时查看！', 'You have a new bulletin：$!{bulletin-title}，please view it immediately!', null, '', 'sms', '您有一条新的公告：$!{bulletin-title}，请及时查看！', null, (select id from message_template_group where code='bulletin-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('92', '0', b'0','0', '您有一条新的公告：$!{bulletin-title}，请及时查看！', 'You have a new bulletin：$!{bulletin-title}，please view it immediately!', null, '', 'site', '您有一条新的公告：$!{bulletin-title}，请及时查看！', null, (select id from message_template_group where code='bulletin-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('93', '0', b'0','0', '您有一条新的公告：$!{bulletin-title}，请及时查看！', 'You have a new bulletin：$!{bulletin-title}，please view it immediately!', null, '', 'mpush', '您有一条新的公告：$!{bulletin-title}，请及时查看！', null, (select id from message_template_group where code='bulletin-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('94', '1', b'0','0', '您有一条新的公告：$!{bulletin-title}，请及时查看！', 'You have a new bulletin：$!{bulletin-title}，please view it immediately!', 'New bulletin notice', '新公告提醒', 'email', '您有一条新的公告：$!{bulletin-title}，请及时查看！', '新公告提醒', (select id from message_template_group where code='bulletin-notice'));

-- stock-check
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('101', '0', b'0','0', '仓库名称：$!{warehouse-name}，物资编码：$!{material-code}，物资名称：$!{material-name}，物资品牌：$!{material-brand}，物资型号：$!{material-model}，库存数量：$!{inventory-amount}，最小库存量：$!{inventory-minimum} ', 'warehouseName：$!{warehouse-name}，materialCode：$!{material-code}，materialName：$!{material-name}，materialBrand：$!{material-brand}，materialModel：$!{material-model}，inventoryAmount：$!{inventory-amount}，inventoryMinimum：$!{inventory-minimum} ', null, '', 'sms', '仓库名称：$!{warehouse-name}，物资编码：$!{material-code}，物资名称：$!{material-name}，物资品牌：$!{material-brand}，物资型号：$!{material-model}，库存数量：$!{inventory-amount}，最小库存量：$!{inventory-minimum} ', null, (select id from message_template_group where code='stock-check'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('102', '0', b'0','0', '仓库名称：$!{warehouse-name}，物资编码：$!{material-code}，物资名称：$!{material-name}，物资品牌：$!{material-brand}，物资型号：$!{material-model}，库存数量：$!{inventory-amount}，最小库存量：$!{inventory-minimum} ', 'warehouseName：$!{warehouse-name}，materialCode：$!{material-code}，materialName：$!{material-name}，materialBrand：$!{material-brand}，materialModel：$!{material-model}，inventoryAmount：$!{inventory-amount}，inventoryMinimum：$!{inventory-minimum} ', null, '', 'site', '仓库名称：$!{warehouse-name}，物资编码：$!{material-code}，物资名称：$!{material-name}，物资品牌：$!{material-brand}，物资型号：$!{material-model}，库存数量：$!{inventory-amount}，最小库存量：$!{inventory-minimum} ', null, (select id from message_template_group where code='stock-check'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('103', '0', b'0','0', '仓库名称：$!{warehouse-name}，物资编码：$!{material-code}，物资名称：$!{material-name}，物资品牌：$!{material-brand}，物资型号：$!{material-model}，库存数量：$!{inventory-amount}，最小库存量：$!{inventory-minimum} ', 'warehouseName：$!{warehouse-name}，materialCode：$!{material-code}，materialName：$!{material-name}，materialBrand：$!{material-brand}，materialModel：$!{material-model}，inventoryAmount：$!{inventory-amount}，inventoryMinimum：$!{inventory-minimum} ', null, '', 'mpush', '仓库名称：$!{warehouse-name}，物资编码：$!{material-code}，物资名称：$!{material-name}，物资品牌：$!{material-brand}，物资型号：$!{material-model}，库存数量：$!{inventory-amount}，最小库存量：$!{inventory-minimum} ', null, (select id from message_template_group where code='stock-check'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('104', '0', b'0','0', '仓库名称：$!{warehouse-name}，物资编码：$!{material-code}，物资名称：$!{material-name}，物资品牌：$!{material-brand}，物资型号：$!{material-model}，库存数量：$!{inventory-amount}，最小库存量：$!{inventory-minimum} ', 'warehouseName：$!{warehouse-name}，materialCode：$!{material-code}，materialName：$!{material-name}，materialBrand：$!{material-brand}，materialModel：$!{material-model}，inventoryAmount：$!{inventory-amount}，inventoryMinimum：$!{inventory-minimum} ', 'Inventory Amount notice', '库存量提醒', 'email', '仓库名称：$!{warehouse-name}，物资编码：$!{material-code}，物资名称：$!{material-name}，物资品牌：$!{material-brand}，物资型号：$!{material-model}，库存数量：$!{inventory-amount}，最小库存量：$!{inventory-minimum} ', '库存量提醒', (select id from message_template_group where code='stock-check'));

-- stock-notice
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('111', '0', b'0','0', '您的预定审批：$!{reserve-code}状态变更为$!{reserve-status}，请及时查看！', 'Your reserve approval：$!{reserve-code} become to $!{reserve-status},please view it immediately!', null, '', 'sms', '您的预定审批：$!{reserve-code}状态变更为$!{reserve-status}，请及时查看！', null, (select id from message_template_group where code='stock-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('112', '0', b'0','0', '您的预定审批：$!{reserve-code}状态变更为$!{reserve-status}，请及时查看！', 'Your reserve approval：$!{reserve-code} become to $!{reserve-status},please view it immediately!', null, '', 'site', '您的预定审批：$!{reserve-code}状态变更为$!{reserve-status}，请及时查看！', null, (select id from message_template_group where code='stock-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('113', '0', b'0','0', '您的预定审批：$!{reserve-code}状态变更为$!{reserve-status}，请及时查看！', 'Your reserve approval：$!{reserve-code} become to $!{reserve-status},please view it immediately!', null, '', 'mpush', '您的预定审批：$!{reserve-code}状态变更为$!{reserve-status}，请及时查看！', null, (select id from message_template_group where code='stock-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('114', '0', b'0','0', '您的预定审批：$!{reserve-code}状态变更为$!{reserve-status}，请及时查看！', 'Your reserve approval：$!{reserve-code} become to $!{reserve-status},please view it immediately!', 'Reserve approval complete notice', '审批完成提醒', 'email', '您的预定审批：$!{reserve-code}状态变更为$!{reserve-status}，请及时查看！', '审批完成提醒', (select id from message_template_group where code='stock-notice'));

-- stock-reserve
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('121', '0', b'0','0', '有一条库存预约待您审批：$!{reserve-code}，请注意查看！', 'You have a reservation for approval：$!{reserve-code}，please view it immediately!', null, '', 'sms', '有一条库存预约待您审批：$!{reserve-code}，请注意查看！', null, (select id from message_template_group where code='stock-reserve'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('122', '0', b'0','0', '有一条库存预约待您审批：$!{reserve-code}，请注意查看！', 'You have a reservation for approval：$!{reserve-code}，please view it immediately!', null, '', 'site', '有一条库存预约待您审批：$!{reserve-code}，请注意查看！', null, (select id from message_template_group where code='stock-reserve'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('123', '0', b'0','0', '有一条库存预约待您审批：$!{reserve-code}，请注意查看！', 'You have a reservation for approval：$!{reserve-code}，please view it immediately!', null, '', 'mpush', '有一条库存预约待您审批：$!{reserve-code}，请注意查看！', null, (select id from message_template_group where code='stock-reserve'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('124', '0', b'0','0', '有一条库存预约待您审批：$!{reserve-code}，请注意查看！', 'You have a reservation for approval：$!{reserve-code}，please view it immediately!', 'Reserve approval notice', '审批提醒', 'email', '有一条库存预约待您审批：$!{reserve-code}，请注意查看！', '审批提醒', (select id from message_template_group where code='stock-reserve'));

-- contract-notice
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('131', '2', b'0','0', '合同($!{contract-code})即将到期，请注意查看!', 'Contract($!{contract-code}) will be expired, please pay attention to view!', null, null, 'sms', '合同($!{contract-code})即将到期，请注意查看!', null, (select id from message_template_group where code='contract-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('132', '2', b'0','0', '合同($!{contract-code})即将到期，请注意查看!', 'Contract($!{contract-code}) will be expired, please pay attention to view!', null, null, 'site', '合同($!{contract-code})即将到期，请注意查看!', null, (select id from message_template_group where code='contract-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('133', '2', b'0','0', '合同($!{contract-code})即将到期，请注意查看!', 'Contract($!{contract-code}) will be expired, please pay attention to view!', null, null, 'mpush', '合同($!{contract-code})即将到期，请注意查看!', null, (select id from message_template_group where code='contract-notice'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('134', '2', b'0','0', '合同($!{contract-code})即将到期，请注意查看!', 'Contract($!{contract-code}) will be expired, please pay attention to view!', 'A Contract Expired Reminder', '合同即将到期提醒', 'email', '合同($!{contract-code})即将到期，请注意查看!', '合同即将到期提醒', (select id from message_template_group where code='contract-notice'));

-- stock-reserve-pass
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('141', '0', b'0','0', '物资预定号为 $!{reserve-code}的审批已通过，审批人$!{reserve-approver}，预订人$!{reserve-destine}，请准备处理物资出库！', 'Reserve $!{reserve-code} is approved by $!{reserve-approver},and booked by $!{reserve-destine}.Please prepare the materials out of the warehouse!', null, '', 'sms', '物资预定号为 $!{reserve-code}的审批已通过，审批人$!{reserve-approver}，预订人$!{reserve-destine}，请准备处理物资出库！', null, (select id from message_template_group where code='stock-reserve-pass'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('142', '0', b'0','0', '物资预定号为 $!{reserve-code}的审批已通过，审批人$!{reserve-approver}，预订人$!{reserve-destine}，请准备处理物资出库！', 'Reserve $!{reserve-code} is approved by $!{reserve-approver},and booked by $!{reserve-destine}.Please prepare the materials out of the warehouse!', null, '', 'site', '物资预定号为 $!{reserve-code}的审批已通过，审批人$!{reserve-approver}，预订人$!{reserve-destine}，请准备处理物资出库！', null, (select id from message_template_group where code='stock-reserve-pass'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('143', '0', b'0','0', '物资预定号为 $!{reserve-code}的审批已通过，审批人$!{reserve-approver}，预订人$!{reserve-destine}，请准备处理物资出库！', 'Reserve $!{reserve-code} is approved by $!{reserve-approver},and booked by $!{reserve-destine}.Please prepare the materials out of the warehouse!', null, '', 'mpush', '物资预定号为 $!{reserve-code}的审批已通过，审批人$!{reserve-approver}，预订人$!{reserve-destine}，请准备处理物资出库！', null, (select id from message_template_group where code='stock-reserve-pass'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('144', '0', b'0','0', '物资预定号为 $!{reserve-code}的审批已通过，审批人$!{reserve-approver}，预订人$!{reserve-destine}，请准备处理物资出库！', 'Reserve $!{reserve-code} is approved by $!{reserve-approver},and booked by $!{reserve-destine}.Please prepare the materials out of the warehouse!', 'Reserve approval complete notice', '物资预定审核通过提醒', 'email', '物资预定号为 $!{reserve-code}的审批已通过，审批人$!{reserve-approver}，预订人$!{reserve-destine}，请准备处理物资出库！', '物资预定审核通过提醒', (select id from message_template_group where code='stock-reserve-pass'));

-- materials-delivered
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('151', '0', b'0','0', '您预定的物资已出库，物料 $!{reserve-materials}(等),出库总数量 $!{reserve-materials-count}！', 'Your reserved materials is delivered, material is $!{reserve-materials} (and so on) ,total $!{reserve-materials-count}!', null, '', 'sms', '您预定的物资已出库，物料 $!{reserve-materials}(等),出库总数量 $!{reserve-materials-count}！', null, (select id from message_template_group where code='materials-delivered'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('152', '0', b'0','0', '您预定的物资已出库，物料 $!{reserve-materials}(等),出库总数量 $!{reserve-materials-count}！', 'Your reserved materials is delivered, material is $!{reserve-materials} (and so on) ,total $!{reserve-materials-count}!', null, '', 'site', '您预定的物资已出库，物料 $!{reserve-materials}(等),出库总数量 $!{reserve-materials-count}！', null, (select id from message_template_group where code='materials-delivered'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('153', '0', b'0','0', '您预定的物资已出库，物料 $!{reserve-materials}(等),出库总数量 $!{reserve-materials-count}！', 'Your reserved materials is delivered, material is $!{reserve-materials} (and so on) ,total $!{reserve-materials-count}!', null, '', 'mpush', '您预定的物资已出库，物料 $!{reserve-materials}(等),出库总数量 $!{reserve-materials-count}！', null, (select id from message_template_group where code='materials-delivered'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('154', '0', b'0','0', '您预定的物资已出库，物料 $!{reserve-materials}(等),出库总数量 $!{reserve-materials-count}！', 'Your reserved materials is delivered, material is $!{reserve-materials} (and so on) ,total $!{reserve-materials-count}!', 'Materials Reserved notice', '物资出库提醒', 'email', '您预定的物资已出库，物料 $!{reserve-materials}(等),出库总数量 $!{reserve-materials-count}！', '审批提醒', (select id from message_template_group where code='materials-delivered'));

-- patrol-undo-remind
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('161', '0', b'0','0', '$!{patrol-name} 计划于 $!{patrol-end-time} 完成，还剩$!{patrol-undo-remind-value}$!{patrol-undo-remind-unit}，请及时处理。', '$!{patrol-name} is scheduled to be completed at $!{patrol-end-time},already!{patrol-undo-remind-value}$!{patrol-undo-remind-unit} left, please check in time.', null, '', 'sms', '$!{patrol-name} 计划于 $!{patrol-end-time} 完成，还剩$!{patrol-undo-remind-value}$!{patrol-undo-remind-unit}，请及时处理。', null, (select id from message_template_group where code='patrol-undo-remind'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('162', '0', b'0','0', '$!{patrol-name} 计划于 $!{patrol-end-time} 完成，还剩$!{patrol-undo-remind-value}$!{patrol-undo-remind-unit}，请及时处理。', '$!{patrol-name} is scheduled to be completed at $!{patrol-end-time},already!{patrol-undo-remind-value}$!{patrol-undo-remind-unit} left, please check in time.', null, '', 'site', '$!{patrol-name} 计划于 $!{patrol-end-time} 完成，还剩$!{patrol-undo-remind-value}$!{patrol-undo-remind-unit}，请及时处理。', null, (select id from message_template_group where code='patrol-undo-remind'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('163', '0', b'0','0', '$!{patrol-name} 计划于 $!{patrol-end-time} 完成，还剩$!{patrol-undo-remind-value}$!{patrol-undo-remind-unit}，请及时处理。', '$!{patrol-name} is scheduled to be completed at $!{patrol-end-time},already!{patrol-undo-remind-value}$!{patrol-undo-remind-unit} left, please check in time.', null, '', 'mpush', '$!{patrol-name} 计划于 $!{patrol-end-time} 完成，还剩$!{patrol-undo-remind-value}$!{patrol-undo-remind-unit}，请及时处理。', null, (select id from message_template_group where code='patrol-undo-remind'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('164', '0', b'0','0', '$!{patrol-name} 计划于 $!{patrol-end-time} 完成，还剩$!{patrol-undo-remind-value}$!{patrol-undo-remind-unit}，请及时处理。', '$!{patrol-name} is scheduled to be completed at $!{patrol-end-time},already!{patrol-undo-remind-value}$!{patrol-undo-remind-unit} left, please check in time.', 'Unfinished reminders of patrol task', '巡检任务未完成提醒', 'email', '$!{patrol-name} 计划于 $!{patrol-end-time} 完成，还剩$!{patrol-undo-remind-value}$!{patrol-undo-remind-unit}，请及时处理。', '巡检任务未完成提醒', (select id from message_template_group where code='patrol-undo-remind'));

-- monitoring-sync
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('171', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}, please pay attention to view!', null, null, 'sms', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。请查看!', null, (select id from message_template_group where code='monitoring-sync'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('172', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}, please pay attention to view!', null, null, 'site', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。请查看!', null, (select id from message_template_group where code='monitoring-sync'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('173', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}, please pay attention to view!', null, null, 'mpush', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。请查看!', null, (select id from message_template_group where code='monitoring-sync'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('174', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}, please pay attention to view!', 'Monitoring data synchronization', '监测数据同步', 'email', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。请查看!', '监测数据同步', (select id from message_template_group where code='monitoring-sync'));

INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('181', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', null, null, 'sms', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', null, (select id from message_template_group where code='monitoring-sync'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('182', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', null, null, 'site', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', null, (select id from message_template_group where code='monitoring-sync'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('183', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', null, null, 'mpush', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', null, (select id from message_template_group where code='monitoring-sync'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('184', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', 'Monitoring data synchronization', '监测数据同步', 'email', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '监测数据同步', (select id from message_template_group where code='monitoring-sync'));

INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('191', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', null, null, 'sms', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', null, (select id from message_template_group where code='monitoring-data-abnormal'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('192', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', null, null, 'site', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', null, (select id from message_template_group where code='monitoring-data-abnormal'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('193', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', null, null, 'mpush', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', null, (select id from message_template_group where code='monitoring-data-abnormal'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('194', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-date}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', 'Monitoring data synchronization', '监测数据同步', 'email', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-date} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '监测数据同步', (select id from message_template_group where code='monitoring-data-abnormal'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('195', '0', b'0','0', '数据监测记录在$!{sync-date}请求失败。$!{sync-msg}', 'Data monitoring failed at $!{sync-date}.$!{sync-msg}', null, null, 'site', '数据监测记录在$!{sync-date}请求失败。$!{sync-msg}', null, (select id from message_template_group where code='monitoring-sync-request-fail'));

INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('201', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-time} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-time}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', null, null, 'sms', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-time} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', null, (select id from message_template_group where code='monitoring-data-abnormal'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('202', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-time} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-time}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', null, null, 'site', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-time} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', null, (select id from message_template_group where code='monitoring-data-abnormal'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('203', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-time} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-time}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', null, null, 'mpush', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-time} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', null, (select id from message_template_group where code='monitoring-data-abnormal'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('204', '0', b'0','0', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-time} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '$!{obj-type} : $!{obj-code}/$!{obj-name} occurred exception at $!{sync-time}, params : $!{device-params}. Monitoring error number : $!{device-params}. please pay attention to view!', 'Monitoring data synchronization', '监测数据同步', 'email', '$!{obj-type} : $!{obj-code}/$!{obj-name} 在$!{sync-time} 发现异常，异常参数：$!{device-params} 。监测异常编号：$!{monitoring_error_no}。请查看!', '监测数据同步', (select id from message_template_group where code='monitoring-data-abnormal'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('205', '0', b'0','0', '数据监测记录在$!{request-time}请求失败。$!{error-message}', 'Data monitoring failed at $!{request-time}.$!{error-message}', null, null, 'site', '数据监测记录在$!{request-time}请求失败。$!{error-message}', null, (select id from message_template_group where code='monitoring-sync-request-fail'));

INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('211', '0', b'0','0', '$!{device-code}/$!{device-name}在$!{alarm-time}有告警“$!{alarm-info}”，请注意查看!', 'There is a alarm ''$!{alarm-info}'' about $!{device-code}/$!{device-name},please pay attention to view!', null, null, 'sms', '$!{device-code}/$!{device-name}在$!{alarm-time}有告警“$!{alarm-info}”，请注意查看!', null, (select id from message_template_group where code='monitoring-alarm'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('212', '0', b'0','0', '$!{device-code}/$!{device-name}在$!{alarm-time}有告警“$!{alarm-info}”，请注意查看!', 'There is a alarm ''$!{alarm-info}'' about $!{device-code}/$!{device-name},please pay attention to view!', null, null, 'site', '$!{device-code}/$!{device-name}在$!{alarm-time}有告警“$!{alarm-info}”，请注意查看!', null, (select id from message_template_group where code='monitoring-alarm'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('213', '0', b'0','0', '$!{device-code}/$!{device-name}在$!{alarm-time}有告警“$!{alarm-info}”，请注意查看!', 'There is a alarm ''$!{alarm-info}'' about $!{device-code}/$!{device-name},please pay attention to view!', null, null, 'mpush', '$!{device-code}/$!{device-name}在$!{alarm-time}有告警“$!{alarm-info}”，请注意查看!', null, (select id from message_template_group where code='monitoring-alarm'));
INSERT INTO `message_template` (id,version,deleted,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES ('214', '0', b'0','0', '$!{device-code}/$!{device-name}在$!{alarm-time}有告警“$!{alarm-info}”，请注意查看!', 'There is a alarm ''$!{alarm-info}'' about $!{device-code}/$!{device-name},please pay attention to view!', 'Monitoring Alarm', '监测告警', 'email', '$!{device-code}/$!{device-name}在$!{alarm-time}有告警“$!{alarm-info}”，请注意查看!', '监测告警', (select id from message_template_group where code='monitoring-alarm'));

--requirement remind
INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('221' , '0' , b'0' , '0' , '您有一条新的需求:$!{require-code}，请及时处理。' , 'You have a new require: $!{require-code},please deal with it in time.' , NULL , NULL , 'sms' , '您有一条新的需求:$!{require-code}，请及时处理。' , NULL ,( SELECT id FROM message_template_group WHERE CODE = 'require-notice'));
INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('222' , '0' , b'0' , '0' , '您有一条新的需求:$!{require-code}，请及时处理。' , 'You have a new require: $!{require-code},please deal with it in time.' , NULL , NULL , 'site' , '您有一条新的需求:$!{require-code}，请及时处理。' , NULL ,( SELECT id FROM message_template_group WHERE CODE = 'require-notice'));
INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('223' , '0' , b'0' , '0' , '您有一条新的需求:$!{require-code}，请及时处理。' , 'You have a new require: $!{require-code},please deal with it in time.' , NULL , NULL , 'mpush' , '您有一条新的需求:$!{require-code}，请及时处理。' , NULL ,( SELECT id FROM message_template_group WHERE CODE = 'require-notice'));
INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('224' , '0' , b'0' , '0' , '您有一条新的需求:$!{require-code}，请及时处理。' , 'You have a new require: $!{require-code},please deal with it in time.' , 'New require message' , '新需求信息' , 'email' , '您有一条新的需求:$!{require-code}，请及时处理。' , '新需求信息' ,( SELECT id FROM message_template_group WHERE CODE = 'require-notice'));

INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('231' , '0' , b'0' , '0' , '你有新的承接查验任务“$!{undertake-name}”，请及时处理。' , 'You have new inspection task $!{undertake-name}, please deal with it in time.' , NULL , NULL , 'sms' , '你有新的承接查验任务“$!{undertake-name}”，请及时处理。' , NULL ,( SELECT id FROM message_template_group WHERE CODE = 'undertake-check-remind'));
INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('232' , '0' , b'0' , '0' , '你有新的承接查验任务“$!{undertake-name}”，请及时处理。' , 'You have new inspection task $!{undertake-name}, please deal with it in time.' , NULL , NULL , 'site' , '你有新的承接查验任务“$!{undertake-name}”，请及时处理。' , NULL ,( SELECT id FROM message_template_group WHERE CODE = 'undertake-check-remind'));
INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('233' , '0' , b'0' , '0' , '你有新的承接查验任务“$!{undertake-name}”，请及时处理。' , 'You have new inspection task $!{undertake-name}, please deal with it in time.' , NULL , NULL , 'mpush' , '你有新的承接查验任务“$!{undertake-name}”，请及时处理。' , NULL ,( SELECT id FROM message_template_group WHERE CODE = 'undertake-check-remind'));
INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('234' , '0' , b'0' , '0' , '你有新的承接查验任务“$!{undertake-name}”，请及时处理。' , 'You have new inspection task $!{undertake-name}, please deal with it in time.' , 'Undertake Check Remind' , '承接查验查验提醒' , 'email' , '你有新的承接查验任务“$!{undertake-name}”，请及时处理。' , '承接查验查验提醒' ,( SELECT id FROM message_template_group WHERE CODE = 'undertake-check-remind'));

INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('241' , '0' , b'0' , '0' , '你有新的承接查验任务“$!{undertake-accept-name}”需要你验收，请及时处理。' , 'Your new inspection task $!{undertake-accept-name} needs your acceptance. Please deal with it in time.' , NULL , NULL , 'sms' , '你有新的承接查验任务“$!{undertake-accept-name}”需要你验收，请及时处理。' , NULL ,( SELECT id FROM message_template_group WHERE CODE = 'undertake-accept-remind'));
INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('242' , '0' , b'0' , '0' , '你有新的承接查验任务“$!{undertake-accept-name}”需要你验收，请及时处理。' , 'Your new inspection task $!{undertake-accept-name} needs your acceptance. Please deal with it in time.' , NULL , NULL , 'site' , '你有新的承接查验任务“$!{undertake-accept-name}”需要你验收，请及时处理。' , NULL ,( SELECT id FROM message_template_group WHERE CODE = 'undertake-accept-remind'));
INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('243' , '0' , b'0' , '0' , '你有新的承接查验任务“$!{undertake-accept-name}”需要你验收，请及时处理。' , 'Your new inspection task $!{undertake-accept-name} needs your acceptance. Please deal with it in time.' , NULL , NULL , 'mpush' , '你有新的承接查验任务“$!{undertake-accept-name}”需要你验收，请及时处理。' , NULL ,( SELECT id FROM message_template_group WHERE CODE = 'undertake-accept-remind'));
INSERT INTO `message_template`(id ,version ,deleted ,proj_id,content,en_content,en_title,title,m_type,zh_content,zh_title,group_id) VALUES('244' , '0' , b'0' , '0' , '你有新的承接查验任务“$!{undertake-accept-name}”需要你验收，请及时处理。' , 'Your new inspection task $!{undertake-accept-name} needs your acceptance. Please deal with it in time.' , 'Undertake Accept Remind' , '承接查验验收提醒' , 'email' , '你有新的承接查验任务“$!{undertake-accept-name}”需要你验收，请及时处理。' , '承接查验验收提醒' ,( SELECT id FROM message_template_group WHERE CODE = 'undertake-accept-remind'));
