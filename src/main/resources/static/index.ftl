<!DOCTYPE html>
<#assign  _STATIC_COMMON_="${baseStaticPath}/resource">
<#assign  _STATIC_BUSINESS_="${baseStaticPath}/business">
<#assign  _STATIC_VERSION_="?_v="+.now?string("yyyyMMdd")>

<html>
<#include "template/head.ftl" encoding="utf8">
<style>
    .projects {
        margin-top: 15px;
    }

    .projects .project {
        border: 1px solid #dddddd;
        max-width: 350px;
        max-height: 240px;
        padding: 10px;
        float: left;
        margin: 0 10px;
    }

    .projects .project span {
        float: right;
    }

    .projects .project img {
        max-width: 100%;
    }
</style>
<body>
<#include "template/header.ftl" encoding="utf8">
<section id="page">
<#include "template/left.ftl" encoding="utf8">
    <div id="main-content" class="">
        <div class="container">
            <div class="row" style="background-color: #f5f5f5;">
                <!-- notice-->
                <div id="index_notice" style="position: fixed;  z-index: 9999;"></div>
                <!--header-->
                <div id="page-header" style="display: none;" data-home="${i18n("page.index.content.home")}">
                    <div class="page-header" style="min-height: 0;"><h3
                            style="margin: 0px;">${i18n("page.index.content.home")}</h3></div>
                </div>
                <!--content-->
                <div id="content" class="col-lg-12">

                </div>
            </div>
            <div class="footer-tools" style="display:none;">
                    <span class="go-top">
                        <i class="fa fa-chevron-up"></i> Top
                    </span>
            </div>
        </div>
    </div>
</section>
<script>document.documentElement.lang = "${i18nLocale}";</script>
<#include "template/footer.ftl" encoding="utf8">
<script src="${_STATIC_COMMON_}/js/jquery-plugin/jquery-plugin-hash.js"></script>
<script src="${_STATIC_COMMON_}/js/js-plugin/navmaster/jquery.nav.js"></script>
<script src="${_STATIC_COMMON_}/js/js-plugin/navmaster/jquery.scrollTo.js"></script>
<script src="${_STATIC_COMMON_}/js/js-plugin/ezuiplayer/ezuiplayer.js"></script>

<#-- xss -->
<script src="${_STATIC_COMMON_}/js/js-plugin/xss/xss.min.js"></script>

<script type="text/javascript" src="${_STATIC_COMMON_}/js/js-plugin/jquery-city-select/data.js"></script>

<script>
    //消息服务配置参数
    var _messagesite_hostname = "${messagesite.hostname}";
    var _messagesite_port = "${messagesite.port?c}";
    var _messagesite_channel = "${messagesite.chanel}";
</script>

<script>
    $(document).ready(function () {

        //doFixButtonGroup(".button-bar");

        //headpic
        if ("${headPic!}" != "") {
            var path = PUBLIC_PATH+"/common/files/id/${headPic!}/img?access_token=" + getAccessToken();
            $("#header-user .dropdown-toggle").find("img").attr("src", path);
        }

        var openProjectHome = function (pid) {
            var accesstoken = localStorage.access_token;
            if (!accesstoken) {
                accesstoken = FO.get_uri_param("access_token");
            }
            localStorage.current_project = pid;

           //百度追踪
            _hmt && _hmt.push(['_trackPageview', "/main/home/" + pid + "?current_project=" + pid]);

            location.href = location.origin + "/main/home/" + pid + "?current_project=" + pid + "&access_token=" + accesstoken;
        }

        //tohome
        var _pone = "${isem?c}";
        if (_pone == "true") {
        <#if pcurrent??>
            openProjectHome("${pcurrent.id?c}");
        </#if>
        }

        //click to home
        $(".projects .project").click(function () {
            var _pid = $(this).data("pid");

            openProjectHome(_pid);
        });
        $(".projects .project img").each(function () {
            var _pid = $(this).data("pic");
            if (_pid) {
                var _src = PUBLIC_PATH+"/common/files/id/" + _pid + "/img?access_token=" + getAccessToken();
                $(this).attr("src", _src);
            }
        });
    });
    var showMeaageSite = function () {
        openMenuPage("/uc002", {});
    }
    var showPerson = function (e) {
        openMenuPage("/uc001", {});
    }
    var shwoHelpCenter=function(e){
        window.open('https://help.fmone.cn')
    }
    var exit = function () {
        $.get("/logout",function(data){
            var href = location.href.split("#");
            href = href[0].split("?")[0];

            //百度追踪
            _hmt && _hmt.push(['_trackPageview', href]);
            window.location.href = href;
        });
    }
</script>

<script src="${_STATIC_COMMON_}/js/index.js"></script>

<audio id="noticeMp3" src="${_STATIC_COMMON_}/img/notice.mp3" hidden="true"></audio>
</body>
</html>
