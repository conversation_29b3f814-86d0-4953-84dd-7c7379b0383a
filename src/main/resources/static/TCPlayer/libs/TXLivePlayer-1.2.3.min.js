!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).TXLivePlayer=t()}(this,(function(){"use strict";function e(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var n=function(e){try{return!!e()}catch(e){return!0}},i=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),o=i,s=Function.prototype,a=s.bind,c=s.call,u=o&&a.bind(c,c),l=o?function(e){return e&&u(e)}:function(e){return e&&function(){return c.apply(e,arguments)}},d=l({}.isPrototypeOf),p=function(e){return e&&e.Math==Math&&e},f=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof t&&t)||function(){return this}()||Function("return this")(),h=i,v=Function.prototype,m=v.apply,y=v.call,g="object"==typeof Reflect&&Reflect.apply||(h?y.bind(m):function(){return y.apply(m,arguments)}),b=function(e){return"function"==typeof e},S={},T=!n((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),_=i,C=Function.prototype.call,w=_?C.bind(C):function(){return C.apply(C,arguments)},E={},R={}.propertyIsEnumerable,P=Object.getOwnPropertyDescriptor,A=P&&!R.call({1:2},1);E.f=A?function(e){var t=P(this,e);return!!t&&t.enumerable}:R;var M,k,O=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},D=l,I=D({}.toString),x=D("".slice),L=function(e){return x(I(e),8,-1)},N=n,F=L,j=Object,B=l("".split),V=N((function(){return!j("z").propertyIsEnumerable(0)}))?function(e){return"String"==F(e)?B(e,""):j(e)}:j,U=TypeError,z=function(e){if(null==e)throw U("Can't call method on "+e);return e},G=V,W=z,Y=function(e){return G(W(e))},q=b,H=function(e){return"object"==typeof e?null!==e:q(e)},J={},Q=J,K=f,Z=b,X=function(e){return Z(e)?e:void 0},$=function(e,t){return arguments.length<2?X(Q[e])||X(K[e]):Q[e]&&Q[e][t]||K[e]&&K[e][t]},ee=$("navigator","userAgent")||"",te=f,re=ee,ne=te.process,ie=te.Deno,oe=ne&&ne.versions||ie&&ie.version,se=oe&&oe.v8;se&&(k=(M=se.split("."))[0]>0&&M[0]<4?1:+(M[0]+M[1])),!k&&re&&(!(M=re.match(/Edge\/(\d+)/))||M[1]>=74)&&(M=re.match(/Chrome\/(\d+)/))&&(k=+M[1]);var ae=k,ce=ae,ue=n,le=!!Object.getOwnPropertySymbols&&!ue((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&ce&&ce<41})),de=le&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,pe=$,fe=b,he=d,ve=Object,me=de?function(e){return"symbol"==typeof e}:function(e){var t=pe("Symbol");return fe(t)&&he(t.prototype,ve(e))},ye=String,ge=function(e){try{return ye(e)}catch(e){return"Object"}},be=b,Se=ge,Te=TypeError,_e=function(e){if(be(e))return e;throw Te(Se(e)+" is not a function")},Ce=_e,we=function(e,t){var r=e[t];return null==r?void 0:Ce(r)},Ee=w,Re=b,Pe=H,Ae=TypeError,Me={exports:{}},ke=f,Oe=Object.defineProperty,De=function(e,t){try{Oe(ke,e,{value:t,configurable:!0,writable:!0})}catch(r){ke[e]=t}return t},Ie="__core-js_shared__",xe=f[Ie]||De(Ie,{}),Le=xe;(Me.exports=function(e,t){return Le[e]||(Le[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.23.3",mode:"pure",copyright:"漏 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.23.3/LICENSE",source:"https://github.com/zloirock/core-js"});var Ne=z,Fe=Object,je=function(e){return Fe(Ne(e))},Be=je,Ve=l({}.hasOwnProperty),Ue=Object.hasOwn||function(e,t){return Ve(Be(e),t)},ze=l,Ge=0,We=Math.random(),Ye=ze(1..toString),qe=function(e){return"Symbol("+(void 0===e?"":e)+")_"+Ye(++Ge+We,36)},He=f,Je=Me.exports,Qe=Ue,Ke=qe,Ze=le,Xe=de,$e=Je("wks"),et=He.Symbol,tt=et&&et.for,rt=Xe?et:et&&et.withoutSetter||Ke,nt=function(e){if(!Qe($e,e)||!Ze&&"string"!=typeof $e[e]){var t="Symbol."+e;Ze&&Qe(et,e)?$e[e]=et[e]:$e[e]=Xe&&tt?tt(t):rt(t)}return $e[e]},it=w,ot=H,st=me,at=we,ct=function(e,t){var r,n;if("string"===t&&Re(r=e.toString)&&!Pe(n=Ee(r,e)))return n;if(Re(r=e.valueOf)&&!Pe(n=Ee(r,e)))return n;if("string"!==t&&Re(r=e.toString)&&!Pe(n=Ee(r,e)))return n;throw Ae("Can't convert object to primitive value")},ut=TypeError,lt=nt("toPrimitive"),dt=function(e,t){if(!ot(e)||st(e))return e;var r,n=at(e,lt);if(n){if(void 0===t&&(t="default"),r=it(n,e,t),!ot(r)||st(r))return r;throw ut("Can't convert object to primitive value")}return void 0===t&&(t="number"),ct(e,t)},pt=me,ft=function(e){var t=dt(e,"string");return pt(t)?t:t+""},ht=H,vt=f.document,mt=ht(vt)&&ht(vt.createElement),yt=function(e){return mt?vt.createElement(e):{}},gt=yt,bt=!T&&!n((function(){return 7!=Object.defineProperty(gt("div"),"a",{get:function(){return 7}}).a})),St=T,Tt=w,_t=E,Ct=O,wt=Y,Et=ft,Rt=Ue,Pt=bt,At=Object.getOwnPropertyDescriptor;S.f=St?At:function(e,t){if(e=wt(e),t=Et(t),Pt)try{return At(e,t)}catch(e){}if(Rt(e,t))return Ct(!Tt(_t.f,e,t),e[t])};var Mt=n,kt=b,Ot=/#|\.prototype\./,Dt=function(e,t){var r=xt[It(e)];return r==Nt||r!=Lt&&(kt(t)?Mt(t):!!t)},It=Dt.normalize=function(e){return String(e).replace(Ot,".").toLowerCase()},xt=Dt.data={},Lt=Dt.NATIVE="N",Nt=Dt.POLYFILL="P",Ft=Dt,jt=_e,Bt=i,Vt=l(l.bind),Ut=function(e,t){return jt(e),void 0===t?e:Bt?Vt(e,t):function(){return e.apply(t,arguments)}},zt={},Gt=T&&n((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Wt=H,Yt=String,qt=TypeError,Ht=function(e){if(Wt(e))return e;throw qt(Yt(e)+" is not an object")},Jt=T,Qt=bt,Kt=Gt,Zt=Ht,Xt=ft,$t=TypeError,er=Object.defineProperty,tr=Object.getOwnPropertyDescriptor,rr="enumerable",nr="configurable",ir="writable";zt.f=Jt?Kt?function(e,t,r){if(Zt(e),t=Xt(t),Zt(r),"function"==typeof e&&"prototype"===t&&"value"in r&&ir in r&&!r.writable){var n=tr(e,t);n&&n.writable&&(e[t]=r.value,r={configurable:nr in r?r.configurable:n.configurable,enumerable:rr in r?r.enumerable:n.enumerable,writable:!1})}return er(e,t,r)}:er:function(e,t,r){if(Zt(e),t=Xt(t),Zt(r),Qt)try{return er(e,t,r)}catch(e){}if("get"in r||"set"in r)throw $t("Accessors not supported");return"value"in r&&(e[t]=r.value),e};var or=zt,sr=O,ar=T?function(e,t,r){return or.f(e,t,sr(1,r))}:function(e,t,r){return e[t]=r,e},cr=f,ur=g,lr=l,dr=b,pr=S.f,fr=Ft,hr=J,vr=Ut,mr=ar,yr=Ue,gr=function(e){var t=function(r,n,i){if(this instanceof t){switch(arguments.length){case 0:return new e;case 1:return new e(r);case 2:return new e(r,n)}return new e(r,n,i)}return ur(e,this,arguments)};return t.prototype=e.prototype,t},br=function(e,t){var r,n,i,o,s,a,c,u,l=e.target,d=e.global,p=e.stat,f=e.proto,h=d?cr:p?cr[l]:(cr[l]||{}).prototype,v=d?hr:hr[l]||mr(hr,l,{})[l],m=v.prototype;for(i in t)r=!fr(d?i:l+(p?".":"#")+i,e.forced)&&h&&yr(h,i),s=v[i],r&&(a=e.dontCallGetSet?(u=pr(h,i))&&u.value:h[i]),o=r&&a?a:t[i],r&&typeof s==typeof o||(c=e.bind&&r?vr(o,cr):e.wrap&&r?gr(o):f&&dr(o)?lr(o):o,(e.sham||o&&o.sham||s&&s.sham)&&mr(c,"sham",!0),mr(v,i,c),f&&(yr(hr,n=l+"Prototype")||mr(hr,n,{}),mr(hr[n],i,o),e.real&&m&&!m[i]&&mr(m,i,o)))},Sr=l([].slice),Tr=l,_r=_e,Cr=H,wr=Ue,Er=Sr,Rr=i,Pr=Function,Ar=Tr([].concat),Mr=Tr([].join),kr={},Or=function(e,t,r){if(!wr(kr,t)){for(var n=[],i=0;i<t;i++)n[i]="a["+i+"]";kr[t]=Pr("C,a","return new C("+Mr(n,",")+")")}return kr[t](e,r)},Dr=Rr?Pr.bind:function(e){var t=_r(this),r=t.prototype,n=Er(arguments,1),i=function(){var r=Ar(n,Er(arguments));return this instanceof i?Or(t,r.length,r):t.apply(e,r)};return Cr(r)&&(i.prototype=r),i},Ir=Dr;br({target:"Function",proto:!0,forced:Function.bind!==Ir},{bind:Ir});var xr=J,Lr=function(e){return xr[e+"Prototype"]},Nr=Lr("Function").bind,Fr=d,jr=Nr,Br=Function.prototype,Vr=function(e){var t=e.bind;return e===Br||Fr(Br,e)&&t===Br.bind?jr:t},Ur=Math.ceil,zr=Math.floor,Gr=Math.trunc||function(e){var t=+e;return(t>0?zr:Ur)(t)},Wr=function(e){var t=+e;return t!=t||0===t?0:Gr(t)},Yr=Wr,qr=Math.max,Hr=Math.min,Jr=function(e,t){var r=Yr(e);return r<0?qr(r+t,0):Hr(r,t)},Qr=Wr,Kr=Math.min,Zr=function(e){return e>0?Kr(Qr(e),9007199254740991):0},Xr=Zr,$r=function(e){return Xr(e.length)},en=Y,tn=Jr,rn=$r,nn=function(e){return function(t,r,n){var i,o=en(t),s=rn(o),a=tn(n,s);if(e&&r!=r){for(;s>a;)if((i=o[a++])!=i)return!0}else for(;s>a;a++)if((e||a in o)&&o[a]===r)return e||a||0;return!e&&-1}},on={includes:nn(!0),indexOf:nn(!1)},sn=on.includes;br({target:"Array",proto:!0,forced:n((function(){return!Array(1).includes()}))},{includes:function(e){return sn(this,e,arguments.length>1?arguments[1]:void 0)}});var an=Lr("Array").includes,cn=H,un=L,ln=nt("match"),dn=function(e){var t;return cn(e)&&(void 0!==(t=e[ln])?!!t:"RegExp"==un(e))},pn=TypeError,fn=function(e){if(dn(e))throw pn("The method doesn't accept regular expressions");return e},hn={};hn[nt("toStringTag")]="z";var vn="[object z]"===String(hn),mn=vn,yn=b,gn=L,bn=nt("toStringTag"),Sn=Object,Tn="Arguments"==gn(function(){return arguments}()),_n=mn?gn:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=Sn(e),bn))?r:Tn?gn(t):"Object"==(n=gn(t))&&yn(t.callee)?"Arguments":n},Cn=_n,wn=String,En=function(e){if("Symbol"===Cn(e))throw TypeError("Cannot convert a Symbol value to a string");return wn(e)},Rn=nt("match"),Pn=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[Rn]=!1,"/./"[e](t)}catch(e){}}return!1},An=br,Mn=fn,kn=z,On=En,Dn=Pn,In=l("".indexOf);An({target:"String",proto:!0,forced:!Dn("includes")},{includes:function(e){return!!~In(On(kn(this)),On(Mn(e)),arguments.length>1?arguments[1]:void 0)}});var xn=Lr("String").includes,Ln=d,Nn=an,Fn=xn,jn=Array.prototype,Bn=String.prototype,Vn=function(e){var t=e.includes;return e===jn||Ln(jn,e)&&t===jn.includes?Nn:"string"==typeof e||e===Bn||Ln(Bn,e)&&t===Bn.includes?Fn:t},Un={},zn=b,Gn=xe,Wn=l(Function.toString);zn(Gn.inspectSource)||(Gn.inspectSource=function(e){return Wn(e)});var Yn,qn,Hn,Jn=Gn.inspectSource,Qn=b,Kn=Jn,Zn=f.WeakMap,Xn=Qn(Zn)&&/native code/.test(Kn(Zn)),$n=Me.exports,ei=qe,ti=$n("keys"),ri=function(e){return ti[e]||(ti[e]=ei(e))},ni={},ii=Xn,oi=f,si=l,ai=H,ci=ar,ui=Ue,li=xe,di=ri,pi=ni,fi="Object already initialized",hi=oi.TypeError,vi=oi.WeakMap;if(ii||li.state){var mi=li.state||(li.state=new vi),yi=si(mi.get),gi=si(mi.has),bi=si(mi.set);Yn=function(e,t){if(gi(mi,e))throw new hi(fi);return t.facade=e,bi(mi,e,t),t},qn=function(e){return yi(mi,e)||{}},Hn=function(e){return gi(mi,e)}}else{var Si=di("state");pi[Si]=!0,Yn=function(e,t){if(ui(e,Si))throw new hi(fi);return t.facade=e,ci(e,Si,t),t},qn=function(e){return ui(e,Si)?e[Si]:{}},Hn=function(e){return ui(e,Si)}}var Ti={set:Yn,get:qn,has:Hn,enforce:function(e){return Hn(e)?qn(e):Yn(e,{})},getterFor:function(e){return function(t){var r;if(!ai(t)||(r=qn(t)).type!==e)throw hi("Incompatible receiver, "+e+" required");return r}}},_i=T,Ci=Ue,wi=Function.prototype,Ei=_i&&Object.getOwnPropertyDescriptor,Ri=Ci(wi,"name"),Pi={EXISTS:Ri,PROPER:Ri&&"something"===function(){}.name,CONFIGURABLE:Ri&&(!_i||_i&&Ei(wi,"name").configurable)},Ai={},Mi=Ue,ki=Y,Oi=on.indexOf,Di=ni,Ii=l([].push),xi=function(e,t){var r,n=ki(e),i=0,o=[];for(r in n)!Mi(Di,r)&&Mi(n,r)&&Ii(o,r);for(;t.length>i;)Mi(n,r=t[i++])&&(~Oi(o,r)||Ii(o,r));return o},Li=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ni=xi,Fi=Li,ji=Object.keys||function(e){return Ni(e,Fi)},Bi=T,Vi=Gt,Ui=zt,zi=Ht,Gi=Y,Wi=ji;Ai.f=Bi&&!Vi?Object.defineProperties:function(e,t){zi(e);for(var r,n=Gi(t),i=Wi(t),o=i.length,s=0;o>s;)Ui.f(e,r=i[s++],n[r]);return e};var Yi,qi=$("document","documentElement"),Hi=Ht,Ji=Ai,Qi=Li,Ki=ni,Zi=qi,Xi=yt,$i=ri("IE_PROTO"),eo=function(){},to=function(e){return"<script>"+e+"</"+"script>"},ro=function(e){e.write(to("")),e.close();var t=e.parentWindow.Object;return e=null,t},no=function(){try{Yi=new ActiveXObject("htmlfile")}catch(e){}var e,t;no="undefined"!=typeof document?document.domain&&Yi?ro(Yi):((t=Xi("iframe")).style.display="none",Zi.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(to("document.F=Object")),e.close(),e.F):ro(Yi);for(var r=Qi.length;r--;)delete no.prototype[Qi[r]];return no()};Ki[$i]=!0;var io,oo,so,ao=Object.create||function(e,t){var r;return null!==e?(eo.prototype=Hi(e),r=new eo,eo.prototype=null,r[$i]=e):r=no(),void 0===t?r:Ji.f(r,t)},co=!n((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),uo=Ue,lo=b,po=je,fo=co,ho=ri("IE_PROTO"),vo=Object,mo=vo.prototype,yo=fo?vo.getPrototypeOf:function(e){var t=po(e);if(uo(t,ho))return t[ho];var r=t.constructor;return lo(r)&&t instanceof r?r.prototype:t instanceof vo?mo:null},go=ar,bo=function(e,t,r,n){return n&&n.enumerable?e[t]=r:go(e,t,r),e},So=n,To=b,_o=ao,Co=yo,wo=bo,Eo=nt("iterator"),Ro=!1;[].keys&&("next"in(so=[].keys())?(oo=Co(Co(so)))!==Object.prototype&&(io=oo):Ro=!0);var Po=null==io||So((function(){var e={};return io[Eo].call(e)!==e}));To((io=Po?{}:_o(io))[Eo])||wo(io,Eo,(function(){return this}));var Ao={IteratorPrototype:io,BUGGY_SAFARI_ITERATORS:Ro},Mo=_n,ko=vn?{}.toString:function(){return"[object "+Mo(this)+"]"},Oo=vn,Do=zt.f,Io=ar,xo=Ue,Lo=ko,No=nt("toStringTag"),Fo=function(e,t,r,n){if(e){var i=r?e:e.prototype;xo(i,No)||Do(i,No,{configurable:!0,value:t}),n&&!Oo&&Io(i,"toString",Lo)}},jo=Ao.IteratorPrototype,Bo=ao,Vo=O,Uo=Fo,zo=Un,Go=function(){return this},Wo=b,Yo=String,qo=TypeError,Ho=l,Jo=Ht,Qo=function(e){if("object"==typeof e||Wo(e))return e;throw qo("Can't set "+Yo(e)+" as a prototype")},Ko=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=Ho(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(r,[]),t=r instanceof Array}catch(e){}return function(r,n){return Jo(r),Qo(n),t?e(r,n):r.__proto__=n,r}}():void 0),Zo=br,Xo=w,$o=function(e,t,r,n){var i=t+" Iterator";return e.prototype=Bo(jo,{next:Vo(+!n,r)}),Uo(e,i,!1,!0),zo[i]=Go,e},es=yo,ts=Fo,rs=bo,ns=Un,is=Pi.PROPER,os=Ao.BUGGY_SAFARI_ITERATORS,ss=nt("iterator"),as="keys",cs="values",us="entries",ls=function(){return this},ds=function(e,t,r,n,i,o,s){$o(r,t,n);var a,c,u,l=function(e){if(e===i&&v)return v;if(!os&&e in f)return f[e];switch(e){case as:case cs:case us:return function(){return new r(this,e)}}return function(){return new r(this)}},d=t+" Iterator",p=!1,f=e.prototype,h=f[ss]||f["@@iterator"]||i&&f[i],v=!os&&h||l(i),m="Array"==t&&f.entries||h;if(m&&(a=es(m.call(new e)))!==Object.prototype&&a.next&&(ts(a,d,!0,!0),ns[d]=ls),is&&i==cs&&h&&h.name!==cs&&(p=!0,v=function(){return Xo(h,this)}),i)if(c={values:l(cs),keys:o?v:l(as),entries:l(us)},s)for(u in c)(os||p||!(u in f))&&rs(f,u,c[u]);else Zo({target:t,proto:!0,forced:os||p},c);return s&&f[ss]!==v&&rs(f,ss,v,{name:i}),ns[t]=v,c},ps=Y,fs=Un,hs=Ti;zt.f;var vs=ds,ms="Array Iterator",ys=hs.set,gs=hs.getterFor(ms);vs(Array,"Array",(function(e,t){ys(this,{type:ms,target:ps(e),index:0,kind:t})}),(function(){var e=gs(this),t=e.target,r=e.kind,n=e.index++;return!t||n>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:n,done:!1}:"values"==r?{value:t[n],done:!1}:{value:[n,t[n]],done:!1}}),"values"),fs.Arguments=fs.Array;var bs={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Ss=f,Ts=_n,_s=ar,Cs=Un,ws=nt("toStringTag");for(var Es in bs){var Rs=Ss[Es],Ps=Rs&&Rs.prototype;Ps&&Ts(Ps)!==ws&&_s(Ps,ws,Es),Cs[Es]=Cs.Array}var As=L,Ms=Array.isArray||function(e){return"Array"==As(e)},ks=l,Os=n,Ds=b,Is=_n,xs=Jn,Ls=function(){},Ns=[],Fs=$("Reflect","construct"),js=/^\s*(?:class|function)\b/,Bs=ks(js.exec),Vs=!js.exec(Ls),Us=function(e){if(!Ds(e))return!1;try{return Fs(Ls,Ns,e),!0}catch(e){return!1}},zs=function(e){if(!Ds(e))return!1;switch(Is(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Vs||!!Bs(js,xs(e))}catch(e){return!0}};zs.sham=!0;var Gs=!Fs||Os((function(){var e;return Us(Us.call)||!Us(Object)||!Us((function(){e=!0}))||e}))?zs:Us,Ws=Ms,Ys=Gs,qs=H,Hs=nt("species"),Js=Array,Qs=function(e){var t;return Ws(e)&&(t=e.constructor,(Ys(t)&&(t===Js||Ws(t.prototype))||qs(t)&&null===(t=t[Hs]))&&(t=void 0)),void 0===t?Js:t},Ks=Ut,Zs=V,Xs=je,$s=$r,ea=function(e,t){return new(Qs(e))(0===t?0:t)},ta=l([].push),ra=function(e){var t=1==e,r=2==e,n=3==e,i=4==e,o=6==e,s=7==e,a=5==e||o;return function(c,u,l,d){for(var p,f,h=Xs(c),v=Zs(h),m=Ks(u,l),y=$s(v),g=0,b=d||ea,S=t?b(c,y):r||s?b(c,0):void 0;y>g;g++)if((a||g in v)&&(f=m(p=v[g],g,h),e))if(t)S[g]=f;else if(f)switch(e){case 3:return!0;case 5:return p;case 6:return g;case 2:ta(S,p)}else switch(e){case 4:return!1;case 7:ta(S,p)}return o?-1:n||i?i:S}},na={forEach:ra(0),map:ra(1),filter:ra(2),some:ra(3),every:ra(4),find:ra(5),findIndex:ra(6),filterReject:ra(7)},ia=n,oa=function(e,t){var r=[][e];return!!r&&ia((function(){r.call(null,t||function(){return 1},1)}))},sa=na.forEach,aa=oa("forEach")?[].forEach:function(e){return sa(this,e,arguments.length>1?arguments[1]:void 0)};br({target:"Array",proto:!0,forced:[].forEach!=aa},{forEach:aa});var ca=Lr("Array").forEach,ua=_n,la=Ue,da=d,pa=ca,fa=Array.prototype,ha={DOMTokenList:!0,NodeList:!0},va=function(e){var t=e.forEach;return e===fa||da(fa,e)&&t===fa.forEach||la(ha,ua(e))?pa:t},ma=TypeError,ya=function(e,t){if(e<t)throw ma("Not enough arguments");return e},ga=f,ba=g,Sa=b,Ta=Sr,_a=ya,Ca=/MSIE .\./.test(ee),wa=ga.Function,Ea=function(e){return Ca?function(t,r){var n=_a(arguments.length,1)>2,i=Sa(t)?t:wa(t),o=n?Ta(arguments,2):void 0;return e(n?function(){ba(i,this,o)}:i,r)}:e},Ra={setTimeout:Ea(ga.setTimeout),setInterval:Ea(ga.setInterval)},Pa=Ra.setInterval;br({global:!0,bind:!0,forced:f.setInterval!==Pa},{setInterval:Pa});var Aa=Ra.setTimeout;br({global:!0,bind:!0,forced:f.setTimeout!==Aa},{setTimeout:Aa});var Ma=J.setTimeout,ka=br,Oa=l,Da=Zr,Ia=En,xa=fn,La=z,Na=Pn,Fa=Oa("".startsWith),ja=Oa("".slice),Ba=Math.min;ka({target:"String",proto:!0,forced:!Na("startsWith")},{startsWith:function(e){var t=Ia(La(this));xa(e);var r=Da(Ba(arguments.length>1?arguments[1]:void 0,t.length)),n=Ia(e);return Fa?Fa(t,n,r):ja(t,r,r+n.length)===n}});var Va=Lr("String").startsWith,Ua=d,za=Va,Ga=String.prototype,Wa=function(e){var t=e.startsWith;return"string"==typeof e||e===Ga||Ua(Ga,e)&&t===Ga.startsWith?za:t},Ya=J.setInterval,qa=n,Ha=ae,Ja=nt("species"),Qa=function(e){return Ha>=51||!qa((function(){var t=[];return(t.constructor={})[Ja]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},Ka=na.filter;br({target:"Array",proto:!0,forced:!Qa("filter")},{filter:function(e){return Ka(this,e,arguments.length>1?arguments[1]:void 0)}});var Za=Lr("Array").filter,Xa=d,$a=Za,ec=Array.prototype,tc=function(e){var t=e.filter;return e===ec||Xa(ec,e)&&t===ec.filter?$a:t},rc=function(){return rc=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},rc.apply(this,arguments)};function nc(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r}function ic(e,t,r,n){return new(r||(r=Promise))((function(i,o){function s(e){try{c(n.next(e))}catch(e){o(e)}}function a(e){try{c(n.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}c((n=n.apply(e,t||[])).next())}))}function oc(e,t){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}function sc(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function ac(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s}function cc(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}var uc="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==uc&&uc,lc="URLSearchParams"in uc,dc="Symbol"in uc&&"iterator"in Symbol,pc="FileReader"in uc&&"Blob"in uc&&function(){try{return new Blob,!0}catch(e){return!1}}(),fc="FormData"in uc,hc="ArrayBuffer"in uc;if(hc)var vc=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],mc=ArrayBuffer.isView||function(e){return e&&vc.indexOf(Object.prototype.toString.call(e))>-1};function yc(e){if("string"!=typeof e&&(e=String(e)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(e)||""===e)throw new TypeError('Invalid character in header field name: "'+e+'"');return e.toLowerCase()}function gc(e){return"string"!=typeof e&&(e=String(e)),e}function bc(e){var t={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return dc&&(t[Symbol.iterator]=function(){return t}),t}function Sc(e){this.map={},e instanceof Sc?e.forEach((function(e,t){this.append(t,e)}),this):Array.isArray(e)?e.forEach((function(e){this.append(e[0],e[1])}),this):e&&Object.getOwnPropertyNames(e).forEach((function(t){this.append(t,e[t])}),this)}function Tc(e){if(e.bodyUsed)return Promise.reject(new TypeError("Already read"));e.bodyUsed=!0}function _c(e){return new Promise((function(t,r){e.onload=function(){t(e.result)},e.onerror=function(){r(e.error)}}))}function Cc(e){var t=new FileReader,r=_c(t);return t.readAsArrayBuffer(e),r}function wc(e){if(e.slice)return e.slice(0);var t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}function Ec(){return this.bodyUsed=!1,this._initBody=function(e){var t;this.bodyUsed=this.bodyUsed,this._bodyInit=e,e?"string"==typeof e?this._bodyText=e:pc&&Blob.prototype.isPrototypeOf(e)?this._bodyBlob=e:fc&&FormData.prototype.isPrototypeOf(e)?this._bodyFormData=e:lc&&URLSearchParams.prototype.isPrototypeOf(e)?this._bodyText=e.toString():hc&&pc&&((t=e)&&DataView.prototype.isPrototypeOf(t))?(this._bodyArrayBuffer=wc(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):hc&&(ArrayBuffer.prototype.isPrototypeOf(e)||mc(e))?this._bodyArrayBuffer=wc(e):this._bodyText=e=Object.prototype.toString.call(e):this._bodyText="",this.headers.get("content-type")||("string"==typeof e?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):lc&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},pc&&(this.blob=function(){var e=Tc(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){if(this._bodyArrayBuffer){var e=Tc(this);return e||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}return this.blob().then(Cc)}),this.text=function(){var e=Tc(this);if(e)return e;if(this._bodyBlob)return function(e){var t=new FileReader,r=_c(t);return t.readAsText(e),r}(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(function(e){for(var t=new Uint8Array(e),r=new Array(t.length),n=0;n<t.length;n++)r[n]=String.fromCharCode(t[n]);return r.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},fc&&(this.formData=function(){return this.text().then(Ac)}),this.json=function(){return this.text().then(JSON.parse)},this}Sc.prototype.append=function(e,t){e=yc(e),t=gc(t);var r=this.map[e];this.map[e]=r?r+", "+t:t},Sc.prototype.delete=function(e){delete this.map[yc(e)]},Sc.prototype.get=function(e){return e=yc(e),this.has(e)?this.map[e]:null},Sc.prototype.has=function(e){return this.map.hasOwnProperty(yc(e))},Sc.prototype.set=function(e,t){this.map[yc(e)]=gc(t)},Sc.prototype.forEach=function(e,t){for(var r in this.map)this.map.hasOwnProperty(r)&&e.call(t,this.map[r],r,this)},Sc.prototype.keys=function(){var e=[];return this.forEach((function(t,r){e.push(r)})),bc(e)},Sc.prototype.values=function(){var e=[];return this.forEach((function(t){e.push(t)})),bc(e)},Sc.prototype.entries=function(){var e=[];return this.forEach((function(t,r){e.push([r,t])})),bc(e)},dc&&(Sc.prototype[Symbol.iterator]=Sc.prototype.entries);var Rc=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function Pc(e,t){if(!(this instanceof Pc))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var r=(t=t||{}).body;if(e instanceof Pc){if(e.bodyUsed)throw new TypeError("Already read");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new Sc(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,r||null==e._bodyInit||(r=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=t.credentials||this.credentials||"same-origin",!t.headers&&this.headers||(this.headers=new Sc(t.headers)),this.method=function(e){var t=e.toUpperCase();return Rc.indexOf(t)>-1?t:e}(t.method||this.method||"GET"),this.mode=t.mode||this.mode||null,this.signal=t.signal||this.signal,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&r)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(r),!("GET"!==this.method&&"HEAD"!==this.method||"no-store"!==t.cache&&"no-cache"!==t.cache)){var n=/([?&])_=[^&]*/;if(n.test(this.url))this.url=this.url.replace(n,"$1_="+(new Date).getTime());else{this.url+=(/\?/.test(this.url)?"&":"?")+"_="+(new Date).getTime()}}}function Ac(e){var t=new FormData;return e.trim().split("&").forEach((function(e){if(e){var r=e.split("="),n=r.shift().replace(/\+/g," "),i=r.join("=").replace(/\+/g," ");t.append(decodeURIComponent(n),decodeURIComponent(i))}})),t}function Mc(e,t){if(!(this instanceof Mc))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');t||(t={}),this.type="default",this.status=void 0===t.status?200:t.status,this.ok=this.status>=200&&this.status<300,this.statusText=void 0===t.statusText?"":""+t.statusText,this.headers=new Sc(t.headers),this.url=t.url||"",this._initBody(e)}Pc.prototype.clone=function(){return new Pc(this,{body:this._bodyInit})},Ec.call(Pc.prototype),Ec.call(Mc.prototype),Mc.prototype.clone=function(){return new Mc(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new Sc(this.headers),url:this.url})},Mc.error=function(){var e=new Mc(null,{status:0,statusText:""});return e.type="error",e};var kc=[301,302,303,307,308];Mc.redirect=function(e,t){if(-1===kc.indexOf(t))throw new RangeError("Invalid status code");return new Mc(null,{status:t,headers:{location:e}})};var Oc=uc.DOMException;try{new Oc}catch(e){(Oc=function(e,t){this.message=e,this.name=t;var r=Error(e);this.stack=r.stack}).prototype=Object.create(Error.prototype),Oc.prototype.constructor=Oc}function Dc(e,t){return new Promise((function(r,n){var i=new Pc(e,t);if(i.signal&&i.signal.aborted)return n(new Oc("Aborted","AbortError"));var o=new XMLHttpRequest;function s(){o.abort()}o.onload=function(){var e,t,n={status:o.status,statusText:o.statusText,headers:(e=o.getAllResponseHeaders()||"",t=new Sc,e.replace(/\r?\n[\t ]+/g," ").split("\r").map((function(e){return 0===e.indexOf("\n")?e.substr(1,e.length):e})).forEach((function(e){var r=e.split(":"),n=r.shift().trim();if(n){var i=r.join(":").trim();t.append(n,i)}})),t)};n.url="responseURL"in o?o.responseURL:n.headers.get("X-Request-URL");var i="response"in o?o.response:o.responseText;setTimeout((function(){r(new Mc(i,n))}),0)},o.onerror=function(){setTimeout((function(){n(new TypeError("Network request failed"))}),0)},o.ontimeout=function(){setTimeout((function(){n(new TypeError("Network request failed"))}),0)},o.onabort=function(){setTimeout((function(){n(new Oc("Aborted","AbortError"))}),0)},o.open(i.method,function(e){try{return""===e&&uc.location.href?uc.location.href:e}catch(t){return e}}(i.url),!0),"include"===i.credentials?o.withCredentials=!0:"omit"===i.credentials&&(o.withCredentials=!1),"responseType"in o&&(pc?o.responseType="blob":hc&&i.headers.get("Content-Type")&&-1!==i.headers.get("Content-Type").indexOf("application/octet-stream")&&(o.responseType="arraybuffer")),!t||"object"!=typeof t.headers||t.headers instanceof Sc?i.headers.forEach((function(e,t){o.setRequestHeader(t,e)})):Object.getOwnPropertyNames(t.headers).forEach((function(e){o.setRequestHeader(e,gc(t.headers[e]))})),i.signal&&(i.signal.addEventListener("abort",s),o.onreadystatechange=function(){4===o.readyState&&i.signal.removeEventListener("abort",s)}),o.send(void 0===i._bodyInit?null:i._bodyInit)}))}Dc.polyfill=!0,uc.fetch||(uc.fetch=Dc,uc.Headers=Sc,uc.Request=Pc,uc.Response=Mc);let Ic=!0,xc=!0;function Lc(e,t,r){const n=e.match(t);return n&&n.length>=r&&parseInt(n[r],10)}function Nc(e,t,r){if(!e.RTCPeerConnection)return;const n=e.RTCPeerConnection.prototype,i=n.addEventListener;n.addEventListener=function(e,n){if(e!==t)return i.apply(this,arguments);const o=e=>{const t=r(e);t&&(n.handleEvent?n.handleEvent(t):n(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(n,o),i.apply(this,[e,o])};const o=n.removeEventListener;n.removeEventListener=function(e,r){if(e!==t||!this._eventMap||!this._eventMap[t])return o.apply(this,arguments);if(!this._eventMap[t].has(r))return o.apply(this,arguments);const n=this._eventMap[t].get(r);return this._eventMap[t].delete(r),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[e,n])},Object.defineProperty(n,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function Fc(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(Ic=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function jc(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(xc=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function Bc(){if("object"==typeof window){if(Ic)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function Vc(e,t){xc&&console.warn(e+" is deprecated, please use "+t+" instead.")}function Uc(e){return"[object Object]"===Object.prototype.toString.call(e)}function zc(e){return Uc(e)?Object.keys(e).reduce((function(t,r){const n=Uc(e[r]),i=n?zc(e[r]):e[r],o=n&&!Object.keys(i).length;return void 0===i||o?t:Object.assign(t,{[r]:i})}),{}):e}function Gc(e,t,r){t&&!r.has(t.id)&&(r.set(t.id,t),Object.keys(t).forEach((n=>{n.endsWith("Id")?Gc(e,e.get(t[n]),r):n.endsWith("Ids")&&t[n].forEach((t=>{Gc(e,e.get(t),r)}))})))}function Wc(e,t,r){const n=r?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;const o=[];return e.forEach((e=>{"track"===e.type&&e.trackIdentifier===t.id&&o.push(e)})),o.forEach((t=>{e.forEach((r=>{r.type===n&&r.trackId===t.id&&Gc(e,r,i)}))})),i}const Yc=Bc;function qc(e,t){const r=e&&e.navigator;if(!r.mediaDevices)return;const n=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach((r=>{if("require"===r||"advanced"===r||"mediaSource"===r)return;const n="object"==typeof e[r]?e[r]:{ideal:e[r]};void 0!==n.exact&&"number"==typeof n.exact&&(n.min=n.max=n.exact);const i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==n.ideal){t.optional=t.optional||[];let e={};"number"==typeof n.ideal?(e[i("min",r)]=n.ideal,t.optional.push(e),e={},e[i("max",r)]=n.ideal,t.optional.push(e)):(e[i("",r)]=n.ideal,t.optional.push(e))}void 0!==n.exact&&"number"!=typeof n.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",r)]=n.exact):["min","max"].forEach((e=>{void 0!==n[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,r)]=n[e])}))})),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},i=function(e,i){if(t.version>=61)return i(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=n(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});const s=t.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&(!r.mediaDevices.getSupportedConstraints||!r.mediaDevices.getSupportedConstraints().facingMode||s)){let t;if(delete e.video.facingMode,"environment"===o.exact||"environment"===o.ideal?t=["back","rear"]:"user"!==o.exact&&"user"!==o.ideal||(t=["front"]),t)return r.mediaDevices.enumerateDevices().then((r=>{let s=(r=r.filter((e=>"videoinput"===e.kind))).find((e=>t.some((t=>e.label.toLowerCase().includes(t)))));return!s&&r.length&&t.includes("back")&&(s=r[r.length-1]),s&&(e.video.deviceId=o.exact?{exact:s.deviceId}:{ideal:s.deviceId}),e.video=n(e.video),Yc("chrome: "+JSON.stringify(e)),i(e)}))}e.video=n(e.video)}return Yc("chrome: "+JSON.stringify(e)),i(e)},o=function(e){return t.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(r.getUserMedia=function(e,t,n){i(e,(e=>{r.webkitGetUserMedia(e,t,(e=>{n&&n(o(e))}))}))}.bind(r),r.mediaDevices.getUserMedia){const e=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(t){return i(t,(t=>e(t).then((e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach((e=>{e.stop()})),new DOMException("","NotFoundError");return e}),(e=>Promise.reject(o(e))))))}}}function Hc(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function Jc(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",(r=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===r.track.id)):{track:r.track};const i=new Event("track");i.track=r.track,i.receiver=n,i.transceiver={receiver:n},i.streams=[t.stream],this.dispatchEvent(i)})),t.stream.getTracks().forEach((r=>{let n;n=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===r.id)):{track:r};const i=new Event("track");i.track=r,i.receiver=n,i.transceiver={receiver:n},i.streams=[t.stream],this.dispatchEvent(i)}))},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else Nc(e,"track",(e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e)))}function Qc(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const r=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){let i=r.apply(this,arguments);return i||(i=t(this,e),this._senders.push(i)),i};const n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){n.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach((e=>{this._senders.push(t(this,e))}))};const n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach((e=>{const t=this._senders.find((t=>t.track===e));t&&this._senders.splice(this._senders.indexOf(t),1)}))}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function Kc(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,r,n]=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const i=function(e){const t={};return e.result().forEach((e=>{const r={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach((t=>{r[t]=e.stat(t)})),t[r.id]=r})),t},o=function(e){return new Map(Object.keys(e).map((t=>[t,e[t]])))};if(arguments.length>=2){const n=function(e){r(o(i(e)))};return t.apply(this,[n,e])}return new Promise(((e,r)=>{t.apply(this,[function(t){e(o(i(t)))},r])})).then(r,n)}}function Zc(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=r.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>Wc(t,e.track,!0)))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),Nc(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>Wc(t,e.track,!1)))}}if(!("getStats"in e.RTCRtpSender.prototype)||!("getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,r,n;return this.getSenders().forEach((r=>{r.track===e&&(t?n=!0:t=r)})),this.getReceivers().forEach((t=>(t.track===e&&(r?n=!0:r=t),t.track===e))),n||t&&r?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():r?r.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function Xc(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map((e=>this._shimmedLocalStreams[e][0]))};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){if(!r)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const n=t.apply(this,arguments);return this._shimmedLocalStreams[r.id]?-1===this._shimmedLocalStreams[r.id].indexOf(n)&&this._shimmedLocalStreams[r.id].push(n):this._shimmedLocalStreams[r.id]=[r,n],n};const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")}));const t=this.getSenders();r.apply(this,arguments);const n=this.getSenders().filter((e=>-1===t.indexOf(e)));this._shimmedLocalStreams[e.id]=[e].concat(n)};const n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],n.apply(this,arguments)};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach((t=>{const r=this._shimmedLocalStreams[t].indexOf(e);-1!==r&&this._shimmedLocalStreams[t].splice(r,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]})),i.apply(this,arguments)}}function $c(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return Xc(e);const r=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=r.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map((e=>this._reverseStreams[e.id]))};const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")})),!this._reverseStreams[t.id]){const r=new e.MediaStream(t.getTracks());this._streams[t.id]=r,this._reverseStreams[r.id]=t,t=r}n.apply(this,[t])};const i=e.RTCPeerConnection.prototype.removeStream;function o(e,t){let r=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const n=e._reverseStreams[t],i=e._streams[n.id];r=r.replace(new RegExp(i.id,"g"),n.id)})),new RTCSessionDescription({type:t.type,sdp:r})}function s(e,t){let r=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const n=e._reverseStreams[t],i=e._streams[n.id];r=r.replace(new RegExp(n.id,"g"),i.id)})),new RTCSessionDescription({type:t.type,sdp:r})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,r){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const n=[].slice.call(arguments,1);if(1!==n.length||!n[0].getTracks().find((e=>e===t)))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");const i=this.getSenders().find((e=>e.track===t));if(i)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const o=this._streams[r.id];if(o)o.addTrack(t),Promise.resolve().then((()=>{this.dispatchEvent(new Event("negotiationneeded"))}));else{const n=new e.MediaStream([t]);this._streams[r.id]=n,this._reverseStreams[n.id]=r,this.addStream(n)}return this.getSenders().find((e=>e.track===t))},["createOffer","createAnswer"].forEach((function(t){const r=e.RTCPeerConnection.prototype[t],n={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?r.apply(this,[t=>{const r=o(this,t);e[0].apply(null,[r])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):r.apply(this,arguments).then((e=>o(this,e)))}};e.RTCPeerConnection.prototype[t]=n[t]}));const a=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=s(this,arguments[0]),a.apply(this,arguments)):a.apply(this,arguments)};const c=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=c.get.apply(this);return""===e.type?e:o(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(e._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach((r=>{this._streams[r].getTracks().find((t=>e.track===t))&&(t=this._streams[r])})),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function eu(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const r=e.RTCPeerConnection.prototype[t],n={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=n[t]}))}function tu(e,t){Nc(e,"negotiationneeded",(e=>{const r=e.target;if(!(t.version<72||r.getConfiguration&&"plan-b"===r.getConfiguration().sdpSemantics)||"stable"===r.signalingState)return e}))}var ru=Object.freeze({__proto__:null,shimMediaStream:Hc,shimOnTrack:Jc,shimGetSendersWithDtmf:Qc,shimGetStats:Kc,shimSenderReceiverGetStats:Zc,shimAddTrackRemoveTrackWithNative:Xc,shimAddTrackRemoveTrack:$c,shimPeerConnection:eu,fixNegotiationNeeded:tu,shimGetUserMedia:qc,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(r){return t(r).then((t=>{const n=r.video&&r.video.width,i=r.video&&r.video.height,o=r.video&&r.video.frameRate;return r.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:o||3}},n&&(r.video.mandatory.maxWidth=n),i&&(r.video.mandatory.maxHeight=i),e.navigator.mediaDevices.getUserMedia(r)}))}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}});function nu(e,t){const r=e&&e.navigator,n=e&&e.MediaStreamTrack;if(r.getUserMedia=function(e,t,n){Vc("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(e).then(t,n)},!(t.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){const e=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])},t=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(r){return"object"==typeof r&&"object"==typeof r.audio&&(r=JSON.parse(JSON.stringify(r)),e(r.audio,"autoGainControl","mozAutoGainControl"),e(r.audio,"noiseSuppression","mozNoiseSuppression")),t(r)},n&&n.prototype.getSettings){const t=n.prototype.getSettings;n.prototype.getSettings=function(){const r=t.apply(this,arguments);return e(r,"mozAutoGainControl","autoGainControl"),e(r,"mozNoiseSuppression","noiseSuppression"),r}}if(n&&n.prototype.applyConstraints){const t=n.prototype.applyConstraints;n.prototype.applyConstraints=function(r){return"audio"===this.kind&&"object"==typeof r&&(r=JSON.parse(JSON.stringify(r)),e(r,"autoGainControl","mozAutoGainControl"),e(r,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[r])}}}}function iu(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function ou(e,t){if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const r=e.RTCPeerConnection.prototype[t],n={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=n[t]}));const r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,i,o]=arguments;return n.apply(this,[e||null]).then((e=>{if(t.version<53&&!i)try{e.forEach((e=>{e.type=r[e.type]||e.type}))}catch(t){if("TypeError"!==t.name)throw t;e.forEach(((t,n)=>{e.set(n,Object.assign({},t,{type:r[t.type]||t.type}))}))}return e})).then(i,o)}}function su(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=r.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function au(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),Nc(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function cu(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function(e){Vc("removeStream","removeTrack"),this.getSenders().forEach((t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)}))})}function uu(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function lu(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];const e=arguments[1],r=e&&"sendEncodings"in e;r&&e.sendEncodings.forEach((e=>{if("rid"in e){if(!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")}));const n=t.apply(this,arguments);if(r){const{sender:t}=n,r=t.getParameters();(!("encodings"in r)||1===r.encodings.length&&0===Object.keys(r.encodings[0]).length)&&(r.encodings=e.sendEncodings,t.sendEncodings=e.sendEncodings,this.setParametersPromises.push(t.setParameters(r).then((()=>{delete t.sendEncodings})).catch((()=>{delete t.sendEncodings}))))}return n})}function du(e){if("object"!=typeof e||!e.RTCRtpSender)return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function pu(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}function fu(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}var hu=Object.freeze({__proto__:null,shimOnTrack:iu,shimPeerConnection:ou,shimSenderGetStats:su,shimReceiverGetStats:au,shimRemoveStream:cu,shimRTCDataChannel:uu,shimAddTransceiver:lu,shimGetParameters:du,shimCreateOffer:pu,shimCreateAnswer:fu,shimGetUserMedia:nu,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(r){if(!r||!r.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===r.video?r.video={mediaSource:t}:r.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(r)})}});function vu(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach((r=>t.call(this,r,e))),e.getVideoTracks().forEach((r=>t.call(this,r,e)))},e.RTCPeerConnection.prototype.addTrack=function(e,...r){return r&&r.forEach((e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]})),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const r=e.getTracks();this.getSenders().forEach((e=>{r.includes(e.track)&&this.removeTrack(e)}))})}}function mu(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach((e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)}))})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach((t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const r=new Event("addstream");r.stream=t,e.dispatchEvent(r)}))}),t.apply(e,arguments)}}}function yu(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,r=t.createOffer,n=t.createAnswer,i=t.setLocalDescription,o=t.setRemoteDescription,s=t.addIceCandidate;t.createOffer=function(e,t){const n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){const r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i};let a=function(e,t,r){const n=i.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n};t.setLocalDescription=a,a=function(e,t,r){const n=o.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.setRemoteDescription=a,a=function(e,t,r){const n=s.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.addIceCandidate=a}function gu(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,r=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>r(bu(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,r,n){t.mediaDevices.getUserMedia(e).then(r,n)}.bind(t))}function bu(e){return e&&void 0!==e.video?Object.assign({},e,{video:zc(e.video)}):e}function Su(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,r){if(e&&e.iceServers){const t=[];for(let r=0;r<e.iceServers.length;r++){let n=e.iceServers[r];!n.hasOwnProperty("urls")&&n.hasOwnProperty("url")?(Vc("RTCIceServer.url","RTCIceServer.urls"),n=JSON.parse(JSON.stringify(n)),n.urls=n.url,delete n.url,t.push(n)):t.push(e.iceServers[r])}e.iceServers=t}return new t(e,r)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function Tu(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function _u(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find((e=>"audio"===e.receiver.track.kind));!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio",{direction:"recvonly"}),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const r=this.getTransceivers().find((e=>"video"===e.receiver.track.kind));!1===e.offerToReceiveVideo&&r?"sendrecv"===r.direction?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":"recvonly"===r.direction&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):!0!==e.offerToReceiveVideo||r||this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function Cu(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var wu=Object.freeze({__proto__:null,shimLocalStreamsAPI:vu,shimRemoteStreamsAPI:mu,shimCallbacksAPI:yu,shimGetUserMedia:gu,shimConstraints:bu,shimRTCIceServerUrls:Su,shimTrackEventTransceiver:Tu,shimCreateOfferLegacy:_u,shimAudioContext:Cu}),Eu={exports:{}};!function(e){const t={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map((e=>e.trim()))},t.splitSections=function(e){return e.split("\nm=").map(((e,t)=>(t>0?"m="+e:e).trim()+"\r\n"))},t.getDescription=function(e){const r=t.splitSections(e);return r&&r[0]},t.getMediaSections=function(e){const r=t.splitSections(e);return r.shift(),r},t.matchPrefix=function(e,r){return t.splitLines(e).filter((e=>0===e.indexOf(r)))},t.parseCandidate=function(e){let t;t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" ");const r={foundation:t[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]};for(let e=8;e<t.length;e+=2)switch(t[e]){case"raddr":r.relatedAddress=t[e+1];break;case"rport":r.relatedPort=parseInt(t[e+1],10);break;case"tcptype":r.tcpType=t[e+1];break;case"ufrag":r.ufrag=t[e+1],r.usernameFragment=t[e+1];break;default:void 0===r[t[e]]&&(r[t[e]]=t[e+1])}return r},t.writeCandidate=function(e){const t=[];t.push(e.foundation);const r=e.component;"rtp"===r?t.push(1):"rtcp"===r?t.push(2):t.push(r),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);const n=e.type;return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substr(14).split(" ")},t.parseRtpMap=function(e){let t=e.substr(9).split(" ");const r={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),r.name=t[0],r.clockRate=parseInt(t[1],10),r.channels=3===t.length?parseInt(t[2],10):1,r.numChannels=r.channels,r},t.writeRtpMap=function(e){let t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);const r=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==r?"/"+r:"")+"\r\n"},t.parseExtmap=function(e){const t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},t.parseFmtp=function(e){const t={};let r;const n=e.substr(e.indexOf(" ")+1).split(";");for(let e=0;e<n.length;e++)r=n[e].trim().split("="),t[r[0].trim()]=r[1];return t},t.writeFmtp=function(e){let t="",r=e.payloadType;if(void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){const n=[];Object.keys(e.parameters).forEach((t=>{void 0!==e.parameters[t]?n.push(t+"="+e.parameters[t]):n.push(t)})),t+="a=fmtp:"+r+" "+n.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){const t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){let t="",r=e.payloadType;return void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((e=>{t+="a=rtcp-fb:"+r+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},t.parseSsrcMedia=function(e){const t=e.indexOf(" "),r={ssrc:parseInt(e.substr(7,t-7),10)},n=e.indexOf(":",t);return n>-1?(r.attribute=e.substr(t+1,n-t-1),r.value=e.substr(n+1)):r.attribute=e.substr(t+1),r},t.parseSsrcGroup=function(e){const t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((e=>parseInt(e,10)))}},t.getMid=function(e){const r=t.matchPrefix(e,"a=mid:")[0];if(r)return r.substr(6)},t.parseFingerprint=function(e){const t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},t.getDtlsParameters=function(e,r){return{role:"auto",fingerprints:t.matchPrefix(e+r,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){let r="a=setup:"+t+"\r\n";return e.fingerprints.forEach((e=>{r+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),r},t.parseCryptoLine=function(e){const t=e.substr(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;const t=e.substr(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,r){return t.matchPrefix(e+r,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,r){const n=t.matchPrefix(e+r,"a=ice-ufrag:")[0],i=t.matchPrefix(e+r,"a=ice-pwd:")[0];return n&&i?{usernameFragment:n.substr(12),password:i.substr(10)}:null},t.writeIceParameters=function(e){let t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},t.parseRtpParameters=function(e){const r={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=t.splitLines(e)[0].split(" ");for(let i=3;i<n.length;i++){const o=n[i],s=t.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(s){const n=t.parseRtpMap(s),i=t.matchPrefix(e,"a=fmtp:"+o+" ");switch(n.parameters=i.length?t.parseFmtp(i[0]):{},n.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(t.parseRtcpFb),r.codecs.push(n),n.name.toUpperCase()){case"RED":case"ULPFEC":r.fecMechanisms.push(n.name.toUpperCase())}}}return t.matchPrefix(e,"a=extmap:").forEach((e=>{r.headerExtensions.push(t.parseExtmap(e))})),r},t.writeRtpDescription=function(e,r){let n="";n+="m="+e+" ",n+=r.codecs.length>0?"9":"0",n+=" UDP/TLS/RTP/SAVPF ",n+=r.codecs.map((e=>void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType)).join(" ")+"\r\n",n+="c=IN IP4 0.0.0.0\r\n",n+="a=rtcp:9 IN IP4 0.0.0.0\r\n",r.codecs.forEach((e=>{n+=t.writeRtpMap(e),n+=t.writeFmtp(e),n+=t.writeRtcpFb(e)}));let i=0;return r.codecs.forEach((e=>{e.maxptime>i&&(i=e.maxptime)})),i>0&&(n+="a=maxptime:"+i+"\r\n"),r.headerExtensions&&r.headerExtensions.forEach((e=>{n+=t.writeExtmap(e)})),n},t.parseRtpEncodingParameters=function(e){const r=[],n=t.parseRtpParameters(e),i=-1!==n.fecMechanisms.indexOf("RED"),o=-1!==n.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute)),a=s.length>0&&s[0].ssrc;let c;const u=t.matchPrefix(e,"a=ssrc-group:FID").map((e=>e.substr(17).split(" ").map((e=>parseInt(e,10)))));u.length>0&&u[0].length>1&&u[0][0]===a&&(c=u[0][1]),n.codecs.forEach((e=>{if("RTX"===e.name.toUpperCase()&&e.parameters.apt){let t={ssrc:a,codecPayloadType:parseInt(e.parameters.apt,10)};a&&c&&(t.rtx={ssrc:c}),r.push(t),i&&(t=JSON.parse(JSON.stringify(t)),t.fec={ssrc:a,mechanism:o?"red+ulpfec":"red"},r.push(t))}})),0===r.length&&a&&r.push({ssrc:a});let l=t.matchPrefix(e,"b=");return l.length&&(l=0===l[0].indexOf("b=TIAS:")?parseInt(l[0].substr(7),10):0===l[0].indexOf("b=AS:")?1e3*parseInt(l[0].substr(5),10)*.95-16e3:void 0,r.forEach((e=>{e.maxBitrate=l}))),r},t.parseRtcpParameters=function(e){const r={},n=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute))[0];n&&(r.cname=n.value,r.ssrc=n.ssrc);const i=t.matchPrefix(e,"a=rtcp-rsize");r.reducedSize=i.length>0,r.compound=0===i.length;const o=t.matchPrefix(e,"a=rtcp-mux");return r.mux=o.length>0,r},t.writeRtcpParameters=function(e){let t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},t.parseMsid=function(e){let r;const n=t.matchPrefix(e,"a=msid:");if(1===n.length)return r=n[0].substr(7).split(" "),{stream:r[0],track:r[1]};const i=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"msid"===e.attribute));return i.length>0?(r=i[0].value.split(" "),{stream:r[0],track:r[1]}):void 0},t.parseSctpDescription=function(e){const r=t.parseMLine(e),n=t.matchPrefix(e,"a=max-message-size:");let i;n.length>0&&(i=parseInt(n[0].substr(19),10)),isNaN(i)&&(i=65536);const o=t.matchPrefix(e,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substr(12),10),protocol:r.fmt,maxMessageSize:i};const s=t.matchPrefix(e,"a=sctpmap:");if(s.length>0){const e=s[0].substr(10).split(" ");return{port:parseInt(e[0],10),protocol:e[1],maxMessageSize:i}}},t.writeSctpDescription=function(e,t){let r=[];return r="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&r.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),r.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,21)},t.writeSessionBoilerplate=function(e,r,n){let i;const o=void 0!==r?r:2;i=e||t.generateSessionId();return"v=0\r\no="+(n||"thisisadapterortc")+" "+i+" "+o+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.getDirection=function(e,r){const n=t.splitLines(e);for(let e=0;e<n.length;e++)switch(n[e]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[e].substr(2)}return r?t.getDirection(r):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substr(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){const r=t.splitLines(e)[0].substr(2).split(" ");return{kind:r[0],port:parseInt(r[1],10),protocol:r[2],fmt:r.slice(3).join(" ")}},t.parseOLine=function(e){const r=t.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:r[0],sessionId:r[1],sessionVersion:parseInt(r[2],10),netType:r[3],addressType:r[4],address:r[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;const r=t.splitLines(e);for(let e=0;e<r.length;e++)if(r[e].length<2||"="!==r[e].charAt(1))return!1;return!0},e.exports=t}(Eu);var Ru=Eu.exports,Pu=Object.freeze(e({__proto__:null,default:Ru},[Eu.exports]));function Au(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length){const r=new t(e),n=Ru.parseCandidate(e.candidate),i=Object.assign(r,n);return i.toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,Nc(e,"icecandidate",(t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t)))}function Mu(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const r=function(e){if(!e||!e.sdp)return!1;const t=Ru.splitSections(e.sdp);return t.shift(),t.some((e=>{const t=Ru.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")}))},n=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const r=parseInt(t[1],10);return r!=r?-1:r},i=function(e){let r=65536;return"firefox"===t.browser&&(r=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),r},o=function(e,r){let n=65536;"firefox"===t.browser&&57===t.version&&(n=65535);const i=Ru.matchPrefix(e.sdp,"a=max-message-size:");return i.length>0?n=parseInt(i[0].substr(19),10):"firefox"===t.browser&&-1!==r&&(n=2147483637),n},s=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(r(arguments[0])){const e=n(arguments[0]),t=i(e),r=o(arguments[0],e);let s;s=0===t&&0===r?Number.POSITIVE_INFINITY:0===t||0===r?Math.max(t,r):Math.min(t,r);const a={};Object.defineProperty(a,"maxMessageSize",{get:()=>s}),this._sctp=a}return s.apply(this,arguments)}}function ku(e){if(!e.RTCPeerConnection||!("createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const r=e.send;e.send=function(){const n=arguments[0],i=n.length||n.size||n.byteLength;if("open"===e.readyState&&t.sctp&&i>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return r.apply(e,arguments)}}const r=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=r.apply(this,arguments);return t(e,this),e},Nc(e,"datachannel",(e=>(t(e.channel,e.target),e)))}function Ou(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach((e=>{const r=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const r=new Event("connectionstatechange",e);t.dispatchEvent(r)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),r.apply(this,arguments)}}))}function Du(e,t){if(!e.RTCPeerConnection)return;if("chrome"===t.browser&&t.version>=71)return;if("safari"===t.browser&&t.version>=605)return;const r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){const r=t.sdp.split("\n").filter((e=>"a=extmap-allow-mixed"!==e.trim())).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:r}):t.sdp=r}return r.apply(this,arguments)}}function Iu(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const r=e.RTCPeerConnection.prototype.addIceCandidate;r&&0!==r.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function xu(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const r=e.RTCPeerConnection.prototype.setLocalDescription;r&&0!==r.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){let e=arguments[0]||{};if("object"!=typeof e||e.type&&e.sdp)return r.apply(this,arguments);if(e={type:e.type,sdp:e.sdp},!e.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":e.type="offer";break;default:e.type="answer"}if(e.sdp||"offer"!==e.type&&"answer"!==e.type)return r.apply(this,[e]);const t="offer"===e.type?this.createOffer:this.createAnswer;return t.apply(this).then((e=>r.apply(this,[e])))})}var Lu=Object.freeze({__proto__:null,shimRTCIceCandidate:Au,shimMaxMessageSize:Mu,shimSendThrowTypeError:ku,shimConnectionState:Ou,removeExtmapAllowMixed:Du,shimAddIceCandidateNullOrEmpty:Iu,shimParameterlessSetLocalDescription:xu});const Nu=function({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const r=Bc,n=function(e){const t={browser:null,version:null};if(void 0===e||!e.navigator)return t.browser="Not a browser.",t;const{navigator:r}=e;if(r.mozGetUserMedia)t.browser="firefox",t.version=Lc(r.userAgent,/Firefox\/(\d+)\./,1);else if(r.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)t.browser="chrome",t.version=Lc(r.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!e.RTCPeerConnection||!r.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=Lc(r.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return t}(e),i={browserDetails:n,commonShim:Lu,extractVersion:Lc,disableLog:Fc,disableWarnings:jc,sdp:Pu};switch(n.browser){case"chrome":if(!ru||!eu||!t.shimChrome)return r("Chrome shim is not included in this adapter release."),i;if(null===n.version)return r("Chrome shim can not determine version, not shimming."),i;r("adapter.js shimming chrome."),i.browserShim=ru,Iu(e,n),xu(e),qc(e,n),Hc(e),eu(e,n),Jc(e),$c(e,n),Qc(e),Kc(e),Zc(e),tu(e,n),Au(e),Ou(e),Mu(e,n),ku(e),Du(e,n);break;case"firefox":if(!hu||!ou||!t.shimFirefox)return r("Firefox shim is not included in this adapter release."),i;r("adapter.js shimming firefox."),i.browserShim=hu,Iu(e,n),xu(e),nu(e,n),ou(e,n),iu(e),cu(e),su(e),au(e),uu(e),lu(e),du(e),pu(e),fu(e),Au(e),Ou(e),Mu(e,n),ku(e);break;case"safari":if(!wu||!t.shimSafari)return r("Safari shim is not included in this adapter release."),i;r("adapter.js shimming safari."),i.browserShim=wu,Iu(e,n),xu(e),Su(e),_u(e),yu(e),vu(e),mu(e),Tu(e),gu(e),Cu(e),Au(e),Mu(e,n),ku(e),Du(e,n);break;default:r("Unsupported browser!")}return i}({window:"undefined"==typeof window?void 0:window});let Fu=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"");var ju={exports:{}};!function(e,t){e.exports=function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=90)}({17:function(e,t,r){t.__esModule=!0,t.default=void 0;var n=r(18),i=function(){function e(){}return e.getFirstMatch=function(e,t){var r=t.match(e);return r&&r.length>0&&r[1]||""},e.getSecondMatch=function(e,t){var r=t.match(e);return r&&r.length>1&&r[2]||""},e.matchAndReturnConst=function(e,t,r){if(e.test(t))return r},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,r,n){void 0===n&&(n=!1);var i=e.getVersionPrecision(t),o=e.getVersionPrecision(r),s=Math.max(i,o),a=0,c=e.map([t,r],(function(t){var r=s-e.getVersionPrecision(t),n=t+new Array(r+1).join(".0");return e.map(n.split("."),(function(e){return new Array(20-e.length).join("0")+e})).reverse()}));for(n&&(a=s-Math.min(i,o)),s-=1;s>=a;){if(c[0][s]>c[1][s])return 1;if(c[0][s]===c[1][s]){if(s===a)return 0;s-=1}else if(c[0][s]<c[1][s])return-1}},e.map=function(e,t){var r,n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n},e.find=function(e,t){var r,n;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(r=0,n=e.length;r<n;r+=1){var i=e[r];if(t(i,r))return i}},e.assign=function(e){for(var t,r,n=e,i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];if(Object.assign)return Object.assign.apply(Object,[e].concat(o));var a=function(){var e=o[t];"object"==typeof e&&null!==e&&Object.keys(e).forEach((function(t){n[t]=e[t]}))};for(t=0,r=o.length;t<r;t+=1)a();return e},e.getBrowserAlias=function(e){return n.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return n.BROWSER_MAP[e]||""},e}();t.default=i,e.exports=t.default},18:function(e,t,r){t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(91))&&n.__esModule?n:{default:n},o=r(18);function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var a=function(){function e(){}var t,r,n;return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw new Error("UserAgent should be a string");return new i.default(e,t)},e.parse=function(e){return new i.default(e).getResult()},t=e,n=[{key:"BROWSER_MAP",get:function(){return o.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return o.ENGINE_MAP}},{key:"OS_MAP",get:function(){return o.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return o.PLATFORMS_MAP}}],(r=null)&&s(t.prototype,r),n&&s(t,n),e}();t.default=a,e.exports=t.default},91:function(e,t,r){t.__esModule=!0,t.default=void 0;var n=c(r(92)),i=c(r(93)),o=c(r(94)),s=c(r(95)),a=c(r(17));function c(e){return e&&e.__esModule?e:{default:e}}var u=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=a.default.find(n.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=a.default.find(i.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=a.default.find(o.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=a.default.find(s.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return a.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,r={},n=0,i={},o=0;if(Object.keys(e).forEach((function(t){var s=e[t];"string"==typeof s?(i[t]=s,o+=1):"object"==typeof s&&(r[t]=s,n+=1)})),n>0){var s=Object.keys(r),c=a.default.find(s,(function(e){return t.isOS(e)}));if(c){var u=this.satisfies(r[c]);if(void 0!==u)return u}var l=a.default.find(s,(function(e){return t.isPlatform(e)}));if(l){var d=this.satisfies(r[l]);if(void 0!==d)return d}}if(o>0){var p=Object.keys(i),f=a.default.find(p,(function(e){return t.isBrowser(e,!0)}));if(void 0!==f)return this.compareVersion(i[f])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var r=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),i=a.default.getBrowserTypeByAlias(n);return t&&i&&(n=i.toLowerCase()),n===r},t.compareVersion=function(e){var t=[0],r=e,n=!1,i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(r=e.substr(1),"="===e[1]?(n=!0,r=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?r=e.substr(1):"~"===e[0]&&(n=!0,r=e.substr(1)),t.indexOf(a.default.compareVersions(i,r,n))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some((function(e){return t.is(e)}))},e}();t.default=u,e.exports=t.default},92:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=/version\/(\d+(\.?_?\d+)+)/i,s=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},r=i.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},r=i.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},r=i.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},r=i.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},r=i.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},r=i.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},r=i.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},r=i.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=i.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},r=i.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},r=i.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},r=i.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},r=i.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},r=i.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},r=i.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},r=i.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},r=i.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},r=i.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},r=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},r=i.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},r=i.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},r=i.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},r=i.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},r=i.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},r=i.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},r=i.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},r=i.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},r=i.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t={name:"Android Browser"},r=i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},r=i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},r=i.default.getFirstMatch(o,e);return r&&(t.version=r),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:i.default.getFirstMatch(t,e),version:i.default.getSecondMatch(t,e)}}}];t.default=s,e.exports=t.default},93:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),s=[{test:[/Roku\/DVP/],describe:function(e){var t=i.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:o.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=i.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=i.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=i.default.getWindowsVersionName(t);return{name:o.OS_MAP.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:o.OS_MAP.iOS},r=i.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe:function(e){var t=i.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=i.default.getMacOSVersionName(t),n={name:o.OS_MAP.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=i.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:o.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t=i.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=i.default.getAndroidVersionName(t),n={name:o.OS_MAP.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=i.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:o.OS_MAP.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=i.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||i.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||i.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:o.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=i.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=i.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:o.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:o.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=i.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.PlayStation4,version:t}}}];t.default=s,e.exports=t.default},94:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),s=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=i.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:o.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe:function(e){var t=i.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:o.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}}];t.default=s,e.exports=t.default},95:function(e,t,r){t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},o=r(18),s=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:o.ENGINE_MAP.Blink};var t=i.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:o.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:o.ENGINE_MAP.Trident},r=i.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:o.ENGINE_MAP.Presto},r=i.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe:function(e){var t={name:o.ENGINE_MAP.Gecko},r=i.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:o.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:o.ENGINE_MAP.WebKit},r=i.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];t.default=s,e.exports=t.default}})}(ju);var Bu,Vu,Uu,zu,Gu,Wu,Yu=r(ju.exports),qu="1.2.3",Hu=["overseas-webrtc.liveplay.myqcloud.com","oswebrtc-lint.liveplay.myqcloud.com"],Ju="TX_LIVE_PLAYER_SIGNAL_DATA",Qu=["webrtc-signal-scheduler.tlivesource.com","bak-webrtc-signal-scheduler.tlivesource.com"],Ku="max-width:100%;max-height:100%;object-fit:contain;display:block;margin:0 auto;";!function(e){e[e.DISCONNECTED=0]="DISCONNECTED",e[e.CONNECTING=1]="CONNECTING",e[e.ESTABLISHED=2]="ESTABLISHED"}(Bu||(Bu={})),function(e){e[e.FAILURE=-1]="FAILURE",e[e.SUCCESS=0]="SUCCESS"}(Vu||(Vu={})),function(e){e[e.NEED_RECONNECT=-1]="NEED_RECONNECT",e[e.MANUAL_CLOSE=0]="MANUAL_CLOSE"}(Uu||(Uu={})),function(e){e[e.SEND=0]="SEND",e[e.RECEIVE=1]="RECEIVE"}(zu||(zu={})),function(e){e[e.INIT=0]="INIT",e[e.PLAYING=1]="PLAYING",e[e.WAITING=2]="WAITING"}(Gu||(Gu={})),function(e){e[e.PLAY_EVT_STREAM_BEGIN=1001]="PLAY_EVT_STREAM_BEGIN",e[e.PLAY_EVT_SERVER_CONNECTED=1002]="PLAY_EVT_SERVER_CONNECTED",e[e.PLAY_EVT_PLAY_BEGIN=1003]="PLAY_EVT_PLAY_BEGIN",e[e.PLAY_EVT_PLAY_STOP=1004]="PLAY_EVT_PLAY_STOP",e[e.PLAY_EVT_SERVER_RECONNECT=1005]="PLAY_EVT_SERVER_RECONNECT",e[e.PLAY_EVT_STREAM_EMPTY=1006]="PLAY_EVT_STREAM_EMPTY",e[e.PLAY_EVT_REQUEST_PULL_BEGIN=1007]="PLAY_EVT_REQUEST_PULL_BEGIN",e[e.PLAY_EVT_REQUEST_PULL_SUCCESS=1008]="PLAY_EVT_REQUEST_PULL_SUCCESS",e[e.PLAY_EVT_PLAY_WAITING_BEGIN=1009]="PLAY_EVT_PLAY_WAITING_BEGIN",e[e.PLAY_EVT_PLAY_WAITING_STOP=1010]="PLAY_EVT_PLAY_WAITING_STOP",e[e.PLAY_ERR_WEBRTC_FAIL=-2001]="PLAY_ERR_WEBRTC_FAIL",e[e.PLAY_ERR_REQUEST_PULL_FAIL=-2002]="PLAY_ERR_REQUEST_PULL_FAIL",e[e.PLAY_ERR_PLAY_FAIL=-2003]="PLAY_ERR_PLAY_FAIL",e[e.PLAY_ERR_SERVER_DISCONNECT=-2004]="PLAY_ERR_SERVER_DISCONNECT",e[e.PLAY_ERR_DECODE_FAIL=-2005]="PLAY_ERR_DECODE_FAIL",e[e.PLAY_ERR_REQUEST_ABR_FAIL=-2006]="PLAY_ERR_REQUEST_ABR_FAIL"}(Wu||(Wu={}));var Zu="\t\n\v\f\r 聽釟€鈥€鈥佲€傗€冣€勨€呪€嗏€団€堚€夆€娾€仧銆€\u2028\u2029\ufeff",Xu=z,$u=En,el=l("".replace),tl="[\t\n\v\f\r 聽釟€鈥€鈥佲€傗€冣€勨€呪€嗏€団€堚€夆€娾€仧銆€\u2028\u2029\ufeff]",rl=RegExp("^"+tl+tl+"*"),nl=RegExp(tl+tl+"*$"),il=function(e){return function(t){var r=$u(Xu(t));return 1&e&&(r=el(r,rl,"")),2&e&&(r=el(r,nl,"")),r}},ol={start:il(1),end:il(2),trim:il(3)},sl=f,al=n,cl=l,ul=En,ll=ol.trim,dl=Zu,pl=sl.parseInt,fl=sl.Symbol,hl=fl&&fl.iterator,vl=/^[+-]?0x/i,ml=cl(vl.exec),yl=8!==pl(dl+"08")||22!==pl(dl+"0x16")||hl&&!al((function(){pl(Object(hl))}))?function(e,t){var r=ll(ul(e));return pl(r,t>>>0||(ml(vl,r)?16:10))}:pl;br({global:!0,forced:parseInt!=yl},{parseInt:yl});var gl=J.parseInt,bl=br,Sl=on.indexOf,Tl=oa,_l=l([].indexOf),Cl=!!_l&&1/_l([1],1,-0)<0,wl=Tl("indexOf");bl({target:"Array",proto:!0,forced:Cl||!wl},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return Cl?_l(this,e,t)||0:Sl(this,e,t)}});var El,Rl,Pl,Al=Lr("Array").indexOf,Ml=d,kl=Al,Ol=Array.prototype,Dl=function(e){var t=e.indexOf;return e===Ol||Ml(Ol,e)&&t===Ol.indexOf?kl:t},Il=/tbs\/(\d+) /i,xl=/OS (\d+)_(\d+)_?(\d+)?/,Ll=Il.test(navigator.userAgent),Nl=/firefox\/(\d+)\./i.test(navigator.userAgent),Fl=Nl,jl=/UCBrowser\/(\d+)\./i.test(navigator.userAgent),Bl=/safari\/(\d+)\./i.test(navigator.userAgent)&&!/chrome\/(\d+)\./i.test(navigator.userAgent),Vl=/iPhone|iPad|iOS/i.test(navigator.userAgent),Ul=Vl,zl=function(){var e=navigator.userAgent.match(xl);return e&&[gl(e[1],10),gl(e[2],10),gl(e[3]||"0",10)]||[]},Gl=function(e,t,r){var n=e.match(t);return n&&n.length>=r&&gl(n[r],10)},Wl=function(e,t){var r,n,i=/^(?:webrtc:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)(?:[^?#]*)(?:\?*)([^?#]*)/.exec(e);if(i){var o=i[1].split("&");try{for(var s=sc(o),a=s.next();!a.done;a=s.next()){var c=ac(a.value.split("="),2),u=c[0],l=c[1];if(u===t)return l}}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}}return null},Yl=function(e,t,r){var n,i,o=/^(?:webrtc:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)(?:[^?#]*)(?:\?*)([^?#]*)/.exec(e);if(o){var s=o[1],a=[],c=t+"="+r;try{for(var u=sc(s.split("&")),l=u.next();!l.done;l=u.next()){var d=ac(l.value.split("="),2),p=d[0],f=d[1];p===t?null!==r&&a.push(c):a.push(p+"="+f)}}catch(e){n={error:e}}finally{try{l&&!l.done&&(i=u.return)&&i.call(u)}finally{if(n)throw n.error}}return null===r||Vn(a).call(a,c)||a.push(c),""+e.replace(s,"")+a.join("&")}return e},ql={},Hl=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],Jl=Hl[0];try{for(var Ql=sc(Hl),Kl=Ql.next();!Kl.done;Kl=Ql.next()){var Zl=Kl.value;if(Zl[1]in document){Pl=Zl;break}}}catch(e){El={error:e}}finally{try{Kl&&!Kl.done&&(Rl=Ql.return)&&Rl.call(Ql)}finally{if(El)throw El.error}}if(Pl)for(var Xl=0;Xl<Pl.length;Xl++)ql[Jl[Xl]]=Pl[Xl];var $l=function(e){if("function"==typeof e.webkitEnterFullScreen){var t=navigator.userAgent||"";if(/Android/.test(t)||!/Chrome|Mac OS X 10.5/.test(t))return!0}return!1},ed=na.map;br({target:"Array",proto:!0,forced:!Qa("map")},{map:function(e){return ed(this,e,arguments.length>1?arguments[1]:void 0)}});var td=Lr("Array").map,rd=d,nd=td,id=Array.prototype,od=function(e){var t=e.map;return e===id||rd(id,e)&&t===id.map?nd:t},sd=ft,ad=zt,cd=O,ud=function(e,t,r){var n=sd(t);n in e?ad.f(e,n,cd(0,r)):e[n]=r},ld=br,dd=Ms,pd=Gs,fd=H,hd=Jr,vd=$r,md=Y,yd=ud,gd=nt,bd=Sr,Sd=Qa("slice"),Td=gd("species"),_d=Array,Cd=Math.max;ld({target:"Array",proto:!0,forced:!Sd},{slice:function(e,t){var r,n,i,o=md(this),s=vd(o),a=hd(e,s),c=hd(void 0===t?s:t,s);if(dd(o)&&(r=o.constructor,(pd(r)&&(r===_d||dd(r.prototype))||fd(r)&&null===(r=r[Td]))&&(r=void 0),r===_d||void 0===r))return bd(o,a,c);for(n=new(void 0===r?_d:r)(Cd(c-a,0)),i=0;a<c;a++,i++)a in o&&yd(n,i,o[a]);return n.length=i,n}});var wd=Lr("Array").slice,Ed=d,Rd=wd,Pd=Array.prototype,Ad=function(e){var t=e.slice;return e===Pd||Ed(Pd,e)&&t===Pd.slice?Rd:t},Md=function(e){var t,r=e.split("\r\n"),n=[];va(r).call(r,(function(e){var t=e.toLowerCase();Vn(t).call(t,"a=rtpmap")&&Vn(t).call(t,"h264")&&n.push(e)}));var i=tc(t=od(n).call(n,(function(e){var t=/a=rtpmap:(\d+)\s/.exec(e);return t&&t.length>1?t[1]:null}))).call(t,(function(e){return null!==e})),o=[];return va(r).call(r,(function(e){var t=e;if(Vn(e).call(e,"a=fmtp:111")&&(t=e+";stereo=1"),Vn(e).call(e,"a=fmtp")){var r=/a=fmtp:(\d+)\s/.exec(e);r&&r.length>1&&Vn(i).call(i,r[1])&&(t=e+";sps-pps-idr-in-keyframe=1")}o.push(t)})),function(e){var t;if(!Fl)return e;var r=e.split("\r\n"),n=[],i=[];va(r).call(r,(function(e){var t=e.toLowerCase();Vn(t).call(t,"a=rtpmap")&&Vn(t).call(t,"h264")&&n.push(e)})),n.length>1&&i.push.apply(i,cc([],ac(Ad(n).call(n,1))));var o=tc(t=od(i).call(i,(function(e){var t=/a=rtpmap:(\d+)\s/.exec(e);return t&&t.length>1?t[1]:null}))).call(t,(function(e){return null!==e})),s=[];return va(r).call(r,(function(e){var t,r=e;if(Vn(e).call(e,"a=setup")&&(r="a=setup:passive"),(Vn(e).call(e,"m=audio")||Vn(e).call(e,"m=video"))&&(r=tc(t=e.split(" ")).call(t,(function(e,t){return t<3||!Vn(o).call(o,e)})).join(" ")),Vn(e).call(e,"a=fmtp")||Vn(e).call(e,"a=rtcp-fb")||Vn(e).call(e,"a=rtpmap")){var n=/a=(?:fmtp|rtcp-fb|rtpmap):(\d+)\s/.exec(e);if(n&&n.length>1&&Vn(o).call(o,n[1]))return}s.push(r)})),s.join("\r\n")}(o.join("\r\n"))},kd=function(){function e(){this.consoleEnabled=!1}return e.prototype.log=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.consoleEnabled&&console.log.apply(console,cc([],ac(e)))},e.prototype.enableConsole=function(e){this.consoleEnabled=e},e}(),Od=new kd,Dd=function(){function e(){var e,t,r,n,i,o;this.peerConnection=null,this.clientSideDescription=null,this.serverSideDescription=null,this.connectStatus=Bu.DISCONNECTED,this.connectDirection=zu.RECEIVE,this.negotiating=!1,this.onAddTrack=null,this.onConnect=null,this.onDisconnect=null,this.onSetLocalDescription=null,this.onError=null,this.onTrack=Vr(e=this.onTrack).call(e,this),this.onIceConnectionStateChange=Vr(t=this.onIceConnectionStateChange).call(t,this),this.onIceCandidate=Vr(r=this.onIceCandidate).call(r,this),this.onSignalingStateChange=Vr(n=this.onSignalingStateChange).call(n,this),this.onConnectionStateChange=Vr(i=this.onConnectionStateChange).call(i,this),this.onNegotiationNeeded=Vr(o=this.onNegotiationNeeded).call(o,this)}return e.prototype.init=function(e){var t=void 0===e?{}:e,r=t.onAddTrack,n=void 0===r?null:r,i=t.onConnect,o=void 0===i?null:i,s=t.onDisconnect,a=void 0===s?null:s,c=t.onSetLocalDescription,u=void 0===c?null:c,l=t.onError,d=void 0===l?null:l;this.onAddTrack=n,this.onConnect=o,this.onDisconnect=a,this.onSetLocalDescription=u,this.onError=d},e.prototype.initWebRTCConnect=function(e){var t=void 0===e?{}:e,r=t.config,n=t.direction,i=void 0===n?zu.RECEIVE:n,o=t.stream;return ic(this,void 0,void 0,(function(){return oc(this,(function(e){switch(e.label){case 0:return Od.log("CloudWebRTC init"),this.peerConnection?(Od.log("Call disconnected before connect"),[2]):(this.connectDirection=i,[4,this.createWebRTCConnect(r,o)]);case 1:return e.sent(),[2]}}))}))},e.prototype.connect=function(e){Od.log("connection connect->",e),e&&this.onAnswer(e)},e.prototype.disconnect=function(e){var t,r=void 0===e?{}:e,n=r.msg,i=void 0===n?"":n,o=r.code,s=void 0===o?Uu.MANUAL_CLOSE:o;Od.log("connection disconnect->",s,i),this.connectStatus!==Bu.DISCONNECTED&&(this.connectStatus=Bu.DISCONNECTED,this.negotiating=!1,this.peerConnection&&(this.peerConnection.removeEventListener("track",this.onTrack),this.peerConnection.removeEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.peerConnection.removeEventListener("icecandidate",this.onIceCandidate),this.peerConnection.removeEventListener("signalingstatechange",this.onSignalingStateChange),this.peerConnection.removeEventListener("connectionstatechange",this.onConnectionStateChange),this.peerConnection.removeEventListener("negotiationneeded",this.onNegotiationNeeded),this.peerConnection.close(),this.peerConnection=null),this.clientSideDescription=null,this.serverSideDescription=null,s===Uu.NEED_RECONNECT&&Od.log("Please try to reconnect"),null===(t=this.onDisconnect)||void 0===t||t.call(this,{code:s,msg:i}))},e.prototype.getClientSideDescription=function(){return this.clientSideDescription||Od.log("webrtc is not initialized"),this.clientSideDescription},e.prototype.getConnectStatus=function(){return this.connectStatus},e.prototype.getStats=function(){return ic(this,void 0,void 0,(function(){return oc(this,(function(e){switch(e.label){case 0:return this.peerConnection?[4,this.peerConnection.getStats(null)]:[2];case 1:return[2,e.sent()]}}))}))},e.prototype.createWebRTCConnect=function(e,t){var r,n;return ic(this,void 0,void 0,(function(){var i,o=this;return oc(this,(function(s){switch(s.label){case 0:try{i=rc({iceServers:[],sdpSemantics:"unified-plan",bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",tcpCandidatePolicy:"disable",IceTransportsType:"nohost"},null!==(r=e.connection)&&void 0!==r?r:{}),this.peerConnection=new RTCPeerConnection(i)}catch(e){var a;return Od.log(e),"ReferenceError"===e.name&&Vn(a=e.message).call(a,"RTCPeerConnection")&&(Od.log("Not support WebRTC"),null===(n=this.onError)||void 0===n||n.call(this,"WebRTC is not supported")),[2]}var c;if(this.connectStatus=Bu.CONNECTING,this.negotiating=!1,this.peerConnection.addEventListener("track",this.onTrack),this.peerConnection.addEventListener("iceconnectionstatechange",this.onIceConnectionStateChange),this.peerConnection.addEventListener("icecandidate",this.onIceCandidate),this.peerConnection.addEventListener("signalingstatechange",this.onSignalingStateChange),this.peerConnection.addEventListener("connectionstatechange",this.onConnectionStateChange),this.peerConnection.addEventListener("negotiationneeded",this.onNegotiationNeeded),this.connectDirection===zu.SEND&&t instanceof MediaStream)va(c=t.getTracks()).call(c,(function(e){o.peerConnection.addTrack(e,t)}));return this.connectDirection!==zu.RECEIVE?[3,2]:[4,this.createOffer(e.offer)];case 1:s.sent(),s.label=2;case 2:return[2]}}))}))},e.prototype.onTrack=function(e){var t;Od.log("onTrack",e),Od.log("on "+e.track.kind+" track",e.streams,e.track),null===(t=this.onAddTrack)||void 0===t||t.call(this,e.track)},e.prototype.onIceConnectionStateChange=function(e){switch(Od.log("onIceConnectionStateChange",this.peerConnection.iceConnectionState,e),this.peerConnection.iceConnectionState){case"failed":case"disconnected":Od.log("Connection disconnected, please try again"),this.disconnect({code:Uu.NEED_RECONNECT,msg:"Connection disconnected, please try again"})}},e.prototype.onIceCandidate=function(e){Od.log("onIceCandidate",e)},e.prototype.onSignalingStateChange=function(e){Od.log("onSignalingStateChange",this.peerConnection.signalingState,e)},e.prototype.onConnectionStateChange=function(e){var t;switch(Od.log("onConnectionStateChange",this.peerConnection.connectionState,e),this.peerConnection.connectionState){case"failed":case"disconnected":Od.log("Connection disconnected, please try again"),this.disconnect({code:Uu.NEED_RECONNECT,msg:"Connection disconnected, please try again"});break;case"connected":Od.log("Connection connected"),this.connectStatus=Bu.ESTABLISHED,null===(t=this.onConnect)||void 0===t||t.call(this,{code:Vu.SUCCESS,msg:"Connection connected"})}},e.prototype.onNegotiationNeeded=function(e){if(Od.log("onNegotiationNeeded",e),this.connectDirection!==zu.RECEIVE)try{if(this.negotiating||"stable"!==this.peerConnection.signalingState)return;this.negotiating=!0,this.createOffer()}catch(e){Od.log("onNegotiationNeeded error",e)}finally{this.negotiating=!1}},e.prototype.createOffer=function(e){var t;return void 0===e&&(e={}),ic(this,void 0,void 0,(function(){var r,n,i,o,s,a,c;return oc(this,(function(u){switch(u.label){case 0:r=e.offerToReceiveVideo,n=e.offerToReceiveAudio,i=nc(e,["offerToReceiveVideo","offerToReceiveAudio"]),o=rc({voiceActivityDetection:!1},i),this.connectDirection===zu.RECEIVE&&(s=!!this.peerConnection.addTransceiver,Od.log("peerConnection "+(s?"":"no")+" addTransceiver"),s?(n&&this.peerConnection.addTransceiver("audio",{direction:"recvonly"}),r&&this.peerConnection.addTransceiver("video",{direction:"recvonly"})):o=rc(rc({},o),{offerToReceiveVideo:r,offerToReceiveAudio:n})),u.label=1;case 1:return u.trys.push([1,4,,5]),a=this.onOffer,[4,this.peerConnection.createOffer(o)];case 2:return[4,a.apply(this,[u.sent()])];case 3:return u.sent(),[3,5];case 4:return c=u.sent(),Od.log("create offer error",c),this.disconnect({code:Uu.NEED_RECONNECT,msg:"create offer is failed"}),null===(t=this.onError)||void 0===t||t.call(this,"create offer is failed"),[3,5];case 5:return[2]}}))}))},e.prototype.onOffer=function(e){var t,r;return ic(this,void 0,void 0,(function(){var n,i;return oc(this,(function(o){switch(o.label){case 0:Od.log("onOffer->",e),n=e.sdp,n=this.connectDirection===zu.RECEIVE?Md(n):od(s=n.split("\r\n")).call(s,(function(e){return Vn(e).call(e,"a=fmtp:111")?e+";stereo=1":e})).join("\r\n"),e.sdp=n,Od.log("onOffer modify->",e),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.peerConnection.setLocalDescription(e)];case 2:return o.sent(),this.clientSideDescription=this.peerConnection.localDescription,null===(t=this.onSetLocalDescription)||void 0===t||t.call(this,this.clientSideDescription),[3,4];case 3:return i=o.sent(),Od.log("setLocalDescription error",i,this.clientSideDescription),null===(r=this.onError)||void 0===r||r.call(this,"set local sdp is failed"),[3,4];case 4:return[2]}var s}))}))},e.prototype.onAnswer=function(e){var t;return ic(this,void 0,void 0,(function(){var r;return oc(this,(function(n){switch(n.label){case 0:Od.log("onAnswer->",e),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.peerConnection.setRemoteDescription(new RTCSessionDescription(e))];case 2:return n.sent(),this.serverSideDescription=this.peerConnection.remoteDescription,Od.log("remoteDescription",this.peerConnection.remoteDescription),[3,4];case 3:return r=n.sent(),Od.log("setRemoteDescription error",r,e),null===(t=this.onError)||void 0===t||t.call(this,"set remote sdp is failed"),[3,4];case 4:return[2]}}))}))},e}(),Id={exports:{}},xd={},Ld=xi,Nd=Li.concat("length","prototype");xd.f=Object.getOwnPropertyNames||function(e){return Ld(e,Nd)};var Fd={},jd=Jr,Bd=$r,Vd=ud,Ud=Array,zd=Math.max,Gd=L,Wd=Y,Yd=xd.f,qd=function(e,t,r){for(var n=Bd(e),i=jd(t,n),o=jd(void 0===r?n:r,n),s=Ud(zd(o-i,0)),a=0;i<o;i++,a++)Vd(s,a,e[i]);return s.length=a,s},Hd="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];Fd.f=function(e){return Hd&&"Window"==Gd(e)?function(e){try{return Yd(e)}catch(e){return qd(Hd)}}(e):Yd(Wd(e))};var Jd=n((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}})),Qd=n,Kd=H,Zd=L,Xd=Jd,$d=Object.isExtensible,ep=Qd((function(){$d(1)}))||Xd?function(e){return!!Kd(e)&&((!Xd||"ArrayBuffer"!=Zd(e))&&(!$d||$d(e)))}:$d,tp=!n((function(){return Object.isExtensible(Object.preventExtensions({}))})),rp=br,np=l,ip=ni,op=H,sp=Ue,ap=zt.f,cp=xd,up=Fd,lp=ep,dp=tp,pp=!1,fp=qe("meta"),hp=0,vp=function(e){ap(e,fp,{value:{objectID:"O"+hp++,weakData:{}}})},mp=Id.exports={enable:function(){mp.enable=function(){},pp=!0;var e=cp.f,t=np([].splice),r={};r[fp]=1,e(r).length&&(cp.f=function(r){for(var n=e(r),i=0,o=n.length;i<o;i++)if(n[i]===fp){t(n,i,1);break}return n},rp({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:up.f}))},fastKey:function(e,t){if(!op(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!sp(e,fp)){if(!lp(e))return"F";if(!t)return"E";vp(e)}return e[fp].objectID},getWeakData:function(e,t){if(!sp(e,fp)){if(!lp(e))return!0;if(!t)return!1;vp(e)}return e[fp].weakData},onFreeze:function(e){return dp&&pp&&lp(e)&&!sp(e,fp)&&vp(e),e}};ip[fp]=!0;var yp=Un,gp=nt("iterator"),bp=Array.prototype,Sp=_n,Tp=we,_p=Un,Cp=nt("iterator"),wp=function(e){if(null!=e)return Tp(e,Cp)||Tp(e,"@@iterator")||_p[Sp(e)]},Ep=w,Rp=_e,Pp=Ht,Ap=ge,Mp=wp,kp=TypeError,Op=function(e,t){var r=arguments.length<2?Mp(e):t;if(Rp(r))return Pp(Ep(r,e));throw kp(Ap(e)+" is not iterable")},Dp=w,Ip=Ht,xp=we,Lp=Ut,Np=w,Fp=Ht,jp=ge,Bp=function(e){return void 0!==e&&(yp.Array===e||bp[gp]===e)},Vp=$r,Up=d,zp=Op,Gp=wp,Wp=function(e,t,r){var n,i;Ip(e);try{if(!(n=xp(e,"return"))){if("throw"===t)throw r;return r}n=Dp(n,e)}catch(e){i=!0,n=e}if("throw"===t)throw r;if(i)throw n;return Ip(n),r},Yp=TypeError,qp=function(e,t){this.stopped=e,this.result=t},Hp=qp.prototype,Jp=function(e,t,r){var n,i,o,s,a,c,u,l=r&&r.that,d=!(!r||!r.AS_ENTRIES),p=!(!r||!r.IS_ITERATOR),f=!(!r||!r.INTERRUPTED),h=Lp(t,l),v=function(e){return n&&Wp(n,"normal",e),new qp(!0,e)},m=function(e){return d?(Fp(e),f?h(e[0],e[1],v):h(e[0],e[1])):f?h(e,v):h(e)};if(p)n=e;else{if(!(i=Gp(e)))throw Yp(jp(e)+" is not iterable");if(Bp(i)){for(o=0,s=Vp(e);s>o;o++)if((a=m(e[o]))&&Up(Hp,a))return a;return new qp(!1)}n=zp(e,i)}for(c=n.next;!(u=Np(c,n)).done;){try{a=m(u.value)}catch(e){Wp(n,"throw",e)}if("object"==typeof a&&a&&Up(Hp,a))return a}return new qp(!1)},Qp=d,Kp=TypeError,Zp=function(e,t){if(Qp(t,e))return e;throw Kp("Incorrect invocation")},Xp=br,$p=f,ef=Id.exports,tf=n,rf=ar,nf=Jp,of=Zp,sf=b,af=H,cf=Fo,uf=zt.f,lf=na.forEach,df=T,pf=Ti.set,ff=Ti.getterFor,hf=bo,vf=$,mf=zt,yf=T,gf=nt("species"),bf=function(e){var t=vf(e),r=mf.f;yf&&t&&!t[gf]&&r(t,gf,{configurable:!0,get:function(){return this}})},Sf=zt.f,Tf=ao,_f=function(e,t,r){for(var n in t)r&&r.unsafe&&e[n]?e[n]=t[n]:hf(e,n,t[n],r);return e},Cf=Ut,wf=Zp,Ef=Jp,Rf=ds,Pf=bf,Af=T,Mf=Id.exports.fastKey,kf=Ti.set,Of=Ti.getterFor,Df={getConstructor:function(e,t,r,n){var i=e((function(e,i){wf(e,o),kf(e,{type:t,index:Tf(null),first:void 0,last:void 0,size:0}),Af||(e.size=0),null!=i&&Ef(i,e[n],{that:e,AS_ENTRIES:r})})),o=i.prototype,s=Of(t),a=function(e,t,r){var n,i,o=s(e),a=c(e,t);return a?a.value=r:(o.last=a={index:i=Mf(t,!0),key:t,value:r,previous:n=o.last,next:void 0,removed:!1},o.first||(o.first=a),n&&(n.next=a),Af?o.size++:e.size++,"F"!==i&&(o.index[i]=a)),e},c=function(e,t){var r,n=s(e),i=Mf(t);if("F"!==i)return n.index[i];for(r=n.first;r;r=r.next)if(r.key==t)return r};return _f(o,{clear:function(){for(var e=s(this),t=e.index,r=e.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete t[r.index],r=r.next;e.first=e.last=void 0,Af?e.size=0:this.size=0},delete:function(e){var t=this,r=s(t),n=c(t,e);if(n){var i=n.next,o=n.previous;delete r.index[n.index],n.removed=!0,o&&(o.next=i),i&&(i.previous=o),r.first==n&&(r.first=i),r.last==n&&(r.last=o),Af?r.size--:t.size--}return!!n},forEach:function(e){for(var t,r=s(this),n=Cf(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:r.first;)for(n(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!c(this,e)}}),_f(o,r?{get:function(e){var t=c(this,e);return t&&t.value},set:function(e,t){return a(this,0===e?0:e,t)}}:{add:function(e){return a(this,e=0===e?0:e,e)}}),Af&&Sf(o,"size",{get:function(){return s(this).size}}),i},setStrong:function(e,t,r){var n=t+" Iterator",i=Of(t),o=Of(n);Rf(e,t,(function(e,t){kf(this,{type:n,target:e,state:i(e),kind:t,last:void 0})}),(function(){for(var e=o(this),t=e.kind,r=e.last;r&&r.removed;)r=r.previous;return e.target&&(e.last=r=r?r.next:e.state.first)?"keys"==t?{value:r.key,done:!1}:"values"==t?{value:r.value,done:!1}:{value:[r.key,r.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),r?"entries":"values",!r,!0),Pf(t)}},If=function(e,t,r){var n,i=-1!==e.indexOf("Map"),o=-1!==e.indexOf("Weak"),s=i?"set":"add",a=$p[e],c=a&&a.prototype,u={};if(df&&sf(a)&&(o||c.forEach&&!tf((function(){(new a).entries().next()})))){var l=(n=t((function(t,r){pf(of(t,l),{type:e,collection:new a}),null!=r&&nf(r,t[s],{that:t,AS_ENTRIES:i})}))).prototype,d=ff(e);lf(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(e){var t="add"==e||"set"==e;!(e in c)||o&&"clear"==e||rf(l,e,(function(r,n){var i=d(this).collection;if(!t&&o&&!af(r))return"get"==e&&void 0;var s=i[e](0===r?0:r,n);return t?this:s}))})),o||uf(l,"size",{configurable:!0,get:function(){return d(this).collection.size}})}else n=r.getConstructor(t,e,i,s),ef.enable();return cf(n,e,!1,!0),u[e]=n,Xp({global:!0,forced:!0},u),o||r.setStrong(n,e,i),n};If("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),Df);var xf=l,Lf=Wr,Nf=En,Ff=z,jf=xf("".charAt),Bf=xf("".charCodeAt),Vf=xf("".slice),Uf=function(e){return function(t,r){var n,i,o=Nf(Ff(t)),s=Lf(r),a=o.length;return s<0||s>=a?e?"":void 0:(n=Bf(o,s))<55296||n>56319||s+1===a||(i=Bf(o,s+1))<56320||i>57343?e?jf(o,s):n:e?Vf(o,s,s+2):i-56320+(n-55296<<10)+65536}},zf={codeAt:Uf(!1),charAt:Uf(!0)}.charAt,Gf=En,Wf=Ti,Yf=ds,qf="String Iterator",Hf=Wf.set,Jf=Wf.getterFor(qf);Yf(String,"String",(function(e){Hf(this,{type:qf,string:Gf(e),index:0})}),(function(){var e,t=Jf(this),r=t.string,n=t.index;return n>=r.length?{value:void 0,done:!0}:(e=zf(r,n),t.index+=e.length,{value:e,done:!1})}));var Qf=J.Map,Kf=Gs,Zf=ge,Xf=TypeError,$f=function(e){if(Kf(e))return e;throw Xf(Zf(e)+" is not a constructor")},eh=Ut,th=w,rh=_e,nh=$f,ih=Jp,oh=[].push,sh=function(e){var t,r,n,i,o=arguments.length,s=o>1?arguments[1]:void 0;return nh(this),(t=void 0!==s)&&rh(s),null==e?new this:(r=[],t?(n=0,i=eh(s,o>2?arguments[2]:void 0),ih(e,(function(e){th(oh,r,i(e,n++))}))):ih(e,oh,{that:r}),new this(r))};br({target:"Map",stat:!0,forced:!0},{from:sh});var ah=Sr,ch=function(){return new this(ah(arguments))};br({target:"Map",stat:!0,forced:!0},{of:ch});var uh=w,lh=_e,dh=Ht,ph=function(){for(var e,t=dh(this),r=lh(t.delete),n=!0,i=0,o=arguments.length;i<o;i++)e=uh(r,t,arguments[i]),n=n&&e;return!!n};br({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:ph});var fh=w,hh=_e,vh=Ht,mh=function(e,t){var r=vh(this),n=hh(r.get),i=hh(r.has),o=hh(r.set),s=fh(i,r,e)&&"update"in t?t.update(fh(n,r,e),e,r):t.insert(e,r);return fh(o,r,e,s),s};br({target:"Map",proto:!0,real:!0,forced:!0},{emplace:mh});var yh=Op,gh=Ht,bh=Ut,Sh=yh,Th=Jp;br({target:"Map",proto:!0,real:!0,forced:!0},{every:function(e){var t=gh(this),r=Sh(t),n=bh(e,arguments.length>1?arguments[1]:void 0);return!Th(r,(function(e,r,i){if(!n(r,e,t))return i()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var _h=Ht,Ch=$f,wh=nt("species"),Eh=function(e,t){var r,n=_h(e).constructor;return void 0===n||null==(r=_h(n)[wh])?t:Ch(r)},Rh=$,Ph=Ut,Ah=w,Mh=_e,kh=Ht,Oh=Eh,Dh=yh,Ih=Jp;br({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(e){var t=kh(this),r=Dh(t),n=Ph(e,arguments.length>1?arguments[1]:void 0),i=new(Oh(t,Rh("Map"))),o=Mh(i.set);return Ih(r,(function(e,r){n(r,e,t)&&Ah(o,i,e,r)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),i}});var xh=Ht,Lh=Ut,Nh=yh,Fh=Jp;br({target:"Map",proto:!0,real:!0,forced:!0},{find:function(e){var t=xh(this),r=Nh(t),n=Lh(e,arguments.length>1?arguments[1]:void 0);return Fh(r,(function(e,r,i){if(n(r,e,t))return i(r)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var jh=Ht,Bh=Ut,Vh=yh,Uh=Jp;br({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(e){var t=jh(this),r=Vh(t),n=Bh(e,arguments.length>1?arguments[1]:void 0);return Uh(r,(function(e,r,i){if(n(r,e,t))return i(e)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var zh=br,Gh=w,Wh=_e,Yh=Op,qh=Jp,Hh=l([].push);zh({target:"Map",stat:!0,forced:!0},{groupBy:function(e,t){Wh(t);var r=Yh(e),n=new this,i=Wh(n.has),o=Wh(n.get),s=Wh(n.set);return qh(r,(function(e){var r=t(e);Gh(i,n,r)?Hh(Gh(o,n,r),e):Gh(s,n,r,[e])}),{IS_ITERATOR:!0}),n}});var Jh=Ht,Qh=yh,Kh=function(e,t){return e===t||e!=e&&t!=t},Zh=Jp;br({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(e){return Zh(Qh(Jh(this)),(function(t,r,n){if(Kh(r,e))return n()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var Xh=w,$h=Jp,ev=_e;br({target:"Map",stat:!0,forced:!0},{keyBy:function(e,t){var r=new this;ev(t);var n=ev(r.set);return $h(e,(function(e){Xh(n,r,t(e),e)})),r}});var tv=Ht,rv=yh,nv=Jp;br({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(e){return nv(rv(tv(this)),(function(t,r,n){if(r===e)return n(t)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var iv=$,ov=Ut,sv=w,av=_e,cv=Ht,uv=Eh,lv=yh,dv=Jp;br({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(e){var t=cv(this),r=lv(t),n=ov(e,arguments.length>1?arguments[1]:void 0),i=new(uv(t,iv("Map"))),o=av(i.set);return dv(r,(function(e,r){sv(o,i,n(r,e,t),r)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),i}});var pv=$,fv=Ut,hv=w,vv=_e,mv=Ht,yv=Eh,gv=yh,bv=Jp;br({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(e){var t=mv(this),r=gv(t),n=fv(e,arguments.length>1?arguments[1]:void 0),i=new(yv(t,pv("Map"))),o=vv(i.set);return bv(r,(function(e,r){hv(o,i,e,n(r,e,t))}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),i}});var Sv=_e,Tv=Ht,_v=Jp;br({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(e){for(var t=Tv(this),r=Sv(t.set),n=arguments.length,i=0;i<n;)_v(arguments[i++],r,{that:t,AS_ENTRIES:!0});return t}});var Cv=Ht,wv=_e,Ev=yh,Rv=Jp,Pv=TypeError;br({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=Cv(this),r=Ev(t),n=arguments.length<2,i=n?void 0:arguments[1];if(wv(e),Rv(r,(function(r,o){n?(n=!1,i=o):i=e(i,o,r,t)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),n)throw Pv("Reduce of empty map with no initial value");return i}});var Av=Ht,Mv=Ut,kv=yh,Ov=Jp;br({target:"Map",proto:!0,real:!0,forced:!0},{some:function(e){var t=Av(this),r=kv(t),n=Mv(e,arguments.length>1?arguments[1]:void 0);return Ov(r,(function(e,r,i){if(n(r,e,t))return i()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var Dv=w,Iv=Ht,xv=_e,Lv=TypeError;br({target:"Map",proto:!0,real:!0,forced:!0},{update:function(e,t){var r=Iv(this),n=xv(r.get),i=xv(r.has),o=xv(r.set),s=arguments.length;xv(t);var a=Dv(i,r,e);if(!a&&s<3)throw Lv("Updating absent value");var c=a?Dv(n,r,e):xv(s>2?arguments[2]:void 0)(e,r);return Dv(o,r,e,t(c,e,r)),r}});var Nv=w,Fv=_e,jv=b,Bv=Ht,Vv=TypeError,Uv=function(e,t){var r,n=Bv(this),i=Fv(n.get),o=Fv(n.has),s=Fv(n.set),a=arguments.length>2?arguments[2]:void 0;if(!jv(t)&&!jv(a))throw Vv("At least one callback required");return Nv(o,n,e)?(r=Nv(i,n,e),jv(t)&&(r=t(r),Nv(s,n,e,r))):jv(a)&&(r=a(),Nv(s,n,e,r)),r};br({target:"Map",proto:!0,real:!0,forced:!0},{upsert:Uv}),br({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:Uv});var zv=Qf,Gv=Pi.PROPER,Wv=n,Yv=Zu,qv=ol.trim;br({target:"String",proto:!0,forced:function(e){return Wv((function(){return!!Yv[e]()||"鈥嬄呩爭"!=="鈥嬄呩爭"[e]()||Gv&&Yv[e].name!==e}))}("trim")},{trim:function(){return qv(this)}});var Hv=Lr("String").trim,Jv=d,Qv=Hv,Kv=String.prototype,Zv=function(e){var t=e.trim;return"string"==typeof e||e===Kv||Jv(Kv,e)&&t===Kv.trim?Qv:t},Xv=function(){function e(){this.timeMark=null,this.commonData=null,this.reportData=null,this.tempData=null,this.onReport=null,this.timeMark=new zv,this.commonData={},this.initCommonData(),this.reportData=rc({},this.commonData),this.tempData={}}return e.prototype.setData=function(e,t,r){void 0===r&&(r=!1),this.reportData[e]=t,r&&(this.commonData[e]=t)},e.prototype.setTempData=function(e,t){this.tempData[e]=t},e.prototype.getTempData=function(e){return this.tempData[e]},e.prototype.startReport=function(e){var t;switch(e){case"start":this.setData("uint32_data_type",1),this.setData("uint32_command",40101);break;case"interval":this.setData("uint32_data_type",2),this.setData("uint32_command",40100);break;case"stop":this.setData("uint32_data_type",1),this.setData("uint32_command",40102)}var r=(new Date).getTime();this.setData("uint64_data_time",Math.round(r/1e3)),null===(t=this.onReport)||void 0===t||t.call(this,this.reportData),this.reportData=rc({},this.commonData)},e.prototype.setHandler=function(e){this.onReport=e},e.prototype.markTime=function(e){var t=window.performance?window.performance.now():(new Date).getTime();this.timeMark.set(e,t)},e.prototype.measureTime=function(e,t,r,n){if(void 0===n&&(n=!1),this.timeMark.has(t)&&this.timeMark.has(r)){var i=Math.round(this.timeMark.get(r)-this.timeMark.get(t));this.setData(e,i),n&&this.setTempData(e,i)}},e.prototype.clearData=function(){this.timeMark?this.timeMark.clear():this.timeMark=new zv,this.commonData={},this.initCommonData(),this.reportData=rc({},this.commonData),this.tempData={}},e.prototype.destroy=function(){var e;null===(e=this.timeMark)||void 0===e||e.clear(),this.timeMark=null,this.commonData=null,this.reportData=null,this.tempData=null,this.onReport=null},e.prototype.initCommonData=function(){var e;this.commonData.str_app_version="TXLivePlayer-1.2.3",this.commonData.bytes_version="TXLivePlayer-1.2.3";var t=window.navigator.userAgent,r=Yu.parse(t),n=r.platform,i=r.os,o=r.browser;this.commonData.str_device_type=n.model||n.vendor||"",this.commonData.str_os_info=Zv(e=(i.name||"")+" "+(i.version||"")).call(e),this.commonData.str_browser_model=o.name||"",this.commonData.str_browser_version=o.version||"",this.commonData.str_user_agent=t,this.commonData.u32_link_type=4,this.commonData.u32_channel_type=3},e}(),$v={};$v.f=Object.getOwnPropertySymbols;var em=$,tm=xd,rm=$v,nm=Ht,im=l([].concat),om=em("Reflect","ownKeys")||function(e){var t=tm.f(nm(e)),r=rm.f;return r?im(t,r(e)):t},sm=Ue,am=om,cm=S,um=zt,lm=Error,dm=l("".replace),pm=String(lm("zxcasd").stack),fm=/\n\s*at [^:]*:[^\n]*/,hm=fm.test(pm),vm=H,mm=ar,ym=En,gm=O,bm=!n((function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",gm(1,7)),7!==e.stack)})),Sm=br,Tm=d,_m=yo,Cm=Ko,wm=function(e,t,r){for(var n=am(t),i=um.f,o=cm.f,s=0;s<n.length;s++){var a=n[s];sm(e,a)||r&&sm(r,a)||i(e,a,o(t,a))}},Em=ao,Rm=ar,Pm=O,Am=function(e,t){if(hm&&"string"==typeof e&&!lm.prepareStackTrace)for(;t--;)e=dm(e,fm,"");return e},Mm=function(e,t){vm(t)&&"cause"in t&&mm(e,"cause",t.cause)},km=Jp,Om=function(e,t){return void 0===e?arguments.length<2?"":t:ym(e)},Dm=bm,Im=nt("toStringTag"),xm=Error,Lm=[].push,Nm=function(e,t){var r,n=arguments.length>2?arguments[2]:void 0,i=Tm(Fm,this);Cm?r=Cm(new xm,i?_m(this):Fm):(r=i?this:Em(Fm),Rm(r,Im,"Error")),void 0!==t&&Rm(r,"message",Om(t)),Dm&&Rm(r,"stack",Am(r.stack,1)),Mm(r,n);var o=[];return km(e,Lm,{that:o}),Rm(r,"errors",o),r};Cm?Cm(Nm,xm):wm(Nm,xm,{name:!0});var Fm=Nm.prototype=Em(xm.prototype,{constructor:Pm(1,Nm),message:Pm(1,""),name:Pm(1,"AggregateError")});Sm({global:!0,constructor:!0,arity:2},{AggregateError:Nm});var jm,Bm,Vm,Um,zm="process"==L(f.process),Gm=/(?:ipad|iphone|ipod).*applewebkit/i.test(ee),Wm=f,Ym=g,qm=Ut,Hm=b,Jm=Ue,Qm=n,Km=qi,Zm=Sr,Xm=yt,$m=ya,ey=Gm,ty=zm,ry=Wm.setImmediate,ny=Wm.clearImmediate,iy=Wm.process,oy=Wm.Dispatch,sy=Wm.Function,ay=Wm.MessageChannel,cy=Wm.String,uy=0,ly={},dy="onreadystatechange";try{jm=Wm.location}catch(e){}var py=function(e){if(Jm(ly,e)){var t=ly[e];delete ly[e],t()}},fy=function(e){return function(){py(e)}},hy=function(e){py(e.data)},vy=function(e){Wm.postMessage(cy(e),jm.protocol+"//"+jm.host)};ry&&ny||(ry=function(e){$m(arguments.length,1);var t=Hm(e)?e:sy(e),r=Zm(arguments,1);return ly[++uy]=function(){Ym(t,void 0,r)},Bm(uy),uy},ny=function(e){delete ly[e]},ty?Bm=function(e){iy.nextTick(fy(e))}:oy&&oy.now?Bm=function(e){oy.now(fy(e))}:ay&&!ey?(Um=(Vm=new ay).port2,Vm.port1.onmessage=hy,Bm=qm(Um.postMessage,Um)):Wm.addEventListener&&Hm(Wm.postMessage)&&!Wm.importScripts&&jm&&"file:"!==jm.protocol&&!Qm(vy)?(Bm=vy,Wm.addEventListener("message",hy,!1)):Bm=dy in Xm("script")?function(e){Km.appendChild(Xm("script")).onreadystatechange=function(){Km.removeChild(this),py(e)}}:function(e){setTimeout(fy(e),0)});var my,yy,gy,by,Sy,Ty,_y,Cy,wy={set:ry,clear:ny},Ey=f,Ry=/ipad|iphone|ipod/i.test(ee)&&void 0!==Ey.Pebble,Py=/web0s(?!.*chrome)/i.test(ee),Ay=f,My=Ut,ky=S.f,Oy=wy.set,Dy=Gm,Iy=Ry,xy=Py,Ly=zm,Ny=Ay.MutationObserver||Ay.WebKitMutationObserver,Fy=Ay.document,jy=Ay.process,By=Ay.Promise,Vy=ky(Ay,"queueMicrotask"),Uy=Vy&&Vy.value;Uy||(my=function(){var e,t;for(Ly&&(e=jy.domain)&&e.exit();yy;){t=yy.fn,yy=yy.next;try{t()}catch(e){throw yy?by():gy=void 0,e}}gy=void 0,e&&e.enter()},Dy||Ly||xy||!Ny||!Fy?!Iy&&By&&By.resolve?((_y=By.resolve(void 0)).constructor=By,Cy=My(_y.then,_y),by=function(){Cy(my)}):Ly?by=function(){jy.nextTick(my)}:(Oy=My(Oy,Ay),by=function(){Oy(my)}):(Sy=!0,Ty=Fy.createTextNode(""),new Ny(my).observe(Ty,{characterData:!0}),by=function(){Ty.data=Sy=!Sy}));var zy=Uy||function(e){var t={fn:e,next:void 0};gy&&(gy.next=t),yy||(yy=t,by()),gy=t},Gy=f,Wy=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}},Yy=function(){this.head=null,this.tail=null};Yy.prototype={add:function(e){var t={item:e,next:null};this.head?this.tail.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return this.head=e.next,this.tail===e&&(this.tail=null),e.item}};var qy=Yy,Hy=f.Promise,Jy="object"==typeof window&&"object"!=typeof Deno,Qy=f,Ky=Hy,Zy=b,Xy=Ft,$y=Jn,eg=nt,tg=Jy,rg=ae,ng=Ky&&Ky.prototype,ig=eg("species"),og=!1,sg=Zy(Qy.PromiseRejectionEvent),ag=Xy("Promise",(function(){var e=$y(Ky),t=e!==String(Ky);if(!t&&66===rg)return!0;if(!ng.catch||!ng.finally)return!0;if(rg>=51&&/native code/.test(e))return!1;var r=new Ky((function(e){e(1)})),n=function(e){e((function(){}),(function(){}))};return(r.constructor={})[ig]=n,!(og=r.then((function(){}))instanceof n)||!t&&tg&&!sg})),cg={CONSTRUCTOR:ag,REJECTION_EVENT:sg,SUBCLASSING:og},ug={},lg=_e,dg=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw TypeError("Bad Promise constructor");t=e,r=n})),this.resolve=lg(t),this.reject=lg(r)};ug.f=function(e){return new dg(e)};var pg,fg,hg=br,vg=zm,mg=f,yg=w,gg=bo,bg=Fo,Sg=bf,Tg=_e,_g=b,Cg=H,wg=Zp,Eg=Eh,Rg=wy.set,Pg=zy,Ag=function(e,t){var r=Gy.console;r&&r.error&&(1==arguments.length?r.error(e):r.error(e,t))},Mg=Wy,kg=qy,Og=Ti,Dg=Hy,Ig=ug,xg="Promise",Lg=cg.CONSTRUCTOR,Ng=cg.REJECTION_EVENT,Fg=Og.getterFor(xg),jg=Og.set,Bg=Dg&&Dg.prototype,Vg=Dg,Ug=Bg,zg=mg.TypeError,Gg=mg.document,Wg=mg.process,Yg=Ig.f,qg=Yg,Hg=!!(Gg&&Gg.createEvent&&mg.dispatchEvent),Jg="unhandledrejection",Qg=function(e){var t;return!(!Cg(e)||!_g(t=e.then))&&t},Kg=function(e,t){var r,n,i,o=t.value,s=1==t.state,a=s?e.ok:e.fail,c=e.resolve,u=e.reject,l=e.domain;try{a?(s||(2===t.rejection&&tb(t),t.rejection=1),!0===a?r=o:(l&&l.enter(),r=a(o),l&&(l.exit(),i=!0)),r===e.promise?u(zg("Promise-chain cycle")):(n=Qg(r))?yg(n,r,c,u):c(r)):u(o)}catch(e){l&&!i&&l.exit(),u(e)}},Zg=function(e,t){e.notified||(e.notified=!0,Pg((function(){for(var r,n=e.reactions;r=n.get();)Kg(r,e);e.notified=!1,t&&!e.rejection&&$g(e)})))},Xg=function(e,t,r){var n,i;Hg?((n=Gg.createEvent("Event")).promise=t,n.reason=r,n.initEvent(e,!1,!0),mg.dispatchEvent(n)):n={promise:t,reason:r},!Ng&&(i=mg["on"+e])?i(n):e===Jg&&Ag("Unhandled promise rejection",r)},$g=function(e){yg(Rg,mg,(function(){var t,r=e.facade,n=e.value;if(eb(e)&&(t=Mg((function(){vg?Wg.emit("unhandledRejection",n,r):Xg(Jg,r,n)})),e.rejection=vg||eb(e)?2:1,t.error))throw t.value}))},eb=function(e){return 1!==e.rejection&&!e.parent},tb=function(e){yg(Rg,mg,(function(){var t=e.facade;vg?Wg.emit("rejectionHandled",t):Xg("rejectionhandled",t,e.value)}))},rb=function(e,t,r){return function(n){e(t,n,r)}},nb=function(e,t,r){e.done||(e.done=!0,r&&(e=r),e.value=t,e.state=2,Zg(e,!0))},ib=function(e,t,r){if(!e.done){e.done=!0,r&&(e=r);try{if(e.facade===t)throw zg("Promise can't be resolved itself");var n=Qg(t);n?Pg((function(){var r={done:!1};try{yg(n,t,rb(ib,r,e),rb(nb,r,e))}catch(t){nb(r,t,e)}})):(e.value=t,e.state=1,Zg(e,!1))}catch(t){nb({done:!1},t,e)}}};Lg&&(Ug=(Vg=function(e){wg(this,Ug),Tg(e),yg(pg,this);var t=Fg(this);try{e(rb(ib,t),rb(nb,t))}catch(e){nb(t,e)}}).prototype,(pg=function(e){jg(this,{type:xg,done:!1,notified:!1,parent:!1,reactions:new kg,rejection:!1,state:0,value:void 0})}).prototype=gg(Ug,"then",(function(e,t){var r=Fg(this),n=Yg(Eg(this,Vg));return r.parent=!0,n.ok=!_g(e)||e,n.fail=_g(t)&&t,n.domain=vg?Wg.domain:void 0,0==r.state?r.reactions.add(n):Pg((function(){Kg(n,r)})),n.promise})),fg=function(){var e=new pg,t=Fg(e);this.promise=e,this.resolve=rb(ib,t),this.reject=rb(nb,t)},Ig.f=Yg=function(e){return e===Vg||undefined===e?new fg(e):qg(e)}),hg({global:!0,constructor:!0,wrap:!0,forced:Lg},{Promise:Vg}),bg(Vg,xg,!1,!0),Sg(xg);var ob=nt("iterator"),sb=!1;try{var ab=0,cb={next:function(){return{done:!!ab++}},return:function(){sb=!0}};cb[ob]=function(){return this},Array.from(cb,(function(){throw 2}))}catch(e){}var ub=Hy,lb=function(e,t){if(!t&&!sb)return!1;var r=!1;try{var n={};n[ob]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch(e){}return r},db=cg.CONSTRUCTOR||!lb((function(e){ub.all(e).then(void 0,(function(){}))})),pb=w,fb=_e,hb=ug,vb=Wy,mb=Jp;br({target:"Promise",stat:!0,forced:db},{all:function(e){var t=this,r=hb.f(t),n=r.resolve,i=r.reject,o=vb((function(){var r=fb(t.resolve),o=[],s=0,a=1;mb(e,(function(e){var c=s++,u=!1;a++,pb(r,t,e).then((function(e){u||(u=!0,o[c]=e,--a||n(o))}),i)})),--a||n(o)}));return o.error&&i(o.value),r.promise}});var yb=br,gb=cg.CONSTRUCTOR;Hy&&Hy.prototype,yb({target:"Promise",proto:!0,forced:gb,real:!0},{catch:function(e){return this.then(void 0,e)}});var bb=w,Sb=_e,Tb=ug,_b=Wy,Cb=Jp;br({target:"Promise",stat:!0,forced:db},{race:function(e){var t=this,r=Tb.f(t),n=r.reject,i=_b((function(){var i=Sb(t.resolve);Cb(e,(function(e){bb(i,t,e).then(r.resolve,n)}))}));return i.error&&n(i.value),r.promise}});var wb=w,Eb=ug;br({target:"Promise",stat:!0,forced:cg.CONSTRUCTOR},{reject:function(e){var t=Eb.f(this);return wb(t.reject,void 0,e),t.promise}});var Rb=Ht,Pb=H,Ab=ug,Mb=function(e,t){if(Rb(e),Pb(t)&&t.constructor===e)return t;var r=Ab.f(e);return(0,r.resolve)(t),r.promise},kb=br,Ob=Hy,Db=cg.CONSTRUCTOR,Ib=Mb,xb=$("Promise"),Lb=!Db;kb({target:"Promise",stat:!0,forced:true},{resolve:function(e){return Ib(Lb&&this===xb?Ob:this,e)}});var Nb=w,Fb=_e,jb=ug,Bb=Wy,Vb=Jp;br({target:"Promise",stat:!0},{allSettled:function(e){var t=this,r=jb.f(t),n=r.resolve,i=r.reject,o=Bb((function(){var r=Fb(t.resolve),i=[],o=0,s=1;Vb(e,(function(e){var a=o++,c=!1;s++,Nb(r,t,e).then((function(e){c||(c=!0,i[a]={status:"fulfilled",value:e},--s||n(i))}),(function(e){c||(c=!0,i[a]={status:"rejected",reason:e},--s||n(i))}))})),--s||n(i)}));return o.error&&i(o.value),r.promise}});var Ub=w,zb=_e,Gb=$,Wb=ug,Yb=Wy,qb=Jp,Hb="No one promise resolved";br({target:"Promise",stat:!0},{any:function(e){var t=this,r=Gb("AggregateError"),n=Wb.f(t),i=n.resolve,o=n.reject,s=Yb((function(){var n=zb(t.resolve),s=[],a=0,c=1,u=!1;qb(e,(function(e){var l=a++,d=!1;c++,Ub(n,t,e).then((function(e){d||u||(u=!0,i(e))}),(function(e){d||u||(d=!0,s[l]=e,--c||o(new r(s,Hb)))}))})),--c||o(new r(s,Hb))}));return s.error&&o(s.value),n.promise}});var Jb=br,Qb=Hy,Kb=n,Zb=$,Xb=b,$b=Eh,eS=Mb,tS=Qb&&Qb.prototype;Jb({target:"Promise",proto:!0,real:!0,forced:!!Qb&&Kb((function(){tS.finally.call({then:function(){}},(function(){}))}))},{finally:function(e){var t=$b(this,Zb("Promise")),r=Xb(e);return this.then(r?function(r){return eS(t,e()).then((function(){return r}))}:e,r?function(r){return eS(t,e()).then((function(){throw r}))}:e)}});var rS=J.Promise,nS=ug,iS=Wy;br({target:"Promise",stat:!0,forced:!0},{try:function(e){var t=nS.f(this),r=iS(e);return(r.error?t.reject:t.resolve)(r.value),t.promise}});var oS=rS,sS=br,aS=$,cS=g,uS=w,lS=l,dS=n,pS=Ms,fS=b,hS=H,vS=me,mS=Sr,yS=le,gS=aS("JSON","stringify"),bS=lS(/./.exec),SS=lS("".charAt),TS=lS("".charCodeAt),_S=lS("".replace),CS=lS(1..toString),wS=/[\uD800-\uDFFF]/g,ES=/^[\uD800-\uDBFF]$/,RS=/^[\uDC00-\uDFFF]$/,PS=!yS||dS((function(){var e=aS("Symbol")();return"[null]"!=gS([e])||"{}"!=gS({a:e})||"{}"!=gS(Object(e))})),AS=dS((function(){return'"\\udf06\\ud834"'!==gS("\udf06\ud834")||'"\\udead"'!==gS("\udead")})),MS=function(e,t){var r=mS(arguments),n=t;if((hS(t)||void 0!==e)&&!vS(e))return pS(t)||(t=function(e,t){if(fS(n)&&(t=uS(n,this,e,t)),!vS(t))return t}),r[1]=t,cS(gS,null,r)},kS=function(e,t,r){var n=SS(r,t-1),i=SS(r,t+1);return bS(ES,e)&&!bS(RS,i)||bS(RS,e)&&!bS(ES,n)?"\\u"+CS(TS(e,0),16):e};gS&&sS({target:"JSON",stat:!0,arity:3,forced:PS||AS},{stringify:function(e,t,r){var n=mS(arguments),i=cS(PS?MS:gS,null,n);return AS&&"string"==typeof i?_S(i,wS,kS):i}});var OS=J,DS=g;OS.JSON||(OS.JSON={stringify:JSON.stringify});var IS=function(e,t,r){return DS(OS.JSON.stringify,null,arguments)},xS=IS,LS=function(e,t){return fetch(e,{body:xS(t),cache:"no-cache",credentials:"same-origin",headers:{"content-type":"application/json"},method:"POST",mode:"cors"}).then((function(e){if(200!==e.status)throw new Error("status Code:"+e.status);return e.json()}))},NS=function(){function e(){this.baseUrl="",this.signalDomainConfig={protocol:"https",domain:"",query:!0}}return e.prototype.setSignalConfig=function(e){var t=e.protocol,r=void 0===t?"https":t,n=e.domain,i=void 0===n?"":n,o=e.query,s=void 0===o||o;this.signalDomainConfig.protocol=r,this.signalDomainConfig.domain=i,this.signalDomainConfig.query=s},e.prototype.fetchPullStream=function(e){var t;return ic(this,void 0,void 0,(function(){var r,n,i,o,s,a,c,u,l=this;return oc(this,(function(d){switch(d.label){case 0:return r=function(e,t){return ic(l,void 0,void 0,(function(){var r,n,i,o,s;return oc(this,(function(a){switch(a.label){case 0:return[4,LS(e+"/webrtc/v1/pullstream",t)];case 1:if(r=a.sent(),n=r.errcode,i=r.errmsg,o=r.remotesdp,s=r.svrsig,0!==n)throw new Error("errCode:"+n+", errMsg:"+i);return[2,{url:e,remoteSdp:o,svrSig:s}]}}))}))},this.baseUrl?[4,r(this.baseUrl,e)]:[3,2];case 1:return n=d.sent(),a=n.url,[2,c=nc(n,["url"])];case 2:i=this.signalDomainConfig.protocol,d.label=3;case 3:return d.trys.push([3,5,,6]),o=od(Hu).call(Hu,(function(t){return r(i+"://"+t,e)})),[4,oS.any(o)];case 4:return s=d.sent(),a=s.url,c=nc(s,["url"]),this.baseUrl=a,[2,c];case 5:throw u=d.sent(),(null===(t=null==u?void 0:u.errors)||void 0===t?void 0:t[0])||u;case 6:return[2]}}))}))},e.prototype.fetchStopStream=function(e){return ic(this,void 0,void 0,(function(){var t,r,n,i;return oc(this,(function(o){switch(o.label){case 0:if(!this.baseUrl)throw new Error("signal domain name is empty");return t=this.baseUrl+"/webrtc/v1/stopstream",[4,LS(t,e)];case 1:if(r=o.sent(),n=r.errcode,i=r.errmsg,0!==n)throw new Error("stop stream failed, errCode:"+n+", errmsg:"+i);return[2,r]}}))}))},e.prototype.fetchStreamSdp=function(e){return ic(this,void 0,void 0,(function(){var t,r,n,i,o,s,a;return oc(this,(function(c){switch(c.label){case 0:return t=e.streamurl,r=e.sessionid,n=e.localsdp,[4,LS(t,{version:"v1.0",sessionId:r,localSdp:n})];case 1:if(i=c.sent(),o=i.code,s=i.message,a=i.remoteSdp,200!==o)throw new Error("errCode:"+o+", errMsg:"+s);return[2,{remoteSdp:a,svrSig:null}]}}))}))},e.prototype.fetchAbrControl=function(e){return ic(this,void 0,void 0,(function(){var t,r,n,i;return oc(this,(function(o){switch(o.label){case 0:if(!this.baseUrl)throw new Error("signal domain name is empty");return t=this.baseUrl+"/webrtc/v1/tabrctl",[4,LS(t,e)];case 1:if(r=o.sent(),n=r.errcode,i=r.seq,0!==n)throw new Error("errCode:"+n+", errMsg:"+(403===n?"not allowed error":"error"));return[2,{seq:i}]}}))}))},e.prototype.updateSignalDomain=function(e){var t=this.signalDomainConfig,r=t.protocol,n=t.domain,i=t.query;if(e&&i){var o=window.localStorage.getItem(Ju);if(o){var s=JSON.parse(o)[e];if(s){var a=s.signal,c=s.expire;return(new Date).getTime()>c&&this.fetchSignalDomain(e,Qu[0]),void(this.baseUrl=r+"://"+a)}}this.baseUrl=n?r+"://"+n:"",this.fetchSignalDomain(e,Qu[0])}else this.baseUrl=n?r+"://"+n:""},e.prototype.fetchSignalDomain=function(e,t){return ic(this,void 0,void 0,(function(){var r,n,i,o,s,a,c,u,l;return oc(this,(function(d){switch(d.label){case 0:if(!e||!t)return[2];r="https://"+t+"/signal_query",d.label=1;case 1:return d.trys.push([1,3,,4]),[4,LS(r,{domain:e,requestid:Fu(16),client_type:"Web",client_info:window.navigator.userAgent})];case 2:if(n=d.sent(),i=n.errcode,o=n.data,0===i){s=o.signal_domain,a=o.cache_time,c={},(u=window.localStorage.getItem(Ju))&&(c=JSON.parse(u)),c[e]={signal:s,expire:(new Date).getTime()+1e3*a};try{window.localStorage.setItem(Ju,xS(c))}catch(e){}}return[3,4];case 3:return d.sent(),-1!==(l=Dl(Qu).call(Qu,t))&&this.fetchSignalDomain(e,Qu[l+1]),[3,4];case 4:return[2]}}))}))},e}(),FS=function(){function e(){var e,t,r,n,i,o,s,a;this.playerView=null,this.playUrl=null,this.rawUrl=null,this.isVideoExisted=!1,this.webrtcConnection=null,this.svrSig=null,this.stream=null,this.connectRetry={maxNum:3,curNum:0,delay:1,playing:!1},this.retryTimeoutId=null,this.statsIntervalId=null,this.disconnectTimeoutId=null,this.docOrigOverflow=null,this.listener={onPlayEvent:null,onPlayStats:null,onPlayReport:null},this.lastStatsReport=null,this.streamEmptyCount=0,this.streamDecodeFailCount=0,this.offerToReceive={video:!0,audio:!0},this.playingStatus=Gu.INIT,this.report=null,this.signal=null,this.abrClient={curBitrate:"",seq:0},this.report=new Xv,this.signal=new NS,this.onAddTrack=Vr(e=this.onAddTrack).call(e,this),this.onSetLocalDescription=Vr(t=this.onSetLocalDescription).call(t,this),this.onConnect=Vr(r=this.onConnect).call(r,this),this.onDisconnect=Vr(n=this.onDisconnect).call(n,this),this.onError=Vr(i=this.onError).call(i,this),this.onStats=Vr(o=this.onStats).call(o,this),this.onBeforeUnload=Vr(s=this.onBeforeUnload).call(s,this),this.onPagehide=Vr(a=this.onPagehide).call(a,this),window.addEventListener("beforeunload",this.onBeforeUnload),window.addEventListener("pagehide",this.onPagehide);var c=Nu.browserDetails,u=c.browser,l=c.version;Od.log("browser is "+u+", version is "+l)}return e.checkSupport=function(e){ic(void 0,void 0,void 0,(function(){var e,t,r;return oc(this,(function(n){var i;switch(n.label){case 0:return e=!1,va(i=["RTCPeerConnection","webkitRTCPeerConnection"]).call(i,(function(t){e||t in window&&(e=!0)})),Ll||(jl&&Vl||Bl&&Vl&&(0===(t=zl()).length||t[0]<11||11===t[0]&&t[1]<1||11===t[0]&&1===t[1]&&t[2]<2))&&(e=!1),[4,ic(void 0,void 0,void 0,(function(){var e,t,r,n;return oc(this,(function(i){var o;switch(i.label){case 0:return i.trys.push([0,2,,3]),e=new RTCPeerConnection({iceServers:[],sdpSemantics:"unified-plan"}),t={},e.addTransceiver?(e.addTransceiver("audio",{direction:"recvonly"}),e.addTransceiver("video",{direction:"recvonly"})):t={offerToReceiveVideo:!0,offerToReceiveAudio:!0},[4,e.createOffer(t)];case 1:return r=i.sent(),n=Dl(o=r.sdp.toLowerCase()).call(o,"h264")>-1,e.close(),[2,n];case 2:return i.sent(),[2,!1];case 3:return[2]}}))}))];case 1:return r=n.sent(),[2,{support:e,isTbs:Ll,tbsVersion:Ll?Gl(navigator.userAgent,Il,1):null,isFirefox:Nl,isSafari:Bl,isIOS:Vl,iOSVersion:Vl?zl().join("."):null,h264Support:r}]}}))})).then((function(t){null==e||e(t)}))},e.prototype.setConfig=function(e){var t=e.connectRetryCount,r=e.connectRetryDelay,n=e.receiveVideo,i=e.receiveAudio,o=e.showLog,s=e.signalDomain;if(void 0!==t&&("number"==typeof t&&t>=0&&t<=10?this.connectRetry.maxNum=t:Od.log("connectRetryCount must be a number between 0 and 10")),void 0!==r&&("number"==typeof r&&r>=0&&r<=10?this.connectRetry.delay=r:Od.log("connectRetryDelay must be a number between 0 and 10")),void 0!==n&&(this.offerToReceive.video=!!n),void 0!==i&&(this.offerToReceive.audio=!!i),void 0!==o&&Od.enableConsole(!!o),void 0!==s)if("string"==typeof s)this.signal.setSignalConfig({domain:s});else{var a,c=s.protocol,u=s.domain,l=s.query;this.signal.setSignalConfig({protocol:Vn(a=["https","http"]).call(a,c)?c:"https",domain:u,query:l})}},e.prototype.setPlayListener=function(e){var t=e.onPlayEvent,r=e.onPlayStats,n=e.onPlayReport;void 0!==t&&("function"==typeof t?this.listener.onPlayEvent=t:Od.log("onPlayEvent must be function")),void 0!==r&&("function"==typeof r?this.listener.onPlayStats=r:Od.log("onPlayStats must be function")),void 0!==n&&("function"==typeof n?(this.listener.onPlayReport=n,this.report.setHandler(n)):Od.log("onPlayReport must be function"))},e.prototype.setPlayerView=function(e){if(this.playerView)Od.log("player view is existed");else{var t="string"==typeof e?document.getElementById(e):e;if(t&&(t instanceof HTMLDivElement||t instanceof HTMLVideoElement))if(t instanceof HTMLVideoElement)this.playerView=t;else{var r=document.createElement("video");r.autoplay=!0,r.muted=!0,r.controls=!0,r.playsInline=!0,r.setAttribute("webkit-playsinline",""),r.setAttribute("x5-playsinline",""),r.setAttribute("style",Ku),t.appendChild(r),this.playerView=r}else Od.log("Require container element id or HTMLDivElement")}},e.prototype.startPlay=function(e){if(this.playerView){var t=function(e){var t=/^(?:webrtc:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)([^?#]*)(?:\?*)(?:[^?#]*)/.exec(e);if(t)return t[1];var r=/^(?:https?:\/\/)(?:[0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)([^?#]*)(?:\.sdp)(?:\?*)(?:[^?#]*)/.exec(e);return r?r[1]:null}(e);if(null!==t)if(this.isVideoExisted)Od.log("Video is existed, please stop playing first");else if(this.offerToReceive.video||this.offerToReceive.audio){var r=(new Date).getTime();this.report.clearData(),this.report.setData("u64_timestamp",r),this.report.setTempData("pull_start_time",r),this.report.markTime("pull_start"),this.report.setData("bytes_stream_id",t,!0),this.report.setData("str_stream_url",e,!0);var n,i=(n=/^(?:webrtc:\/\/)([0-9.\-A-Za-z_]+)(?:\/)(?:[0-9.\-A-Za-z_=]+)(?:\/)(?:[^?#]*)(?:\?*)(?:[^?#]*)/.exec(e))?n[1]:null;this.signal.updateSignalDomain(i),this.rawUrl=e,this.playUrl=function(e){var t=Wl(e,"tabr_bitrates"),r=Wl(e,"tabr_start_bitrate"),n=Wl(e,"tabr_control"),i=e;return t&&r&&(i=Yl(i,"tabr_control",null),i=Yl(i,"webrtc_tabr_level","auto"===n?"server_control":"client_control")),i}(e),this.streamEmptyCount=0,this.streamDecodeFailCount=0,this.playingStatus=Gu.INIT,this.abrClient.seq=0,this.webrtcConnection||(this.webrtcConnection=new Dd,this.webrtcConnection.init({onAddTrack:this.onAddTrack,onSetLocalDescription:this.onSetLocalDescription,onConnect:this.onConnect,onDisconnect:this.onDisconnect,onError:this.onError})),this.webrtcConnection.initWebRTCConnect({config:{connection:{},offer:{offerToReceiveVideo:this.offerToReceive.video,offerToReceiveAudio:this.offerToReceive.audio}}})}else Od.log("Both receiveVideo and receiveAudio are false");else Od.log("Play url is not correct")}else Od.log("Please set player view first")},e.prototype.stopPlay=function(e){var t,r,n;return void 0===e&&(e=!0),ic(this,void 0,void 0,(function(){var i;return oc(this,(function(o){switch(o.label){case 0:if(!this.isVideoExisted)return Od.log("Video is not existed"),[2];if(this.isVideoExisted=!1,this.disconnectTimeoutId&&(window.clearTimeout(this.disconnectTimeoutId),this.disconnectTimeoutId=null),this.retryTimeoutId&&(window.clearTimeout(this.retryTimeoutId),this.retryTimeoutId=null),this.abrClient.curBitrate="",this.connectRetry.curNum=0,this.connectRetry.playing=!1,this.statsIntervalId&&(window.clearInterval(this.statsIntervalId),this.statsIntervalId=null),!this.playUrl||!this.svrSig)return[3,5];o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.signal.fetchStopStream({streamurl:this.playUrl,svrsig:this.svrSig})];case 2:return o.sent(),[3,4];case 3:return i=o.sent(),Od.log(i.message||""),[3,4];case 4:this.playUrl=null,this.svrSig=null,o.label=5;case 5:return[4,this.startReport("stop")];case 6:var s;if(o.sent(),this.webrtcConnection.disconnect(),e)null===(t=this.stream)||void 0===t||va(s=t.getTracks()).call(s,(function(e){e.stop()}));return this.stream=null,this.playerView&&(this.playerView.pause(),e&&(this.playerView.srcObject=null,this.playerView.load())),null===(n=(r=this.listener).onPlayEvent)||void 0===n||n.call(r,Wu.PLAY_EVT_PLAY_STOP),[2]}}))}))},e.prototype.switchStream=function(e){return ic(this,void 0,void 0,(function(){var t,r,n;return oc(this,(function(i){switch(i.label){case 0:if(!this.isVideoExisted)return Od.log("Video is not existed"),[2];if(o=this.rawUrl,s=e,!(a=function(e){var t=Wl(e,"tabr_bitrates"),r=Wl(e,"tabr_start_bitrate"),n=Wl(e,"tabr_control");return!(!t||!r||"auto"===n)})(o)||!a(s)||Yl(o,"tabr_start_bitrate",null)!==Yl(s,"tabr_start_bitrate",null))return[3,5];if(!this.svrSig)return[3,4];i.label=1;case 1:return i.trys.push([1,3,,4]),t=Wl(e,"tabr_start_bitrate"),Od.log("abr control: switch "+t),this.abrClient.seq=this.abrClient.seq+1,[4,this.signal.fetchAbrControl({svrsig:this.svrSig,seq:this.abrClient.seq,bitrate_name:t})];case 2:return r=i.sent().seq,this.abrClient.seq=Math.max(this.abrClient.seq,r),this.abrClient.curBitrate=t,[3,4];case 3:return n=i.sent(),Od.log(n.message),this.onPlayError(Wu.PLAY_ERR_REQUEST_ABR_FAIL,{message:n.message}),[3,4];case 4:return[3,7];case 5:return[4,this.stopPlay()];case 6:i.sent(),this.startPlay(e),i.label=7;case 7:return[2]}var o,s,a}))}))},e.prototype.isPlaying=function(){return this.isVideoExisted&&this.isVideoPlaying()},e.prototype.pause=function(){this.playerView&&this.isVideoExisted&&this.isVideoPlaying()&&this.playerView.pause()},e.prototype.resume=function(){return ic(this,void 0,void 0,(function(){var e;return oc(this,(function(t){switch(t.label){case 0:if(!this.playerView||!this.isVideoExisted||this.isVideoPlaying())return[3,4];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.playerView.play()];case 2:return t.sent(),[3,4];case 3:return e=t.sent(),Od.log("resume failed",e),[3,4];case 4:return[2]}}))}))},e.prototype.setMute=function(e){this.playerView&&(this.playerView.muted=!!e)},e.prototype.setVolume=function(e){"number"==typeof e&&e>=0&&e<=100?this.playerView&&(this.playerView.volume=e/100):Od.log("volume must be a number between 0 and 100")},e.prototype.setControls=function(e){this.playerView&&(this.playerView.controls=!!e)},e.prototype.setFullscreen=function(e){this.playerView&&(e?this.requestFullscreen():this.exitFullscreen())},e.prototype.getVideoElement=function(){return this.playerView},e.prototype.destroy=function(){return ic(this,void 0,void 0,(function(){return oc(this,(function(e){switch(e.label){case 0:return window.removeEventListener("pagehide",this.onPagehide),window.removeEventListener("beforeunload",this.onBeforeUnload),this.disconnectTimeoutId&&(window.clearTimeout(this.disconnectTimeoutId),this.disconnectTimeoutId=null),this.retryTimeoutId&&(window.clearTimeout(this.retryTimeoutId),this.retryTimeoutId=null),this.statsIntervalId&&(window.clearInterval(this.statsIntervalId),this.statsIntervalId=null),this.isVideoExisted&&this.webrtcConnection?[4,this.startReport("stop")]:[3,2];case 1:e.sent(),e.label=2;case 2:var t;if(this.stream)va(t=this.stream.getTracks()).call(t,(function(e){e.stop()})),this.stream=null;return this.playerView&&(this.playerView.pause(),this.playerView.srcObject=null,this.playerView.parentElement.removeChild(this.playerView),this.playerView=null),this.webrtcConnection&&(this.webrtcConnection.disconnect(),this.webrtcConnection=null,this.lastStatsReport=null),this.report&&(this.report.destroy(),this.report=null),this.signal=null,[2]}}))}))},e.prototype.isVideoPlaying=function(){return!(!this.playerView||!1!==this.playerView.paused)},e.prototype.requestFullscreen=function(){var e=ql;try{e.requestFullscreen?this.playerView[e.requestFullscreen]({navigationUI:"hide"}):$l(this.playerView)?this.playerView.webkitEnterFullScreen():this.enterFullWindow()}catch(e){Od.log("enter full screen failed, ",e)}},e.prototype.exitFullscreen=function(){var e,t=ql;try{if(t.requestFullscreen){var r=document[t.exitFullscreen]();null===(e=null==r?void 0:r.catch)||void 0===e||e.call(r,(function(e){return Od.log("exit full screen failed, ",null==e?void 0:e.message)}))}else $l(this.playerView)?this.playerView.webkitExitFullScreen():this.exitFullWindow()}catch(e){Od.log("exit full screen failed, ",e)}},e.prototype.enterFullWindow=function(){this.docOrigOverflow=document.documentElement.style.overflow,document.documentElement.style.overflow="hidden",this.playerView.setAttribute("style","position:fixed;overflow:hidden;z-index:9999;left:0;top:0;bottom:0;right:0;width:100% !important;height:100% !important;padding-top:0 !important;background-color:#000;")},e.prototype.exitFullWindow=function(){document.documentElement.style.overflow=this.docOrigOverflow,this.playerView.setAttribute("style",Ku)},e.prototype.onAddStream=function(e){var t,r,n,i;return ic(this,void 0,void 0,(function(){var o,s,a,c,u,l=this;return oc(this,(function(d){switch(d.label){case 0:if(null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,Wu.PLAY_EVT_STREAM_BEGIN),!this.playerView)return[2];if(o=function(){l.playerView.removeEventListener("canplay",o),l.report.markTime("video_play"),l.offerToReceive.video?(l.report.measureTime("u32_first_video_decode_time","pull_start","video_play"),l.report.measureTime("u32_first_i_frame","pull_start","video_play",!0)):(l.report.setData("u32_first_video_decode_time",0),l.report.setData("u32_first_i_frame",0),l.report.setTempData("u32_first_i_frame",0)),l.offerToReceive.audio?l.report.measureTime("u32_first_audio_render_time","pull_start","video_play"):l.report.setData("u32_first_audio_render_time",0),l.playerView.autoplay||l.connectRetry.playing||l.startReport("start")},this.playerView.addEventListener("canplay",o),this.playerView.srcObject=e,this.isVideoExisted=!0,!this.playerView.autoplay&&!this.connectRetry.playing)return[2];s=function(e){var t;Od.log("play failed",e),Vn(t=e.toString()).call(t,"NotAllowedError")?Ma((function(){l.webrtcConnection.getStats().then((function(t){var r,n,i=function(e){if(e){var t=null,r=null;va(e).call(e,(function(e){"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?t=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(r=e.id))}));var n=e.get(t),i=e.get(r);return{video:{bytesReceived:null==n?void 0:n.bytesReceived},audio:{bytesReceived:null==i?void 0:i.bytesReceived}}}}(t);l.onPlayError(Wu.PLAY_ERR_PLAY_FAIL,{message:e.toString(),videoActive:(null===(r=null==i?void 0:i.video)||void 0===r?void 0:r.bytesReceived)>0,audioActive:(null===(n=null==i?void 0:i.audio)||void 0===n?void 0:n.bytesReceived)>0})}))}),1e3):l.onPlayError(Wu.PLAY_ERR_PLAY_FAIL,{message:e.toString()})},d.label=1;case 1:return d.trys.push([1,3,,4]),[4,this.playerView.play()];case 2:return d.sent(),a=zl(),Ul&&a.length>0&&13===a[0]?(this.playerView.pause(),c=function(){return ic(l,void 0,void 0,(function(){var e,t,r;return oc(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,this.playerView.play()];case 1:return n.sent(),Od.log("play ok ios"),null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,Wu.PLAY_EVT_PLAY_BEGIN),this.startReport("start"),[3,3];case 2:return e=n.sent(),s(e),[3,3];case 3:return[2]}}))}))},Ma((function(){c()}),200)):(Od.log("play ok"),null===(i=(n=this.listener).onPlayEvent)||void 0===i||i.call(n,Wu.PLAY_EVT_PLAY_BEGIN),this.startReport("start")),[3,4];case 3:return u=d.sent(),s(u),[3,4];case 4:return[2]}}))}))},e.prototype.onAddTrack=function(e){"video"===e.kind?(this.report.markTime("video_down"),this.report.measureTime("u32_first_frame_down","pull_start","video_down")):"audio"===e.kind&&(this.report.markTime("audio_down"),this.report.measureTime("u32_first_audio_frame_down","pull_start","audio_down")),this.stream?this.stream.addTrack(e):(this.stream=new MediaStream,this.stream.addTrack(e),this.onAddStream(this.stream)),Od.log("track length: "+this.stream.getTracks().length)},e.prototype.onSetLocalDescription=function(e){var t,r,n,i;return ic(this,void 0,void 0,(function(){var o,s,a,c,u,l,d,p,f,h=this;return oc(this,(function(v){var m,y,g,b,S;switch(v.label){case 0:return v.trys.push([0,5,,6]),o=Fu(),s=Yu.parse(window.navigator.userAgent),a=s.browser,c=s.os,u=(c.name||"other")+" "+c.version+";"+(a.name||"other")+" "+a.version,null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,Wu.PLAY_EVT_REQUEST_PULL_BEGIN,{localSdp:e}),this.report.setData("str_session_id",o,!0),this.report.setData("bytes_token",o,!0),this.report.setData("str_ice_info",(y=e.sdp,b="",S="",va(g=y.split("\r\n")).call(g,(function(e){var t=/(?:a=ice-ufrag:)(.+)/.exec(e);if(t)b=t[1];else{var r=/(?:a=ice-pwd:)(.+)/.exec(e);r&&(S=r[1])}})),b+"_"+S),!0),this.report.markTime("request_start"),l=null,Wa(m=this.playUrl).call(m,"webrtc")?[4,this.signal.fetchPullStream({streamurl:this.playUrl,sessionid:o,clientinfo:u,localsdp:e})]:[3,2];case 1:return l=v.sent(),[3,4];case 2:return[4,this.signal.fetchStreamSdp({streamurl:this.playUrl,sessionid:o,localsdp:e})];case 3:l=v.sent(),v.label=4;case 4:return this.report.markTime("request_end"),this.report.measureTime("u32_signal_time","request_start","request_end"),d=l.remoteSdp,p=l.svrSig,null===(i=(n=this.listener).onPlayEvent)||void 0===i||i.call(n,Wu.PLAY_EVT_REQUEST_PULL_SUCCESS,{remoteSdp:d}),this.webrtcConnection.connect(d),this.svrSig=p,this.disconnectTimeoutId&&window.clearTimeout(this.disconnectTimeoutId),this.disconnectTimeoutId=Ma((function(){Od.log("connect timeout, try reconnect"),h.webrtcConnection.disconnect({code:Uu.NEED_RECONNECT,msg:"Connection disconnected, please try again"})}),5e3),[3,6];case 5:return f=v.sent(),Od.log(f.message),this.webrtcConnection.disconnect(),this.onPlayError(Wu.PLAY_ERR_REQUEST_PULL_FAIL,{message:f.message}),[3,6];case 6:return[2]}}))}))},e.prototype.onConnect=function(e){var t,r,n=this;Od.log("TXLivePlayer onConnect",e),e.code===Vu.SUCCESS&&(this.disconnectTimeoutId&&(window.clearTimeout(this.disconnectTimeoutId),this.disconnectTimeoutId=null),this.startStat(),null===(r=(t=this.listener).onPlayEvent)||void 0===r||r.call(t,Wu.PLAY_EVT_SERVER_CONNECTED),this.abrClient.curBitrate&&this.svrSig&&(Od.log("abr control: reconnect "+this.abrClient.curBitrate),this.abrClient.seq=this.abrClient.seq+1,this.signal.fetchAbrControl({svrsig:this.svrSig,seq:this.abrClient.seq,bitrate_name:this.abrClient.curBitrate}).then((function(e){var t=e.seq;n.abrClient.seq=Math.max(n.abrClient.seq,t)})).catch((function(e){Od.log(e.message)}))))},e.prototype.onDisconnect=function(e){return ic(this,void 0,void 0,(function(){var t,r=this;return oc(this,(function(n){switch(n.label){case 0:if(this.lastStatsReport=null,this.streamEmptyCount=0,this.streamDecodeFailCount=0,this.playingStatus=Gu.INIT,this.disconnectTimeoutId&&(window.clearTimeout(this.disconnectTimeoutId),this.disconnectTimeoutId=null),this.statsIntervalId&&(window.clearInterval(this.statsIntervalId),this.statsIntervalId=null),Od.log("TXLivePlayer onDisconnect",e),e.code!==Uu.NEED_RECONNECT)return[3,6];if(this.connectRetry.playing=this.isPlaying(),!this.playUrl||!this.svrSig)return[3,5];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.signal.fetchStopStream({streamurl:this.playUrl,svrsig:this.svrSig})];case 2:return n.sent(),[3,4];case 3:return t=n.sent(),Od.log(t.message||""),[3,4];case 4:this.svrSig=null,n.label=5;case 5:var i;if(this.stream)va(i=this.stream.getTracks()).call(i,(function(e){e.stop()})),this.stream=null;this.isVideoExisted&&this.playerView&&(this.playerView.pause(),this.isVideoExisted=!1),this.retryTimeoutId&&(window.clearTimeout(this.retryTimeoutId),this.retryTimeoutId=null),this.retryTimeoutId=Ma((function(){var e,t;r.connectRetry.curNum+=1;var n=r.connectRetry,i=n.curNum,o=n.maxNum;i<=o?(Od.log("current retry num: "+i+", max retry num: "+o),null===(t=(e=r.listener).onPlayEvent)||void 0===t||t.call(e,Wu.PLAY_EVT_SERVER_RECONNECT),r.startPlay(r.rawUrl)):(r.onPlayError(Wu.PLAY_ERR_SERVER_DISCONNECT),r.connectRetry.curNum=0,r.connectRetry.playing=!1),r.retryTimeoutId=null}),1e3*this.connectRetry.delay),n.label=6;case 6:return[2]}}))}))},e.prototype.onError=function(e){this.onPlayError(Wu.PLAY_ERR_WEBRTC_FAIL,{message:e})},e.prototype.startStat=function(){return ic(this,void 0,void 0,(function(){var e,t,r=this;return oc(this,(function(n){switch(n.label){case 0:return this.report.markTime("server_connected"),this.report.measureTime("u32_connect_server_time","pull_start","server_connected"),this.statsIntervalId&&(window.clearInterval(this.statsIntervalId),this.statsIntervalId=null),[4,this.webrtcConnection.getStats()];case 1:return(e=n.sent())&&(this.report.setTempData("start_stats",e),this.report.setTempData("last_stats",e),this.report.setTempData("last_time",(new Date).getTime()),this.onStats(e)),t=0,this.statsIntervalId=Ya((function(){var e;null===(e=r.webrtcConnection)||void 0===e||e.getStats().then((function(e){e&&r.onStats(e),(t+=1)%5==0&&r.startReport("interval")}))}),1e3),[2]}}))}))},e.prototype.onStats=function(e){var t,r,n,i,o,s,a,c,u,l,d=function(e,t){if(void 0===t&&(t=null),t){var r=null,n=null,i=null,o=null;va(e).call(e,(function(e){"track"===e.type&&("video"===e.kind||e.frameWidth?r=e.id:("audio"===e.kind||e.audioLevel)&&(i=e.id)),"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?n=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(o=e.id))}));var s=e.get(r),a=e.get(n),c=t.get(r),u=t.get(n),l=0;void 0!==(null==a?void 0:a.timestamp)&&void 0!==(null==u?void 0:u.timestamp)&&(l=(a.timestamp-u.timestamp)/1e3);var d=void 0;if((null==a?void 0:a.codecId)&&(w=e.get(a.codecId))){var p=w.mimeType,f=w.payloadType,h=w.sdpFmtpLine;d=(null==p?void 0:p.replace("video/",""))||"",f&&h&&(d=d+" ("+f+", "+h+")")}var v=void 0;void 0!==(null==a?void 0:a.framesPerSecond)?v=a.framesPerSecond:void 0!==(null==a?void 0:a.framerateMean)?v=a.framerateMean:void 0!==(null==s?void 0:s.framesReceived)&&void 0!==(null==c?void 0:c.framesReceived)&&l&&(v=(s.framesReceived-c.framesReceived)/l);var m=void 0;void 0!==(null==a?void 0:a.framesDecoded)&&void 0!==(null==u?void 0:u.framesDecoded)&&l&&(m=(a.framesDecoded-u.framesDecoded)/l);var y=void 0;void 0!==(null==a?void 0:a.bytesReceived)&&void 0!==(null==u?void 0:u.bytesReceived)&&l&&(y=8*(a.bytesReceived-u.bytesReceived)/l);var g=void 0;void 0!==(null==s?void 0:s.jitterBufferDelay)&&void 0!==(null==s?void 0:s.jitterBufferEmittedCount)&&void 0!==(null==c?void 0:c.jitterBufferDelay)&&void 0!==(null==c?void 0:c.jitterBufferEmittedCount)&&(g=s.jitterBufferEmittedCount-c.jitterBufferEmittedCount?(s.jitterBufferDelay-c.jitterBufferDelay)/(s.jitterBufferEmittedCount-c.jitterBufferEmittedCount)*1e3:s.jitterBufferDelay/s.jitterBufferEmittedCount*1e3);var b=void 0;void 0!==(null==a?void 0:a.totalDecodeTime)&&void 0!==(null==s?void 0:s.framesDecoded)&&void 0!==(null==u?void 0:u.totalDecodeTime)&&void 0!==(null==c?void 0:c.framesDecoded)&&(b=s.framesDecoded-c.framesDecoded?(a.totalDecodeTime-u.totalDecodeTime)/(s.framesDecoded-c.framesDecoded)*1e3:a.totalDecodeTime/s.framesDecoded*1e3);var S=e.get(i),T=e.get(o),_=t.get(i),C=t.get(o);void 0!==(null==T?void 0:T.timestamp)&&void 0!==(null==C?void 0:C.timestamp)&&(l=(T.timestamp-C.timestamp)/1e3);var w,E=void 0;(null==T?void 0:T.codecId)&&(w=e.get(T.codecId))&&(p=w.mimeType,f=w.payloadType,h=w.sdpFmtpLine,E=(null==p?void 0:p.replace("audio/",""))||"",f&&h&&(E=E+" ("+f+", "+h+")"));var R=void 0;void 0!==(null==S?void 0:S.audioLevel)?R=null==S?void 0:S.audioLevel:void 0!==(null==T?void 0:T.audioLevel)&&(R=null==T?void 0:T.audioLevel);var P=void 0;void 0!==(null==T?void 0:T.bytesReceived)&&void 0!==(null==C?void 0:C.bytesReceived)&&l&&(P=8*(T.bytesReceived-C.bytesReceived)/l);var A=void 0;return void 0!==(null==S?void 0:S.jitterBufferDelay)&&void 0!==(null==S?void 0:S.jitterBufferEmittedCount)&&void 0!==(null==_?void 0:_.jitterBufferDelay)&&void 0!==(null==_?void 0:_.jitterBufferEmittedCount)&&(A=S.jitterBufferEmittedCount-_.jitterBufferEmittedCount?(S.jitterBufferDelay-_.jitterBufferDelay)/(S.jitterBufferEmittedCount-_.jitterBufferEmittedCount)*1e3:S.jitterBufferDelay/S.jitterBufferEmittedCount*1e3),{timestamp:(null==a?void 0:a.timestamp)||(null==T?void 0:T.timestamp),video:{codec:d,bitrate:y&&Number(y.toFixed(2)),framesPerSecond:v&&Math.round(v),framesDecodedPerSecond:m&&Math.round(m),frameWidth:null==s?void 0:s.frameWidth,frameHeight:null==s?void 0:s.frameHeight,framesDecoded:null==a?void 0:a.framesDecoded,framesDropped:null==s?void 0:s.framesDropped,framesReceived:null==s?void 0:s.framesReceived,packetsLost:null==a?void 0:a.packetsLost,packetsReceived:null==a?void 0:a.packetsReceived,nackCount:null==a?void 0:a.nackCount,firCount:null==a?void 0:a.firCount,pliCount:null==a?void 0:a.pliCount,jitterBufferDelay:g&&Number(g.toFixed(2)),frameDecodeAvgTime:b&&Number(b.toFixed(2))},audio:{codec:E,audioLevel:R,bitrate:P&&Number(P.toFixed(2)),packetsLost:null==T?void 0:T.packetsLost,packetsReceived:null==T?void 0:T.packetsReceived,jitterBufferDelay:A&&Number(A.toFixed(2))}}}}(e,this.lastStatsReport);if(this.lastStatsReport=e,d&&(d.video.frameWidth=d.video.frameWidth||(null===(t=this.playerView)||void 0===t?void 0:t.videoWidth)||void 0,d.video.frameHeight=d.video.frameHeight||(null===(r=this.playerView)||void 0===r?void 0:r.videoHeight)||void 0,null===(i=(n=this.listener).onPlayStats)||void 0===i||i.call(n,d)),(null==d?void 0:d.video)&&this.offerToReceive.video){var p=d.video,f=p.bitrate,h=p.framesPerSecond,v=p.framesDecoded,m=p.framesDecodedPerSecond;if(f&&h&&(this.connectRetry.curNum=0),!this.isVideoExisted)return;if(f&&h?this.streamEmptyCount=0:(this.streamEmptyCount+=1,this.streamEmptyCount>=5&&(null===(s=(o=this.listener).onPlayEvent)||void 0===s||s.call(o,Wu.PLAY_EVT_STREAM_EMPTY),this.streamEmptyCount=0)),f&&h&&!v?(this.streamDecodeFailCount+=1,3===this.streamDecodeFailCount&&this.onPlayError(Wu.PLAY_ERR_DECODE_FAIL,{message:"decode failed",bitrate:f,framesPerSecond:h})):this.streamDecodeFailCount=0,m>=0||h>=0)if(m<=5||h<=5){var y=this.report.getTempData("freeze_interval_count")||0;this.report.setTempData("freeze_interval_count",y+1),this.playingStatus!==Gu.WAITING&&(this.playingStatus=Gu.WAITING,this.report.setTempData("freeze_start_time",(new Date).getTime()),null===(c=(a=this.listener).onPlayEvent)||void 0===c||c.call(a,Wu.PLAY_EVT_PLAY_WAITING_BEGIN))}else{if(this.playingStatus===Gu.WAITING){var g=this.report.getTempData("freeze_start_time"),b=(new Date).getTime(),S=b-g,T=this.report.getTempData("video_freeze")||{},_=T.count,C=void 0===_?0:_,w=T.totalTime,E=void 0===w?0:w,R=T.maxTime,P=void 0===R?0:R,A=T.endTimes,M=void 0===A?[]:A;C+=1,E+=S,P=Math.max(S,P),M=cc(cc([],ac(M)),[b]),this.report.setTempData("video_freeze",{count:C,totalTime:E,maxTime:P,endTimes:M}),null===(l=(u=this.listener).onPlayEvent)||void 0===l||l.call(u,Wu.PLAY_EVT_PLAY_WAITING_STOP)}this.playingStatus=Gu.PLAYING}}},e.prototype.startReport=function(e,t){var r,n;return void 0===t&&(t=""),ic(this,void 0,void 0,(function(){var i,o,s,a,c,u,l,d,p,f,h,v,m,y,g,b,S,T,_,C,w,E,R,P,A,M,k,O,D,I,x,L,N,F;return oc(this,(function(j){switch(j.label){case 0:return i=(new Date).getTime(),o=this.report.getTempData("pull_start_time"),"stop"===e?(s=this.report.getTempData("u32_first_i_frame")||0,this.report.setData("u64_timestamp",i),this.report.setData("u64_begin_timestamp",o),this.report.setData("u32_result",Math.round((i-o)/1e3)),this.report.setData("u32_first_i_frame",s),this.report.setData("u32_first_frame_black",s>1e4?1:0),a=this.report.getTempData("video_freeze")||{},c=a.count,m=void 0===c?0:c,u=a.totalTime,l=void 0===u?0:u,d=a.maxTime,p=void 0===d?0:d,this.report.setData("u64_block_count",m),this.report.setData("u32_video_block_time",l),this.report.setData("u64_block_duration_max",p),this.report.setData("u32_avg_block_time",l&&m?Math.round(l/m):0),this.report.setData("u32_delay_report",t?1:0)):"interval"===e&&(this.report.setData("u64_timestamp",i),this.report.setData("u64_playtime",i-o),f=(this.report.getTempData("video_freeze")||{}).endTimes,h=void 0===f?[]:f,v=this.report.getTempData("last_time")||0,m=tc(h).call(h,(function(e){return e>=v&&e<i})).length,this.report.setData("u32_video_block_count",m),this.report.setTempData("last_time",i),y=this.report.getTempData("freeze_interval_count")||0,this.report.setData("u32_block_usage",y/5*1e3),this.report.setTempData("freeze_interval_count",0)),g=null,"stop"!==e||"pagehide"!==t?[3,1]:(g=this.report.getTempData("last_stats"),[3,3]);case 1:return[4,this.webrtcConnection.getStats()];case 2:g=j.sent(),j.label=3;case 3:return g&&(b=function(e){var t=null;va(e).call(e,(function(r){var n;"candidate-pair"===r.type&&r.selected?t=r.remoteCandidateId:"transport"===r.type&&r.selectedCandidatePairId&&(t=null===(n=e.get(r.selectedCandidatePairId))||void 0===n?void 0:n.remoteCandidateId)}));var r=e.get(t)||{},n=r.address,i=void 0===n?"":n,o=r.port;return{address:i,port:void 0===o?"":o}}(g),S=b.address,T=b.port,this.report.setData("u32_server_ip",S+(T?":":"")+T),"stop"===e?(_=this.report.getTempData("start_stats"),C=function(e,t){var r=null,n=null,i=null;va(e).call(e,(function(e){"track"!==e.type||"video"!==e.kind&&!e.frameWidth||(r=e.id),"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?n=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(i=e.id))}));var o=e.get(r),s=e.get(n),a=e.get(i),c=null==t?void 0:t.get(n),u=0;void 0!==(null==s?void 0:s.timestamp)&&void 0!==(null==c?void 0:c.timestamp)&&(u=(s.timestamp-c.timestamp)/1e3);var l=0;void 0!==(null==s?void 0:s.packetsLost)&&void 0!==(null==s?void 0:s.packetsReceived)&&(l=s.packetsLost/(s.packetsLost+s.packetsReceived)*1e3);var d=0;void 0!==(null==a?void 0:a.packetsLost)&&void 0!==(null==a?void 0:a.packetsReceived)&&(d=a.packetsLost/(a.packetsLost+a.packetsReceived)*1e3);var p=0;return void 0!==(null==s?void 0:s.framesDecoded)&&void 0!==(null==c?void 0:c.framesDecoded)&&u&&(p=Math.round((s.framesDecoded-c.framesDecoded)/u)),{width:(null==o?void 0:o.frameWidth)||0,height:(null==o?void 0:o.frameHeight)||0,videoFractionLost:Number(l.toFixed(2)),audioFractionLost:Number(d.toFixed(2)),framerateMean:p}}(g,_),w=C.width,E=C.height,N=C.videoFractionLost,O=C.audioFractionLost,R=C.framerateMean,this.report.setData("u32_video_width",w||(null===(r=this.playerView)||void 0===r?void 0:r.videoWidth)),this.report.setData("u32_video_height",E||(null===(n=this.playerView)||void 0===n?void 0:n.videoHeight)),this.report.setData("u32_video_drop_usage",N),this.report.setData("u32_audio_drop_usage",O),this.report.setData("u32_video_avg_fps",R)):"interval"===e&&(P=this.report.getTempData("last_stats"),A=function(e,t){var r=null,n=null,i=null,o=null;va(e).call(e,(function(e){"track"===e.type&&("video"===e.kind||e.frameWidth)&&(r=e.id),"inbound-rtp"===e.type&&("video"===e.kind||"video"===e.mediaType?n=e.id:"audio"!==e.kind&&"audio"!==e.mediaType||(i=e.id)),"candidate-pair"===e.type&&e.selected?o=e.id:"transport"===e.type&&e.selectedCandidatePairId&&(o=e.selectedCandidatePairId)}));var s=e.get(r),a=e.get(n),c=null==t?void 0:t.get(r),u=null==t?void 0:t.get(n),l=0;void 0!==(null==a?void 0:a.timestamp)&&void 0!==(null==u?void 0:u.timestamp)&&(l=(a.timestamp-u.timestamp)/1e3);var d=0;void 0!==(null==a?void 0:a.packetsLost)&&void 0!==(null==u?void 0:u.packetsLost)&&(d=a.packetsLost-u.packetsLost);var p=0;void 0!==(null==s?void 0:s.framesReceived)&&void 0!==(null==c?void 0:c.framesReceived)&&l?p=(s.framesReceived-c.framesReceived)/l:void 0!==(null==a?void 0:a.framerateMean)&&void 0!==(null==u?void 0:u.framerateMean)&&(p=(a.framerateMean+u.framerateMean)/2);var f=0;void 0!==(null==a?void 0:a.framesDecoded)&&void 0!==(null==u?void 0:u.framesDecoded)&&l&&(f=(a.framesDecoded-u.framesDecoded)/l);var h=0;void 0!==(null==a?void 0:a.bytesReceived)&&void 0!==(null==u?void 0:u.bytesReceived)&&l&&(h=8*(a.bytesReceived-u.bytesReceived)/l/1024);var v=0;void 0!==(null==a?void 0:a.packetsLost)&&void 0!==(null==a?void 0:a.packetsReceived)&&void 0!==(null==u?void 0:u.packetsLost)&&void 0!==(null==u?void 0:u.packetsReceived)&&(v=(S=a.packetsLost-u.packetsLost)/(S+(a.packetsReceived-u.packetsReceived))*1e3);var m=e.get(i),y=null==t?void 0:t.get(i);void 0!==(null==m?void 0:m.timestamp)&&void 0!==(null==y?void 0:y.timestamp)&&(l=(m.timestamp-y.timestamp)/1e3);var g=0;void 0!==(null==m?void 0:m.packetsLost)&&void 0!==(null==y?void 0:y.packetsLost)&&(g=m.packetsLost-y.packetsLost);var b=0;void 0!==(null==m?void 0:m.bytesReceived)&&void 0!==(null==y?void 0:y.bytesReceived)&&l&&(b=8*(m.bytesReceived-y.bytesReceived)/l/1024);var S,T=0;void 0!==(null==m?void 0:m.packetsLost)&&void 0!==(null==m?void 0:m.packetsReceived)&&void 0!==(null==y?void 0:y.packetsLost)&&void 0!==(null==y?void 0:y.packetsReceived)&&(T=(S=m.packetsLost-y.packetsLost)/(S+(m.packetsReceived-y.packetsReceived))*1e3);var _=e.get(o),C=null==t?void 0:t.get(o),w=0;return void 0!==(null==_?void 0:_.totalRoundTripTime)&&void 0!==(null==_?void 0:_.responsesReceived)&&void 0!==(null==C?void 0:C.totalRoundTripTime)&&void 0!==(null==C?void 0:C.responsesReceived)&&(w=(_.totalRoundTripTime-C.totalRoundTripTime)/(_.responsesReceived-C.responsesReceived)*1e3),{videoPacketsLost:Math.max(d,0),videoReceiveFps:Math.round(p),videoDecodeFps:Math.round(f),videoBitrate:Number(h.toFixed(2)),videoFractionLost:Math.max(Number(v.toFixed(2)),0),audioPacketsLost:Math.max(g,0),audioBitrate:Number(b.toFixed(2)),audioFractionLost:Math.max(Number(T.toFixed(2)),0),roundTripTime:Math.round(w)}}(g,P),M=A.audioPacketsLost,k=A.audioBitrate,O=A.audioFractionLost,D=A.videoReceiveFps,I=A.videoDecodeFps,x=A.videoBitrate,L=A.videoPacketsLost,N=A.videoFractionLost,F=A.roundTripTime,this.report.setData("u32_audio_drop",M),this.report.setData("u32_audio_drop_usage",O),this.report.setData("u32_avg_audio_bitrate",k),this.report.setData("u32_video_drop",L),this.report.setData("u32_video_drop_usage",N),this.report.setData("u32_video_recv_fps",D),this.report.setData("u32_fps",I),this.report.setData("u32_avg_video_bitrate",x),this.report.setData("u32_avg_net_speed",Number((x+k).toFixed(2))),this.report.setData("u64_rtt",F),this.report.setTempData("last_stats",g))),this.report.startReport(e),[2]}}))}))},e.prototype.onPlayError=function(e,t){var r,n;return ic(this,void 0,void 0,(function(){var i;return oc(this,(function(o){return i=(t||{}).message,null===(n=(r=this.listener).onPlayEvent)||void 0===n||n.call(r,e,t),this.report.setData("u64_err_code",e),this.report.setData("str_err_info",i||""),e===Wu.PLAY_ERR_SERVER_DISCONNECT?this.startReport("stop"):this.startReport("start"),[2]}))}))},e.prototype.onBeforeUnload=function(){window.removeEventListener("pagehide",this.onPagehide),this.isVideoExisted&&this.webrtcConnection&&this.report&&this.startReport("stop","beforeunload")},e.prototype.onPagehide=function(){this.isVideoExisted&&this.webrtcConnection&&this.report&&this.startReport("stop","pagehide")},e.version=qu,e}();return FS}));