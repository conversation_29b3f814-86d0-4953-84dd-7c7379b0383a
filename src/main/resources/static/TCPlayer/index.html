<!DOCTYPE html>
<html>
	 <link href="./tcplayer.min.css" rel="stylesheet"/>
	 <!--如果需要在 Chrome 和 Firefox 等现代浏览器中通过 H5 播放 Webrtc 视频，需要在 tcplayer.vx.x.x.min.js 之前引入 TXLivePlayer-x.x.x.min.js。-->
	 <!--有些浏览器环境不支持 Webrtc，播放器会将 Webrtc 流地址自动转换为 HLS 格式地址，因此快直播场景同样需要引入hls.min.x.xx.xm.js。-->
	 <script src="./libs/TXLivePlayer-1.2.3.min.js"></script>
	 <!--如果需要在 Chrome 和 Firefox 等现代浏览器中通过 H5 播放 HLS 协议的视频，需要在 tcplayer.vx.x.x.min.js 之前引入 hls.min.x.xx.xm.js。-->
	 <script src="./libs/hls.min.1.1.5.js"></script>
	 <!--如果需要在 Chrome 和 Firefox 等现代浏览器中通过 H5 播放 FLV 格式的视频，需要在 tcplayer.vx.x.x.min.js 之前引入 flv.min.x.x.x.js。-->
	 <script src="./libs/flv.min.1.6.3.js"></script>
	  <!--如果需要在 Chrome 和 Firefox 等现代浏览器中通过 H5 播放 DASH 视频，需要在 tcplayer.vx.x.x.min.js 之前引入 dash.min.x.x.x.js。-->
	 <script src="./libs/dash.all.min.4.4.1.js"></script>
	 <!--播放器脚本文件-->
	 <script src="./tcplayer.v4.6.0.min.js"></script>
	<head>
		<meta charset="utf-8">
		<title></title>
		<style>
			body{
				margin: 0;
				text-align: center;
			}
		</style>
	</head>
	<body>
		<div id="videoContainer">
			<video id="player-container-id"  preload="auto" playsinline webkit-playsinline style="width: 100%;object-fit:fill">
			</video>
		</div>
		<script>
			window.onload = function () {
				window.play = function (width, height, url) {
					var video = document.createElement("video");
					video.id = "player-container-id";
					// video.preload = "auto";
					// video.setAttribute("playsinline", true)
					// video.setAttribute("webkit-playsinline", true)

					// video.style.width = "100%";
					video.style["object-fit"] = "fill";
					video.style["margin"] = "auto";
					var videoContainer = document.getElementById("videoContainer");
					videoContainer.innerHTML = "";
					videoContainer.appendChild(video);
					// var height = video.clientWidth * 0.6 * 0.75;
					// video.style.height = height + "px";

					window.player = TCPlayer('player-container-id',); // player-container-id 为播放器容器 ID，必须与 html 中一致

					checkUrl(width, height, url);
				};

				window.checkUrl = function(width, height, url) {
					var xhr = new XMLHttpRequest();
					xhr.open("get", url, false)
					xhr.onreadystatechange = function() {
						// 如果已经关闭视频不进行任何操作
						if (!sessionStorage.getItem("VIDEO-TOKEN")) {
							return
						}
						if (xhr.readyState === 4 && xhr.status === 404) {
							setTimeout(function () {
								checkUrl(width, height, url);
							}, 5000)
						} else {
							window.parent.closeLoading()
							initTCPlay(width, height, url)
						}
					}
					xhr.send();
				}

				window.initTCPlay = function(width, height, url) {
					player.src(url); // url 播放地址
					// player.width(height * 4 / 3);
					// player.height(height);
					player.on("loadedmetadata", function () {
						console.log(player.videoHeight())
						console.log(player.videoWidth())
						console.log(player.height())
						console.log(player.width())
						var videoWidth = player.videoWidth(),
							videoHeight = player.videoHeight();
						var frameHeight = player.videoHeight();
						if (videoWidth > width) {
							player.width(width);
							frameHeight = videoHeight / (videoWidth / width)
							player.height(frameHeight)
						}

						window.parent.setFrameHeight(frameHeight + 10);
					})
					player.ready(function () {
						player.play()
					}),

					player.on('error', function(error) {
						window.timer = setTimeout(function () {
							clearTimeout(window.timer);
							window.player.dispose();
							window.play(width, height, url);
						}, 2000)
						// var timers = 0;
						// var inter = setInterval(function (){
						// 	timers++;
						// 	if (timers >= 5) {
						// 		timers = 0;
						// 		clearInterval(inter);
						// 		window.parent.TCPlayerDestory()
						// 	}
						// }, 1000)
						// 做一些处理`

					});
				};

				window.destroy = function() {
					clearTimeout(window.timer);
					window.player.dispose()
				}
			}
		</script>
	</body>
</html>