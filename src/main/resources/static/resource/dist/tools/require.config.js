/**
 * Created by will.song on 2016/10/25.
 */

require.config({
    urlArgs: 'v='+new Date().getTime(),
    baseUrl: PUBLIC_PATH + "/resource/app",
    paths: {
        "jquery":"./components/jquery/jquery-3.6.0",
        "bundle":"./core/bundle",
        "text":"./tools/text",
        "css":"./tools/css",
        "startup":"./core/startup",
        "init":"./core/init",
        "common":"./core/common",
        "global":"./core/global",
        "facilityone":"./core/facilityone",
        "io":"./components/netty/socket.io-1.3.0",
        "bootbox":"./components/bootbox/index",
        "jquery.cookie":"./components/jquery.cookie/jquery.cookie.min",
        "jquery.hash":"./components/jquery.hash/jquery-plugin-hash",
        "jquery.slimscroll":"./components/jquery.slimscroll/jquery.slimscroll.min",
        "jquery.serializeJSON":"./components/jquery.serializeJSON/jquery.serializejson",
        "jquery.form":"./components/jquery.form/jquery-plugin-form",
        "jquery.inputclear":"./components/jquery.inputclear/jquery-plugin-input-clear",
        "jquery.print":"./components/jquery.print/index",

        "timeago":"./components/jquery.timeago/jquery.timeago.min",
        "timeago.locale":"./components/jquery.timeago/jquery.timeago.locale",

        "bootstrap":"./components/bootstrap/js/bootstrap.min",
        "bootstrapValidatorWrapper":"./components/bootstrap-validate/index",
        "bootstrapValidator":"./components/bootstrap-validate/bootstrap-validator.min",
        "bootstrapValidator.locale":"./components/bootstrap-validate/bootstrap-validator.locale",

        /* datatables */
        "XiaTableV2":"./components/XiaDataTables/XiaTableV2",
        "datatables":"./components/XiaDataTables/datatables-1.10/js/jquery.dataTables-v2",
        "datatables.tools":"./components/XiaDataTables/datatables-1.10/js/dataTables.tableTools",
        "datatables.locale":"./components/XiaDataTables/datable.locale",

        "XiaI18n":"./core/xiai18n",
        "XiaI18n.resource":"../../i18n/properties",
        "l10n":"./components/l10n/l10n",

        /* autocomplete */
        "XiaAutoComplete":"./components/XiaAutoComplete/jquery-plugin-autocomplete",
        "MagicSuggest":"./components/XiaAutoComplete/magic-suggest-2.1.4/magicsuggest",

        /* city list */
        "cityselect":"./components/jquery.city.select/city.select.min",
        "citydata":"./components/jquery.city.select/data",

        /* upload file */
        "XiaUploadFile":"./components/XiaUploadFile/jquery-plugin-dropzone",
        "dropzone":"./components/XiaUploadFile/dropzone/dropzone",

        /* echarts */
        "echarts":"./components/echarts/echarts.min",
        "chinaMap":"./components/echarts/map/china",

        "circleProgress":"./components/circleprogress/jquery.circleprogress",

        /* zTree */
        "zTree":"./components/ztree/jquery-plugin-ztree",
        "zTree.core":"./components/ztree/jquery.ztree.core-3.5",
        "zTree.excheck":"./components/ztree/jquery.ztree.excheck-3.5",
        "zTree.exedit":"./components/ztree/jquery.ztree.exedit-3.5",

        /* daterange */
        "XiaDateRange":"./components/XiaDateRange/jquery-plugin-daterange",
        "daterangepicker":"./components/XiaDateRange/bootstrap-daterangepicker/daterangepicker",
        "moment":"./components/XiaDateRange/bootstrap-daterangepicker/moment",

        "form":"./core/form",

        "shang.tabs":"./components/shang.tabs/index",
      //  "shang.grid":"./components/ShangDataTables/index",

        "vue":"./components/vue/vue",
        "ELEMENT":"./components/element.ui/index",
        "ELEMENT.locale":"./components/element.ui/locale/start",
        "ELEMENT.locale.zh-cn":"./components/element.ui/locale/lang/zh-cn",
        "ELEMENT.locale.en":"./components/element.ui/locale/lang/en",

        "hls": "./components/hls/hls",

        "FMOne":'./components/shang.extensions/index',
        "core.notice":'./core/notice',
        "core.messagebox":'./core/messagebox',
        "app.header":'./components/app.header/index',
        "app.menu":'./components/app.menu/index',
        "app.rootmenu":'./components/app.menu/rootmenu',
        "app.submenu":'./components/app.menu/sub-menu',
        "app.memberselect":'./components/app.memberselect/index',
        "app.timeago":"./components/jquery.timeago/index",
        "modules.contract.RemindContractDetail":"../../business/contract/contract004-remind-contract-detail",
        "modules.contract.NewContract":"../../business/contract/contract003-new-contract",
        "modules.contract.ContractDetail":"../../business/contract/contract003-contract-detail",
        "modules.contract.NewArchives":"../../business/contract/contract007-new",
        "modules.contract.DetailArchives":"../../business/contract/contract007-detail",
        "modules.ePayment.NewEPayment":"../../business/epayment/epayment001-new-epayment",
        "modules.ePayment.EPaymentDetail":"../../business/epayment/epayment001-epayment-detail",
        "modules.ePayment.NewRefund":"../../business/epayment/epayment001-new-refund",
        "modules.epayment.RefundDetails":"../../business/epayment/epayment002-refund-details",
        "modules.report.EmployeeKpiTable":"../../business/report/report071-employeekpi-report-table",
        "reportCanvas":"./components/shang.extensions/report-canvas-util",
        "countUp":"./components/countUp/countUp",
        "shang.countUp":"./components/shang.countUp/index",
        "modules.monitoring.Detail":"../../../business/monitoring/monitoring003-setting-detail",
        "modules.monitoring.Static":"../../../business/monitoring/monitoring004-monitoring-static",
        "modules.undertake.NewUnder":"../../business/undertake/ut001-task-new",
        "modules.undertake.NewTemplate":"../../business/undertake/ut002-template-new",
        "modules.undertake.ChooseSpae":"../../business/undertake/ut001-choose-space",
        "modules.undertake.UnderDetail":"../../business/undertake/ut001-task-detail",
        "modules.ezviz.Video":"../../business/ezviz/ezviz-video",
        "modules.monitor.video":"../../business/asset/asset005-monitor-video",
        "modules.iot.detail":"../../business/iot/iot003-detail",
    },
    shim:{
        "init":{
            "deps":["facilityone"]
        },
        "jquery.hash":{
            "deps":["jquery"]
        },
        "jquery.cookie":{
            "deps":["jquery"]
        },
        "jquery.slimscroll":{
            "deps":["jquery"]
        },
        "jquery.form":{
            "deps":["jquery"]
        },
        "jquery.timeago":{
            "deps":["jquery"]
        },
        "timeago.locale":{
            "deps":["timeago"]
        },
        "bootstrap":{
            "deps":["jquery"]
        },
        "datatables.locale":{
            "deps":["datatables"]
        },
        "XiaI18n":{
            "deps":[
                "XiaI18n.resource"
            ]
        },
        "XiaI18n.resource":{
            "deps":[
                "l10n"
            ]
        },
        "XiaAutoComplete":{
            "deps":["MagicSuggest"]
        },
        "XiaDateRange":{
            "deps":["daterangepicker","moment"]
        },
        "cityselect":{
            "deps":["citydata","jquery"]
        },
        "chinaMap":{
            "deps":["echarts"]
        },
        "zTree":{
            "deps":["zTree.excheck","zTree.exedit"]
        },
        "zTree.excheck":{
            "deps":["zTree.core"]
        },
        "zTree.exedit":{
            "deps":["zTree.core"]
        },
        "zTree.core":{
            "deps":["jquery"]
        }
    },
    waitSeconds: 60
});