define(['app.rootmenu'],function() {
    var isInstalled = false;
    var vm = null;

    function install(Vue, $){
        if(!isInstalled){
            vm = new Vue({
                el:"#VueElement_LeftMenu",
                data:{
                    selectMenu:'',
                    isNarrow:false,
                },
                methods:{
                    openMenu:function(index){
                        this.$refs.rootMenu.$refs.menu.handleSelect(index);
                    }
                },
                mounted:function(){
                    if(location.hash.indexOf('__aurl=%2Fchart%2Fdashboard%2Findex_new') > 0) {
                        this.isNarrow = true;
                    } else {
                        this.isNarrow = false;
                    }
                    var me = this;
                    this.$on('home-load', function () {
                        var path =  $.hashAjax.getHashValue().path;
                        if(path){
                            me.selectMenu = path;
                        }else {
                            me.selectMenu = "0";
                        }
                    })
                    //this.selectMenu =  $.hashAjax.getHashValue().path;
                    var type = FO.get_uri_param("type");
                    if(isWhiteUser == "false" || typeof (type)=='undefined'){
                        $("#VueElement_Header").show();
                        $("#VueElement_LeftMenu").show();
                        $(".main-content").removeClass('no-visiable');
                    }
                }
            });
        }
        return vm;
    }

    return { install: install }
});
