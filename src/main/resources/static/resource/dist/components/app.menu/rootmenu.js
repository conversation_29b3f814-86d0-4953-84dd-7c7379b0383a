define(['text!./components/app.menu/rootmenu.html','app.submenu','vue','ELEMENT','facilityone'],function(html,subMenu,Vue, ELEMENT){
   Vue.component('left-menu', {
        template: html,
        props: {
            data: {
                type:[Array,String],
                default:function(){
                    return []
                }
            },
            menuOpen: Function,
            defaultActive:String,
            isNarrow: Boolean
        },
        data: function () {
            return {
                screenHeight:document.documentElement.clientHeight - 61,
                rootMenu: this.$el,
                pureData:[],
                selectIndex:''
            }
        },
        mounted:function(){
            var me = this;
            window.addEventListener('resize', function(){
                me.screenHeight=document.documentElement.clientHeight - 61
            });
        },
        created:function(){
            this.pureData = this.convertToMenuData();
        },
        methods:{
            onMenuSelect:function(index){
                var indexPaths = index.split('-');
                var source =  this.pureData;
                var selectPath = '';
                for(var i = 0;i<indexPaths.length;i++){
                    var item = source[indexPaths[i]];
                    if(!item){
                        return selectPath;
                    }

                    selectPath = item.link;

                    if(item.children && item.children.length>0){
                        source = item.children;
                    }
                }
                if (selectPath === "/parking/jump") {
                    location.assign(location.origin + PUBLIC_PATH + selectPath + location.search)
                    return ;
                }
                if(FO.newVersionUrls.indexOf(selectPath)==-1){
                    var re = /access_token=(\w+)/g;
                    var accesstoken = getAccessToken();
                    var search = window.location.search.replace(re, 'access_token='+accesstoken);

                    var newUrl=window.location.pathname.replace('main_v2','main')
                        + search
                        + "#__aurl=" + encodeURIComponent(selectPath);
                    window.location.assign(newUrl);
                }
                else{
                    $.hashAjax.request(selectPath, {});
                }
            },

            convertToMenuData: function(){
                var me = this;
                var pureData = [];
                var dataFromProperty = JSON.parse(this.$options.propsData.data);
                $.each(dataFromProperty, function(index,item){

                    var link = item.value.link;

                    if(FO.isHome() && link !== "/parking/jump"){
                        link = "/chart/dashboard/home";
                    }else {
                        // link = "/chart/dashboard/index_new";
                    }

                    var pureItem = {name:item.value.name, link: link, icon:item.value.icon};
                    if(item.value.subMenus){
                        me.addChildren(pureItem, item.value.subMenus);
                    }
                    pureData.push(pureItem);
                });
                return pureData;
            },

            addChildren:function(parent, subMenus){
                parent.children=[];
                for(var i in subMenus){
                    var item ={
                        name: subMenus[i].name,
                        link: subMenus[i].link,
                        icon: subMenus[i].icon
                    };
                    if(subMenus[i].subMenus){
                        this.addChildren(item, subMenus[i].subMenus);
                    }
                    parent.children.push(item);
                }
            }
        }
    });
});
