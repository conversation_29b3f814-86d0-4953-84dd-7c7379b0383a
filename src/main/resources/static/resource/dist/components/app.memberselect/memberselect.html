<div ref='root'>
    <div class="ms-ctn form-control chose_patrol_people el-input__inner"
         :id="uuid2"
         style="overflow: auto;height: auto;max-height: 200px; padding-top: 3px;padding-bottom: 1px;"
         @click="openDialog">
        <span style="color: #aaa;line-height: 28px;padding-left:3px;">{{placeholder}}</span>
    </div>
    <el-dialog
            :title="title"
            custom-class="member-select-dialog"
            v-model="dialogVisible">
        <form class="box_choose_people">
            <div>
                <div :id="uuid" class="member-select"></div>
            </div>
        </form>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirmSelect">{{confirmText}}</el-button>
            <el-button @click="dialogVisible = false">{{cancelText}}</el-button>
        </span>
    </el-dialog>
</div>