/**
 * member select [required jQuery, morphdom]
 */
(function ($) {
    var Selection = function (options) {
        //this.options = {};
        this.container = options.container;
        this.store = options.store;
        this.itemRender = options.itemRender;

    }

    Selection.prototype.init = function () {
        var me = this;
        this.data=[];
        this.selectionContainer = $("<div class='selection-container'></div>");
        this.selectionInner = $("<div class='selection-container-inner'></div>");

        this.container.append(this.selectionContainer);
        this.selectionContainer.append(this.selectionInner);

        this.selectionContainer.delegate('.select-item-remove', 'click', function () {
            var item = {
                path: $(this).parent().attr('data-path').toString(),
                id: +$(this).parent().attr('data-id'),
                type: +$(this).parent().attr('data-type')
            };

            me.store.deselect(item);
        });
    }

    Selection.prototype.render = function () {
        var me = this;
        //me.selectionInner.children().remove();
        var dom = this.selectionInner;
        var newDOM =$("<div class='selection-container-inner'></div>");

        var selectedNodes = this.store.getCheckNodes();
        $.each(selectedNodes, function (index, item) {
            if (item.selected) {
                var renderText =  item.name;
                if(me.itemRender && typeof me.itemRender === 'function'){
                    renderText = me.itemRender.call(me, item);
                }
                newDOM.append("<div class='select-item' data-type='"+ item['type'] +"' data-id='"+ item.id +"' data-path='" + item.path + "'><span class='select-item-inner'>" + (renderText) + "</span><span class='select-item-remove'>×</span></div>");
            }
        })

        if (selectedNodes.length == 0) {
            newDOM.append("<span>" + XiaI18n("js.memberselecter.notice") + "</span>");
        }

        morphdom(dom[0], newDOM[0]);
    }

    var Path = function (options) {
        //this.options = {};
        this.container = options.container;
        this.store = options.store;
    }

    Path.prototype.init = function () {
        var me = this;
        this.pathContainer = $("<div class='path-container'></div>");
        this.levels = $("<ul class='path-container-inner clearfix'></ul>");
        this.levels.append("<li class='select-path-root path-item' data-level='0'><a href='root'>"+XiaI18n("js.bul002.root.directory")+"</a></li>");
        this.container.append(this.pathContainer);
        this.pathContainer.append(this.levels);

        this.levels.delegate(".path-item a", "click", function (e) {
            e.preventDefault();
            me.store.selectPath($(this).parent().attr("data-level"));
        })
    }

    Path.prototype.render = function () {
        //this.levels.find("li.select-path").remove();
        var dom = this.levels;
        var newDOM =  $("<ul class='path-container-inner clearfix'><li class='select-path-root path-item' data-level='0'><a href='root'>"+XiaI18n("js.bul002.root.directory")+"</a></li></ul>");

        for (var i = 0, ln = this.store.states.pathValues.length; i < ln; i++) {
            var value = this.store.states.pathValues[i];
            if (i == ln - 1) {
                newDOM.append("<li class='select-path path-item' data-level='" + value.level + "'><i class='fa fa-angle-right' aria-hidden='true'></i>" +  $.escapeHtml(value.name) + "</li>")
            }
            else {
                newDOM.append("<li class='select-path path-item' data-level='" + value.level + "'><i class='fa fa-angle-right' aria-hidden='true'></i><a href='" + $.escapeHtml(value.name) + "'>" +  $.escapeHtml(value.name) + "</a></li>")
            }

        }

        if (newDOM.find("li.select-path").length == 0) {
            newDOM.find(".select-path-root a").removeAttr("href");
        }
        else {
            newDOM.find(".select-path-root a").attr("href", "root");
        }

        morphdom(dom[0], newDOM[0]);
    }

    var List = function (options) {
        // this.options = {};
        this.container = options.container;
        this.store = options.store;
    }

    List.prototype.init = function () {
        var me = this,
            containerId = this.container.attr("id");

        this.listContainer = $("<div class='member-list'></div>");
        this.selectAll = $("<div class='member-list-select-all'><input id='" + containerId + "_select_all' class='select-all' type='checkbox' style='display:none'></input><label class='checkboxdiv' for='"+containerId+"_select_all'></label><label for='" + containerId + "_select_all'>" + XiaI18n("js.memberselecter.all") + "</label></div>");
        this.list = $("<div class='member-list-container'><div class='member-list-inner'></div></div>");
        this.container.append(this.listContainer);

        this.listContainer.append(this.selectAll);
        this.listContainer.append(this.list);

        this.selectAll.delegate('.select-all', 'click', function () {
            if ($(this).is(":checked")) {
                me.store.selectAll();
            }
            else {
                me.store.deselectAll();
            }
        });

        this.list.delegate(".member-item>input", 'click', function (e) {
            var item = {
                index: $(this).parent().attr('data-index'),
                level: $(this).parent().attr('data-level'),
                path: $(this).parent().attr('data-path').toString()
            }

            if ($(this).is(":checked")) {
                $(this).parent().addClass("checked");
                me.store.select(item);
            }
            else {
                $(this).parent().removeClass("checked");
                me.store.deselect(item);
            }
        })
        this.list.delegate(".member-item>a.next", 'click', function (e) {
            e.preventDefault();
            var item = {
                index: $(this).parent().attr('data-index'),
                level: $(this).parent().attr('data-level'),
                path: $(this).parent().attr('data-path').toString()
            }
            me.store.nextPath(item);
        })
    }

    List.prototype.render = function () {
        var me = this,
            data = this.store.getCurrentLevelNodes(),
            selectedItemCount = 0;

        var dom = this.listContainer.find('.member-list-inner'); //this.list;
        var newDOM =  $("<div class='member-list-inner'></div>");

        $.each(data, function (index, item) {
            var renderFunc = me.getItemRender(index, item);
            newDOM.append(renderFunc);
            if (item.selected)
                selectedItemCount++;
        });

        if (selectedItemCount > 0 && selectedItemCount == data.length) {
            this.selectAll.find(".select-all").prop("checked", true);
        }
        else {
            this.selectAll.find(".select-all").prop("checked", false);
        }

        morphdom(dom[0], newDOM[0]);
    }

    List.prototype.getItemRender = function (index, item) {
        var checkedTpl = item.selected ? "checked" : "",
            level = this.store.states.currentLevel,
            containerId = this.container.attr("id"),
            itemId = containerId + "." + level + "." + index;

        if (item.isParent) {
            return $("<div class='parent member-item' data-path='" + item.path + "' data-level='" + level + "' data-index='" + index + "'><input id='" + itemId + "' type='checkbox' " + checkedTpl + " style='display:none'></input><label class='checkboxdiv' for='"+itemId+"'></label><label for='" + itemId + "'>" +  $.escapeHtml(item.name) + "</label><a href='next' class='next'>" + XiaI18n('js.memberselecter.in') + "</a></div>");
        }
        else {
            return $("<div class='member member-item' data-path='" + item.path + "' data-level='" + level + "' data-index='" + index + "'><input id='" + itemId + "' type='checkbox' " + checkedTpl + " style='display:none'></input><label class='checkboxdiv' for='"+itemId+"'></label><label for='" + itemId + "'>" + $.escapeHtml(item.name) + "</label></div>");
        }
    }

    var Store = function (memberSelect, options) {
        this.states = {
            data: [],
            currentLevel: 0,
            path: '',
            currentNode: '',
            pathValues: [],
            initValues: []
        };

        this.options = options;
        this.dataKey = options.dataKey,
            this.returnFields= options.returnFields;
        this.memberSelect = memberSelect;
        this.sync = false;
        if (!this.options.ajax && typeof this.options.loadData == 'function') {
            this.loadDataHandler = options.loadData;
        }

        if (options.values) {
            var initValues = [],
                returnFields = this.returnFields,
                dataKey = this.dataKey;

            /*for (var i in options.values) {
             var item = options.values[i];
             $.each(item, function (index, sItem) {
             var valueItem = {};
             for(var m in returnFields){
             var prop = returnFields[m];

             if(sItem.hasOwnProperty(prop)){
             valueItem[prop]= sItem[prop];
             }
             valueItem.parent = i;
             }
             initValues.push(valueItem);
             })
             }*/
            initValues = options.values;
            initValues.sort(function (a, b) {
                if (a[dataKey] < b[dataKey])
                    return -1;
                else {
                    return 1;
                }
            });
            this.states.initValues = initValues;
        }
    }

    Store.prototype.init = function () { }

    Store.prototype.load = function () {
        var me = this,
            dtd = $.Deferred();

        var updateData = function (data) {
            var items = [];
            $.each(data, function (index, item) {
                item.selected = false;
                item.level = me.states.currentLevel;
                item.path = index.toString();
                items.push(item);
            });
            me.states.data = items;
        }

        if (this.options.ajax) {
            this.sync = true;
            this.loadDataHandler().then(function (data) {
                updateData(data);
                dtd.resolve(me.states.data);
                me.memberSelect.render();
            }).fail(function () {
                dtd.reject();
            });
        }
        else if (this.options.data.length > 0) {
            updateData(this.options.data);
            dtd.resolve(me.states.data);
            me.memberSelect.render();
        }

        return dtd.promise();
    }

    Store.prototype.loadDataWrapper = function (index, level) {
        var me = this,
            dtd = $.Deferred(),
            path = this.buildPath(index),
            node = this.findNode(path),
            returnData = [];

        var updateData = function (node, returnData) {
            node.children = [];
            for (var i = 0; i < returnData.length; i++) {
                var child = returnData[i];
                child.selected = node.selected;
                child.path = path + "." + i;

                node.children.push(child);
            }
        }

        if (node.children && node.children.length > 0) {
            returnData = node.children;
            dtd.resolve(returnData);
        }
        else {
            if (this.sync) {
                this.loadDataHandler(node).then(function (data) {
                    returnData = data;
                    updateData(node, returnData);
                    dtd.resolve(returnData);
                }).fail(function () {
                    dtd.reject();
                });
            }
            else {
                returnData = this.loadDataHandler(node);
                updateData(node, returnData);
                dtd.resolve(returnData);
            }
        }



        return dtd.promise();
    }

    Store.prototype.loadDataHandler = function (node) {
        var ajaxConfig = this.options.ajax,
            params = ajaxConfig.params,
            initParams = ajaxConfig.initParams,
            ajaxData = {},
            ajaxOptions = {},
            url = ajaxConfig.url;

        if (node) {
            for (var i = 0; i < params.length; i++) {
                var prop = params[i];
                if (node.hasOwnProperty(prop)) {
                    url = url.replace('{' + prop + '}', node[prop]);
                }
            }
        }
        else {
            for (var i = 0; i < params.length; i++) {
                var prop = params[i];
                url = url.replace('{' + prop + '}', initParams[i]);
            }
        }

        ajaxOptions = {
            url: url,
            //data: ajaxData,
            type: ajaxConfig.method
        };

        if (ajaxConfig.method.toLowerCase() == 'post') {
            ajaxOptions.contentType = 'application/json'
        }

        return $.ajax(ajaxOptions);
    }

    Store.prototype.selectAll = function () {
        var nodes = this.getCurrentLevelNodes();
        this.selectChildren(nodes, true);

        var currentNode = this.findNode(nodes[0].path);
        this.reInitChecked(currentNode);

        this.memberSelect.render();
    }

    Store.prototype.deselectAll = function () {
        var nodes = this.getCurrentLevelNodes();
        this.selectChildren(nodes, false);

        var currentNode = this.findNode(nodes[0].path);
        this.reInitChecked(currentNode);

        this.memberSelect.render();
    }

    Store.prototype.selectChildren = function (items, value) {
        if (!items) return;

        var me = this;
        $.each(items, function (index, item) {
            item.selected = value;
            if (item.isParent && item.children) {
                me.selectChildren(item.children, value);
            }
        });
    }

    Store.prototype.reInitChecked = function (node) {
        var lastIndexOfDot = node.path.lastIndexOf('.');
        var parentPath = node.path.substring(0, lastIndexOfDot);

        var parentNode = this.findNode(parentPath);
        if (parentNode == null)
            return;

        var allSelected = true;
        var noneSelected = true;
        $.each(parentNode.children, function (index, item) {
            if (item.selected) {
                noneSelected = false;
            }
            else {
                allSelected = false;
            }
        });

        if (allSelected) {
            parentNode.selected = true;
        }
        else {
            parentNode.selected = false;
        }
        this.reInitChecked(parentNode);
    }

    Store.prototype.select = function (item) {
        var node = this.findNode(item.path);
        node.selected = true;
        this.selectChildren(node.children, true);
        this.reInitChecked(node);

        this.memberSelect.render();
    }

    Store.prototype.deselect = function (item) {
        var node = this.findNode(item.path);
        if(node){
            node.selected = false;
            this.selectChildren(node.children, false);
            this.reInitChecked(node);
        } else{
            this.removeFromInitValues(item, this.dataKey);
        }

        this.memberSelect.render();
    }

    Store.prototype.selectPath = function (index) {
        this.states.currentLevel = index;
        this.states.pathValues.splice(index);
        if (this.states.pathValues.length > 0) {
            var newPath = [];
            $.each(this.states.pathValues, function (index, item) {
                newPath.push(item.index);
            });
            this.states.path = newPath.join('.');
        }
        else {
            this.states.path = '';
        }

        this.memberSelect.render();
    }

    Store.prototype.nextPath = function (item) {
        var path = item.path,
            me = this;

        this.loadDataWrapper(item.index, item.level).then(function () {
            me.states.path = path;
            me.states.currentNode = me.findNode(me.states.path);
            me.states.currentLevel = parseInt(item.level) + 1;

            me.states.pathValues.push({
                level: me.states.currentLevel,
                name: me.states.currentNode.name,
                index: item.index
            });

            me.memberSelect.render();
        })
    }

    Store.prototype.buildPath = function (index) {
        var path = this.states.path;//.substring(0, this.states.path.lastIndexOf('.'));
        if (path !== '') {
            path = path.split('.');
        } else {
            path = [];
        }
        path.push(index);
        return path.join('.');
    }

    Store.prototype.findNode = function (path) {
        var dataPath = path.split('.'),
            nodes = this.states.data,
            findNode;

        for (var i = 0; i < dataPath.length; i++) {
            findNode = nodes[dataPath[i]];
            if (!findNode)
                return null;

            nodes = $.isArray(findNode) ? findNode : findNode.children;

            if(!nodes && i+1 < dataPath.length){
                return null;
            }
        }

        return findNode;
    }

    Store.prototype.getCurrentLevelNodes = function () {
        if (this.states.currentLevel == 0) {
            return this.states.data;
        }
        else {
            var lastPathValue = this.states.pathValues[this.states.pathValues.length - 1],
                path = this.states.path,
                node = this.findNode(path);

            return node.children;
        }
    }

    Store.prototype.isInInitValues= function(node){
        return this.removeFromInitValues(node, this.dataKey);
    }

    Store.prototype.isParentOfInitValues = function(node){
        if(this.states.initValues.length==0)
            return;

        var find = false,
            index = -1,
            dataKey = this.dataKey;

        for(var ln= this.states.initValues.length, i=ln-1;i>=0; i--){
            var item = this.states.initValues[i];
            if(item.parent=='$')
                continue;

            var parent = item.parent;
            if(+parent === +node[dataKey]){
                this.states.initValues.splice(i,1);
            }
        }
    }

    Store.prototype.removeFromInitValues = function(node,dataKey){
        var find = false,
            index = -1,
            dataKey = dataKey;

        for(var i=0, ln= this.states.initValues.length;i<ln; i++){
            var item = this.states.initValues[i];
            if(item[dataKey] === node[dataKey] && item['type'] === node['type']){ //TODO: should use dataKey
                index = i;
                find = true;
            }
        }

        if(index>-1){
            this.states.initValues.splice(index,1);
        }

        return find;
    }

    Store.prototype.getCheckNodes = function () {
        var me = this;
        var checkedNodes = [];

        var walk = function (node, parent) {
            var childNodes = node ? node.children : me.states.data;
            var parentPath = parent === undefined ? '$' : parent.id;
            if (!childNodes) {
                return;
            }
            $.each(childNodes, function (index, child) {
                child.parent = parentPath;
                if (child.selected) {
                    checkedNodes.push(child);

                    me.isParentOfInitValues(child);
                }
                else {
                    if(me.isInInitValues(child)){
                        child.selected = true;
                        checkedNodes.push(child);
                    }
                    else{
                        walk(child, child.id);
                    }
                }
            });
        };

        walk();

        $.each(this.states.initValues, function(index,item){
            item.selected=true;
            checkedNodes.push(item);
        });

        var dataKey =  this.dataKey;
        checkedNodes.sort(function(a,b){
            if(a[dataKey]<b[dataKey]){
                return -1;
            }
            else{
                return 1;
            }
        });
        return checkedNodes;
    }

    Store.prototype.getCheckNodesInJSONFormat = function () {
        var me = this;
        var checkedNodes = {};
        var dataKey = this.dataKey;
        var returnFields = this.returnFields;

        var walk = function (node, parents) {
            var childNodes = node ? node.children : me.states.data;
            var parentNodes = parents ? parents : [];
            if (!childNodes) {
                return;
            }
            var currentLevelCheckedNodes = [];
            $.each(childNodes, function (index, child) {
                if (child.selected) {
                    currentLevelCheckedNodes.push(child);
                    me.isParentOfInitValues(child);
                }
                else {
                    if(me.isInInitValues(child)){
                        currentLevelCheckedNodes.push(child);
                    }
                    else{
                        var pNodes = $.extend(true, [], parentNodes);
                        pNodes.push(child[dataKey]);
                        walk(child, pNodes);
                    }
                }
            });

            var key='$'
            if(parentNodes.length>0){
                key=parentNodes.join('$');
            }

            $.each(currentLevelCheckedNodes, function(index,item){
                var nodeItem = {};

                for(var i=0, ln= returnFields.length;i<ln;i++){
                    var prop = returnFields[i]

                    if(item.hasOwnProperty(prop)){
                        nodeItem[prop]= item[prop];
                    }
                }
                if(!checkedNodes[key])
                    checkedNodes[key]=[];
                checkedNodes[key].push(nodeItem);
            });
        };

        walk();

        $.each(this.states.initValues, function(index,item){
            var key = item.parent,
                nodeItem = {};

            for(var i=0, ln= returnFields.length;i<ln;i++){
                var prop = returnFields[i]

                if(item.hasOwnProperty(prop)){
                    nodeItem[prop]= item[prop];
                }
            }
            if(!checkedNodes[key])
                checkedNodes[key]=[];
            checkedNodes[key].push(nodeItem);
        });

        return checkedNodes;
    }

    /**
     * options: {ajax, data}
     *
     */
    var MemberSelect = function (container, options) {
        var defaultOptions={
            returnFields:['name'],
            dataKey : 'path',
            selectionRender : null
        };
        defaultOptions = $.extend(true, defaultOptions , options);

        var store = new Store(this, defaultOptions);
        this.returnFields = defaultOptions.returnFields;
        this.dataKey = defaultOptions.dataKey;
        this.container = container;
        this.store = store;
        this.select = new Selection({ container: container, store: store, itemRender: defaultOptions.selectionRender });
        this.path = new Path({ container: container, store: store });
        this.list = new List({ container: container, store: store });

        this.init();
    }

    MemberSelect.prototype.init = function () {

        var me = this;

        this.container.children().remove();

        this.select.init();
        this.path.init();
        this.list.init();

        this.store.load().then(function () {
            me.list.render();
        }).fail(function () {
            me.container.html('component init failed');
        })


        this.container.find(".member-list-inner").slimScroll({
            height: "250px",
            opacity: .3
        });

        this.container.find(".selection-container-inner").slimScroll({
            height: '80px',
            opacity: .3
        });
    }

    MemberSelect.prototype.render = function () {

        this.select.render();
        this.path.render();
        this.list.render();

    }

    MemberSelect.prototype.getValues = function () {
        var values = [];
        var nodes = this.store.getCheckNodes();
        var me = this;
        $.each(nodes, function (index, item) {
            var returnObj = {};
            for (var i = 0; i < me.returnFields.length; i++) {
                var prop = me.returnFields[i];
                if (item.hasOwnProperty(prop)) {
                    returnObj[prop] = item[prop];
                }
            }
            values.push(returnObj);
        });
        return values;
    }

    MemberSelect.prototype.getJSONValues = function () {
        return this.getValues();
        var values = [];
        var nodes = this.store.getCheckNodesInJSONFormat();
        var me = this,
            prop = this.dataKey;

        return nodes;
    }

    var INSTANCES = [];

    function getInstances(key) {
        for (var i = 0, ln = INSTANCES.length; i < ln; i++) {
            if (INSTANCES[i].key === key) {
                return INSTANCES[i].instance;
            }
        }
        return null;
    }

    $.fn.memberSelect = function (options) {
        //var memberSelect= getInstances($(this).data("key"));
        //if(!memberSelect){
        //  var key= "member-select-instance-"+(INSTANCES.length+1);
        var memberSelect = new MemberSelect($(this), options)
        // INSTANCES.push({key:key, instance:memberSelect});
        // }

        return memberSelect;
    }
})(jQuery);
