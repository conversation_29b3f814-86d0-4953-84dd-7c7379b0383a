
.ztree * {padding:0; margin:0; font-size:14px; font-family: "微软雅黑", "Helvetica Neue", Helvetica, Arial, sans-serif}
.ztree,.ztree ul {
	overflow-x: hidden;
	overflow-y: auto;
	position: relative;
    list-style: none;
    padding: 20px 20px 0px 20px;
    
    background: #FFFFFF;
    border-radius: 2px;
}
.ztree:before,.ztree ul:before{
}
.ztree li{
	position: relative;
	cursor: pointer;
	min-height: 20px;
	width: auto;
	white-space: nowrap;
	padding-left: 2px;
}
.ztree li a span:last-child{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  width: 90%;
}
.ztree li:before{
	   -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: #67B2DD;
    border-image: none;
    border-style: dotted;
    border-width: 0 0 0 1px;
    bottom: -6px;
    content: "";
    display: inline-block;
    left: 9px;
    position: absolute;
    top: 22px;
    z-index: 1;
}
.ztree li:last-child:before{
	border:0;
	content:none;
}
.ztree li:last-child  span.button.bottom_docu:before{
    bottom: 10px;
    top: 1px;
}
.ztree li a span.button.ico_docu:before{
    border-top: 1px dotted #67B2DD;
  content: "";
  display: inline-block;
  height: 0;
  left: 11px;
  position: absolute;
  top: 14px;
  width: 14px;
  z-index: 1;
}
.ztree li ul{ 
	margin:0; padding:0 0 0 12px;list-style: none;  margin-left: 8px;  padding-left: 9px;  border: 0;
}
.ztree li a {
	cursor:pointer; color:#333; background-color: transparent;
	text-decoration:none;display: inline-block;width: 100%;height: 25px;padding-top: 4px;
}
.ztree li a:hover {text-decoration:underline;background-color: #f7f3f0;}
.ztree li a.curSelectedNode {color:black; opacity:0.8;background-color: #dff2f8;color: #6398B0;}
.ztree li a.curSelectedNode_Edit {padding-top:0px; background-color:#FFE6B0; color:black; height:16px; border:1px #FFB951 solid; opacity:0.8;}
.ztree li a.tmpTargetNode_inner {padding-top:0px; background-color:#316AC5; color:white; height:16px; border:1px #316AC5 solid;
	opacity:0.8; filter:alpha(opacity=80)}
.ztree li a.tmpTargetNode_prev {}
.ztree li a.tmpTargetNode_next {}
.ztree li a input.rename {height:14px; width:80px; padding:0; margin:0;
	font-size:12px; border:1px #7EC4CC solid; *border:0px}
.ztree li span {line-height:17px; margin-right:5px;color: #666;margin-left: 1px;}
.ztree li span.button {
	display: inline-block;
	font-family: FontAwesome;
	font-style: normal;
	font-weight: normal;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
    vertical-align: inherit;
     *vertical-align:inherit;
    width: 16px;
    height: 16px;
    font-size: 18px;
    color: #375471;
}
.ztree li span.button.chk {
	cursor: auto;
	margin-right: 0;
	margin-left: 5px;
}
.ztree li span.button.chk.checkbox_false_full { }
.ztree li span.button.chk.checkbox_false_full_focus {}
.ztree li span.button.chk.checkbox_false_full:before,
.ztree li span.button.chk.checkbox_false_full_focus:before{
	content: "\f096";
}
.ztree li span.button.chk.checkbox_true_full {}
.ztree li span.button.chk.checkbox_true_full_focus {}
.ztree li span.button.chk.checkbox_true_full:before ,
.ztree li span.button.chk.checkbox_true_full_focus:before{
	content: "\f046";
}
.ztree li span.button.chk.checkbox_false_part {}
.ztree li span.button.chk.checkbox_false_part_focus {}
.ztree li span.button.chk.checkbox_false_part:before,
.ztree li span.button.chk.checkbox_false_part_focus:before{
    content: "\f14a";
}
  
.ztree li span.button.chk.checkbox_false_disable {background-position:0 -56px}
.ztree li span.button.chk.checkbox_true_part {}
.ztree li span.button.chk.checkbox_true_part_focus {}
.ztree li span.button.chk.checkbox_true_part:before,
.ztree li span.button.chk.checkbox_true_part_focus:before{
	content: "\f14a";
}
.ztree li span.button.chk.checkbox_true_disable {background-position:-14px -56px}
.ztree li span.button.chk.radio_false_full {background-position:-28px 0}
.ztree li span.button.chk.radio_false_full_focus {background-position:-28px -14px}
.ztree li span.button.chk.radio_false_part {background-position:-28px -28px}
.ztree li span.button.chk.radio_false_part_focus {background-position:-28px -42px}
.ztree li span.button.chk.radio_false_disable {background-position:-28px -56px}
.ztree li span.button.chk.radio_true_full {background-position:-42px 0}
.ztree li span.button.chk.radio_true_full_focus {background-position:-42px -14px}
.ztree li span.button.chk.radio_true_part {background-position:-42px -28px}
.ztree li span.button.chk.radio_true_part_focus {background-position:-42px -42px}
.ztree li span.button.chk.radio_true_disable {background-position:-42px -56px}
.ztree li span.button.root_open,.ztree li span.button.root_close,
.ztree li span.button.roots_open,.ztree li span.button.roots_close,
.ztree li span.button.center_open,.ztree li span.button.center_close,
.ztree li span.button.bottom_open,.ztree li span.button.bottom_close{
         margin-right: 0;
}
.ztree li span.button.center_open,.ztree li span.button.bottom_open,
.ztree li span.button.roots_open,.ztree li span.button.root_open{
}
.ztree li span.button.center_open:before,.ztree li span.button.bottom_open:before,
.ztree li span.button.roots_open:before,.ztree li span.button.root_open:before{
    content: "\f147";
}
.ztree li span.button.center_close,.ztree li span.button.bottom_close,
.ztree li span.button.roots_close,.ztree li span.button.root_close{
}
.ztree li span.button.center_close:before,.ztree li span.button.bottom_close:before,
.ztree li span.button.roots_close:before,.ztree li span.button.root_close:before{
    content: "\f196";
}
.ztree li span.button.noline_open{background-position:-92px -72px}
.ztree li span.button.noline_close{background-position:-74px -72px}
.ztree li span.button.root_docu,
.ztree li span.button.roots_docu,
.ztree li span.button.center_docu,
.ztree li span.button.bottom_docu,
.ztree li span.button.noline_docu{ 
	margin-right: 0;
}
.ztree li span.button.root_docu:before,
.ztree li span.button.roots_docu:before,
.ztree li span.button.center_docu:before,
.ztree li span.button.bottom_docu:before,
.ztree li span.button.noline_docu:before{ 
      -moz-border-bottom-colors: none;
  -moz-border-left-colors: none;
  -moz-border-right-colors: none;
  -moz-border-top-colors: none;
  border-color: #67B2DD;
  border-image: none;
  border-style: dotted;
  border-width: 0 0 0 1px;
  bottom: 4px;
  content: "";
  display: inline-block;
  left: 9px;
  position: absolute;
  top: 5px;
  z-index: 1;
}
.ztree li span.button.ico_open,
.ztree li span.button.ico_close,
.ztree li span.button.ico_docu{
	display: inline;vertical-align:top; *vertical-align:middle;
}
.ztree li a.curSelectedNode span.button.ico_open,
.ztree li a.curSelectedNode span.button.ico_close,
.ztree li a.curSelectedNode span.button.ico_docu{
	display: inline;vertical-align:top; *vertical-align:middle
}
.ztree li span.button.edit {margin-right:2px; background-position:-110px -48px; vertical-align:top; *vertical-align:middle}
.ztree li span.button.remove {margin-right:2px; background-position:-110px -64px; vertical-align:top; *vertical-align:middle}
.ztree li span.button.ico_loading{margin-right:2px; background:url(./img/loading.gif) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
ul.tmpTargetzTree {background-color:#FFE6B0; opacity:0.8; filter:alpha(opacity=80)}
span.tmpzTreeMove_arrow {width:16px; height:16px; display: inline-block; padding:0; margin:2px 0 0 1px; border:0 none; position:absolute;
	background-color:transparent; background-repeat:no-repeat; background-attachment: scroll;
	background-position:-110px -80px; background-image:url("./img/zTreeStandard.png"); *background-image:url("./img/zTreeStandard.gif")}
ul.ztree.zTreeDragUL {margin:0; padding:0; position:absolute; width:auto; height:auto;overflow:hidden; background-color:#cfcfcf; border:1px #00B83F dotted; opacity:0.8; filter:alpha(opacity=80)}
.zTreeMask {z-index:10000; background-color:#cfcfcf; opacity:0.0; filter:alpha(opacity=0); position:absolute}
