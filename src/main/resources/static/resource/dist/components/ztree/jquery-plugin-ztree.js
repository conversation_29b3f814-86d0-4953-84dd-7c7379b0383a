/**
 * 依赖ztree.js
 */

    var XiaZtree = function(id,options,data){

        this.id = id;

        var setting = {
            view: {
                dblClickExpand: true
            },
            data: {
                simpleData: {
                    enable: true,
                    pIdKey: "pid"
                }
            },
            check: {
                enable: false,
                chkboxType: {'Y': 'ps', 'N': 'ps'},
                chkStyle: 'checkbox'
            }
        };

        $.extend(setting, options);
        var init = function(){
            $.fn.zTree.init($("#"+id), setting, data);

        }
        init();


        var getTree = function(){
            return $.fn.zTree.getZTreeObj(id);
        }
        var getSelectedNode = function(){
            var _tree = getTree();
            if(_tree){
                return  _tree.getSelectedNodes()[0];
            }else{
                return false;
            }
        }
        var getRealCheckedNodes = function(){
            var checkedNodes = getCheckedNodes();
            var seleted = {},_i,realSeleted=[],realShowSelected=[],_result={};
            for( _i in checkedNodes){
                seleted[checkedNodes[_i]["id"]] = checkedNodes[_i];
            }
            //向上删除
            var rmPnodesUnCheckedAll=function(selects,pid){
                if(selects[pid] && !selects[pid]["delete"]){
                    selects[pid]["delete"] = true;
                    selects = rmPnodesUnCheckedAll(selects,selects[pid]["pid"]);
                }
                return selects;
            }

            //判断同级是否全选
            for(_i in checkedNodes){
                if(seleted[checkedNodes[_i]["pid"]] && !seleted[checkedNodes[_i]["pid"]]["delete"]){
                    var broNodes = getTree().getNodesByParam("pid",checkedNodes[_i]["pid"]);
                    var isAll = true,_j;
                    for(_j in broNodes){
                        if(!seleted[broNodes[_j]["id"]]){
                            isAll = false;
                        }
                    }
                    if(!isAll){
                        seleted = rmPnodesUnCheckedAll(seleted,checkedNodes[_i]["pid"]);
                    }
                }
            }
            //deleted
            for(_i in seleted){
                if(!seleted[_i]["delete"]){
                    realSeleted.push(seleted[_i]);
                }
            }
            _result["realSeleted"] = realSeleted;
            //向下筛选
            var rmSubNodesSelected = function(selects,id){
                var broNodes = getTree().getNodesByParam("pid",id);
                if(broNodes.length>0){
                    var _j;
                    for(_j in broNodes){
                        if(selects[broNodes[_j]["id"]]){
                            selects[broNodes[_j]["id"]]["delete"] = true;
                            selects = rmSubNodesSelected(selects,broNodes[_j]["id"]);
                        }
                    }
                }
                return selects;
            }

            //同级全选，只取顶级
            for(_i in seleted){
                if(!seleted[_i]["delete"]){
                    seleted = rmSubNodesSelected(seleted,seleted[_i]["id"]);
                }
            }

            //deleted
            for(_i in seleted){
                if(!seleted[_i]["delete"]){
                    realShowSelected.push(seleted[_i]);
                }
            }
            _result["realShowSelected"] = realShowSelected;
            return _result;
        }
        var getCheckedNodes = function(){
            var _tree = getTree();
            if(_tree){
                return _tree.getCheckedNodes(true);
            }else{
                return false;
            }
        }
        var setNodesChecked = function(nodes){
            var treeObj = getTree();
            if(treeObj){
                treeObj.checkAllNodes(false);
                $(nodes).each(function(idx, e) {
                    var node = treeObj.getNodeByParam("id", e.id);
                    if (node) {
                        treeObj.checkNode(node, true, true, false);
                    }
                });
            }
        }
        var openTree = function(level){
            var tr = $("#"+id);
            var _openTree=function(obj,cla){
                obj.find(".root_close"+cla).click();
                obj.find(".roots_close"+cla).click()
                obj.find(".center_close"+cla).click()
                obj.find(".bottom_close"+cla).click()
            }
            if(level){
                for(var i=0;i<level;i++){
                    levelcss = ".level" + i;
                    _openTree(tr,levelcss);
                }
            }else{
                level = "";
                _openTree(tr,level);
            }
        }
        var getTreeParentNodeText = function(inputText){

            var getParentNodeText = function(cur,text){
                if(cur){
                    if(text){
                        text =   cur.name + " / " + text;
                    }else{
                        text += cur.name;
                    }
                    text = getParentNodeText(cur.getParentNode(),text);
                }
                return text;
            }
            var node = getSelectedNode();
            inputText = getParentNodeText(node,inputText);
            return inputText;
        }
        this.getTree = getTree;
        this.getSelectedNode = getSelectedNode;
        this.getRealCheckedNodes = getRealCheckedNodes;
        this.getCheckedNodes = getCheckedNodes;
        this.setNodesChecked = setNodesChecked;
        this.openTree = openTree;
        this.getTreeParentNodeText = getTreeParentNodeText;
    }
    window.XiaZtree = XiaZtree;
