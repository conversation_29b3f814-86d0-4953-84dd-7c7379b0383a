!function(){function t(e,i,r){var n=t.resolve(e);if(null==n){r=r||e,i=i||"root";var o=new Error('Failed to require "'+r+'" from "'+i+'"');throw o.path=r,o.parent=i,o.require=!0,o}var a=t.modules[n];return a.exports||(a.exports={},a.client=a.component=!0,a.call(this,a.exports,t.relative(n),a)),a.exports}t.modules={},t.aliases={},t.resolve=function(e){"/"===e.charAt(0)&&(e=e.slice(1));for(var i=[e,e+".js",e+".json",e+"/index.js",e+"/index.json"],r=0;r<i.length;r++){var e=i[r];if(t.modules.hasOwnProperty(e))return e;if(t.aliases.hasOwnProperty(e))return t.aliases[e]}},t.normalize=function(t,e){var i=[];if("."!=e.charAt(0))return e;t=t.split("/"),e=e.split("/");for(var r=0;r<e.length;++r)".."==e[r]?t.pop():"."!=e[r]&&""!=e[r]&&i.push(e[r]);return t.concat(i).join("/")},t.register=function(e,i){t.modules[e]=i},t.alias=function(e,i){if(!t.modules.hasOwnProperty(e))throw new Error('Failed to alias "'+e+'", it does not exist');t.aliases[i]=e},t.relative=function(e){function i(t,e){for(var i=t.length;i--;)if(t[i]===e)return i;return-1}function r(i){var n=r.resolve(i);return t(n,e,i)}var n=t.normalize(e,"..");return r.resolve=function(r){var o=r.charAt(0);if("/"==o)return r.slice(1);if("."==o)return t.normalize(n,r);var a=e.split("/"),s=i(a,"deps")+1;return s||(s=0),r=a.slice(0,s+1).join("/")+"/deps/"+r},r.exists=function(e){return t.modules.hasOwnProperty(r.resolve(e))},r},t.register("component-emitter/index.js",function(t,e,i){function r(t){return t?n(t):void 0}function n(t){for(var e in r.prototype)t[e]=r.prototype[e];return t}i.exports=r,r.prototype.on=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks[t]=this._callbacks[t]||[]).push(e),this},r.prototype.once=function(t,e){function i(){r.off(t,i),e.apply(this,arguments)}var r=this;return this._callbacks=this._callbacks||{},e._off=i,this.on(t,i),this},r.prototype.off=r.prototype.removeListener=r.prototype.removeAllListeners=function(t,e){this._callbacks=this._callbacks||{};var i=this._callbacks[t];if(!i)return this;if(1==arguments.length)return delete this._callbacks[t],this;var r=i.indexOf(e._off||e);return~r&&i.splice(r,1),this},r.prototype.emit=function(t){this._callbacks=this._callbacks||{};var e=[].slice.call(arguments,1),i=this._callbacks[t];if(i){i=i.slice(0);for(var r=0,n=i.length;n>r;++r)i[r].apply(this,e)}return this},r.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks[t]||[]},r.prototype.hasListeners=function(t){return!!this.listeners(t).length}}),t.register("dropzone/index.js",function(t,e,i){i.exports=e("./lib/dropzone.js")}),t.register("dropzone/lib/dropzone.js",function(t,e,i){!function(){var t,r,n,o,a,s,l={}.hasOwnProperty,h=function(t,e){function i(){this.constructor=t}for(var r in e)l.call(e,r)&&(t[r]=e[r]);return i.prototype=e.prototype,t.prototype=new i,t.__super__=e.prototype,t},c=[].slice;r="undefined"!=typeof Emitter&&null!==Emitter?Emitter:e("emitter"),a=function(){},t=function(t){function e(t,r){var n,o,a;if(this.element=t,this.version=e.version,this.defaultOptions.previewTemplate=this.defaultOptions.previewTemplate.replace(/\n*/g,""),this.clickableElements=[],this.listeners=[],this.files=[],"string"==typeof this.element&&(this.element=document.querySelector(this.element)),!this.element||null==this.element.nodeType)throw new Error("Invalid dropzone element.");if(this.element.dropzone)throw new Error("Dropzone already attached.");if(e.instances.push(this),t.dropzone=this,n=null!=(a=e.optionsForElement(this.element))?a:{},this.options=i({},this.defaultOptions,n,null!=r?r:{}),this.options.forceFallback||!e.isBrowserSupported())return this.options.fallback.call(this);if(null==this.options.url&&(this.options.url=this.element.getAttribute("action")),!this.options.url)throw new Error("No URL provided.");if(this.options.acceptedFiles&&this.options.acceptedMimeTypes)throw new Error("You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.");this.options.acceptedMimeTypes&&(this.options.acceptedFiles=this.options.acceptedMimeTypes,delete this.options.acceptedMimeTypes),this.options.method=this.options.method.toUpperCase(),(o=this.getExistingFallback())&&o.parentNode&&o.parentNode.removeChild(o),this.previewsContainer=this.options.previewsContainer?e.getElement(this.options.previewsContainer,"previewsContainer"):this.element,this.options.clickable&&(this.clickableElements=this.options.clickable===!0?[this.element]:e.getElements(this.options.clickable,"clickable")),this.init()}var i;return h(e,t),e.prototype.events=["drop","dragstart","dragend","dragenter","dragover","dragleave","selectedfiles","addedfile","removedfile","thumbnail","error","errormultiple","processing","processingmultiple","uploadprogress","totaluploadprogress","sending","sendingmultiple","success","successmultiple","canceled","canceledmultiple","complete","completemultiple","reset","maxfilesexceeded"],e.prototype.defaultOptions={url:null,method:"post",withCredentials:!1,parallelUploads:2,uploadMultiple:!1,maxFilesize:256,paramName:"file",createImageThumbnails:!0,maxThumbnailFilesize:10,thumbnailWidth:100,thumbnailHeight:100,maxFiles:null,params:{},clickable:!0,ignoreHiddenFiles:!0,acceptedFiles:null,acceptedMimeTypes:null,autoProcessQueue:!0,addRemoveLinks:!1,previewsContainer:null,dictDefaultMessage:"Drop files here to upload",dictFallbackMessage:"Your browser does not support drag'n'drop file uploads.",dictFallbackText:"Please use the fallback form below to upload your files like in the olden days.",dictFileTooBig:"File is too big ({{filesize}}MB). Max filesize: {{maxFilesize}}MB.",dictInvalidFileType:"You can't upload files of this type.",dictResponseError:"Server responded with {{statusCode}} code.",dictCancelUpload:"Cancel upload",dictCancelUploadConfirmation:"Are you sure you want to cancel this upload?",dictRemoveFile:"Remove file",dictRemoveFileConfirmation:null,dictMaxFilesExceeded:"You can only upload {{maxFiles}} files.",accept:function(t,e){return e()},init:function(){return a},forceFallback:!1,fallback:function(){var t,i,r,n,o,a;for(this.element.className=""+this.element.className+" dz-browser-not-supported",a=this.element.getElementsByTagName("div"),n=0,o=a.length;o>n;n++)t=a[n],/(^| )dz-message($| )/.test(t.className)&&(i=t,t.className="dz-message");return i||(i=e.createElement('<div class="dz-message"><span></span></div>'),this.element.appendChild(i)),r=i.getElementsByTagName("span")[0],r&&(r.textContent=this.options.dictFallbackMessage),this.element.appendChild(this.getFallbackForm())},resize:function(t){var e,i,r;return e={srcX:0,srcY:0,srcWidth:t.width,srcHeight:t.height},i=t.width/t.height,r=this.options.thumbnailWidth/this.options.thumbnailHeight,t.height<this.options.thumbnailHeight||t.width<this.options.thumbnailWidth?(e.trgHeight=e.srcHeight,e.trgWidth=e.srcWidth):i>r?(e.srcHeight=t.height,e.srcWidth=e.srcHeight*r):(e.srcWidth=t.width,e.srcHeight=e.srcWidth/r),e.srcX=(t.width-e.srcWidth)/2,e.srcY=(t.height-e.srcHeight)/2,e},drop:function(){return this.element.classList.remove("dz-drag-hover")},dragstart:a,dragend:function(){return this.element.classList.remove("dz-drag-hover")},dragenter:function(){return this.element.classList.add("dz-drag-hover")},dragover:function(){return this.element.classList.add("dz-drag-hover")},dragleave:function(){return this.element.classList.remove("dz-drag-hover")},selectedfiles:function(){return this.element===this.previewsContainer?this.element.classList.add("dz-started"):void 0},reset:function(){return this.element.classList.remove("dz-started")},addedfile:function(t){var i=this;return t.previewElement=e.createElement(this.options.previewTemplate),t.previewTemplate=t.previewElement,this.previewsContainer.appendChild(t.previewElement),t.previewElement.querySelector("[data-dz-name]").textContent=t.name,t.previewElement.querySelector("[data-dz-size]").innerHTML=this.filesize(t.size),this.options.addRemoveLinks&&(t._removeLink=e.createElement('<a class="dz-remove" href="javascript:undefined;">'+this.options.dictRemoveFile+"</a>"),t._removeLink.addEventListener("click",function(r){return r.preventDefault(),r.stopPropagation(),t.status===e.UPLOADING?e.confirm(i.options.dictCancelUploadConfirmation,function(){return i.removeFile(t)}):i.options.dictRemoveFileConfirmation?e.confirm(i.options.dictRemoveFileConfirmation,function(){return i.removeFile(t)}):i.removeFile(t)}),t.previewElement.appendChild(t._removeLink)),this._updateMaxFilesReachedClass()},removedfile:function(t){var e;return null!=(e=t.previewElement)&&e.parentNode.removeChild(t.previewElement),this._updateMaxFilesReachedClass()},thumbnail:function(t,e){var i;return t.previewElement.classList.remove("dz-file-preview"),t.previewElement.classList.add("dz-image-preview"),i=t.previewElement.querySelector("[data-dz-thumbnail]"),i.alt=t.name,i.src=e},error:function(t,e){return t.previewElement.classList.add("dz-error"),t.previewElement.querySelector("[data-dz-errormessage]").textContent=e},errormultiple:a,processing:function(t){return t.previewElement.classList.add("dz-processing"),t._removeLink?t._removeLink.textContent=this.options.dictCancelUpload:void 0},processingmultiple:a,uploadprogress:function(t,e){return t.previewElement.querySelector("[data-dz-uploadprogress]").style.width=""+e+"%"},totaluploadprogress:a,sending:a,sendingmultiple:a,success:function(t){return t.previewElement.classList.add("dz-success")},successmultiple:a,canceled:function(t){return this.emit("error",t,"Upload canceled.")},canceledmultiple:a,complete:function(t){return t._removeLink?t._removeLink.textContent=this.options.dictRemoveFile:void 0},completemultiple:a,maxfilesexceeded:a,previewTemplate:'<div class="dz-preview dz-file-preview">\n  <div class="dz-details">\n    <div class="dz-filename"><span data-dz-name></span></div>\n    <div class="dz-size" data-dz-size></div>\n    <img data-dz-thumbnail />\n  </div>\n  <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>\n  <div class="dz-success-mark"><span>✔</span></div>\n  <div class="dz-error-mark"><span>✘</span></div>\n  <div class="dz-error-message"><span data-dz-errormessage></span></div>\n</div>'},i=function(){var t,e,i,r,n,o,a;for(r=arguments[0],i=2<=arguments.length?c.call(arguments,1):[],o=0,a=i.length;a>o;o++){e=i[o];for(t in e)n=e[t],r[t]=n}return r},e.prototype.getAcceptedFiles=function(){var t,e,i,r,n;for(r=this.files,n=[],e=0,i=r.length;i>e;e++)t=r[e],t.accepted&&n.push(t);return n},e.prototype.getRejectedFiles=function(){var t,e,i,r,n;for(r=this.files,n=[],e=0,i=r.length;i>e;e++)t=r[e],t.accepted||n.push(t);return n},e.prototype.getQueuedFiles=function(){var t,i,r,n,o;for(n=this.files,o=[],i=0,r=n.length;r>i;i++)t=n[i],t.status===e.QUEUED&&o.push(t);return o},e.prototype.getUploadingFiles=function(){var t,i,r,n,o;for(n=this.files,o=[],i=0,r=n.length;r>i;i++)t=n[i],t.status===e.UPLOADING&&o.push(t);return o},e.prototype.init=function(){var t,i,r,n,o,a,s,l=this;for("form"===this.element.tagName&&this.element.setAttribute("enctype","multipart/form-data"),this.element.classList.contains("dropzone")&&!this.element.querySelector(".dz-message")&&this.element.appendChild(e.createElement('<div class="dz-default dz-message"><span>'+this.options.dictDefaultMessage+"</span></div>")),this.clickableElements.length&&(r=function(){return l.hiddenFileInput&&document.body.removeChild(l.hiddenFileInput),l.hiddenFileInput=document.createElement("input"),l.hiddenFileInput.setAttribute("type","file"),l.hiddenFileInput.setAttribute("multiple","multiple"),null!=l.options.acceptedFiles&&l.hiddenFileInput.setAttribute("accept",l.options.acceptedFiles),l.hiddenFileInput.style.visibility="hidden",l.hiddenFileInput.style.position="absolute",l.hiddenFileInput.style.top="0",l.hiddenFileInput.style.left="0",l.hiddenFileInput.style.height="0",l.hiddenFileInput.style.width="0",document.body.appendChild(l.hiddenFileInput),l.hiddenFileInput.addEventListener("change",function(){var t;return t=l.hiddenFileInput.files,t.length&&(l.emit("selectedfiles",t),l.handleFiles(t)),r()})},r()),this.URL=null!=(a=window.URL)?a:window.webkitURL,s=this.events,n=0,o=s.length;o>n;n++)t=s[n],this.on(t,this.options[t]);return this.on("uploadprogress",function(){return l.updateTotalUploadProgress()}),this.on("removedfile",function(){return l.updateTotalUploadProgress()}),this.on("canceled",function(t){return l.emit("complete",t)}),i=function(t){return t.stopPropagation(),t.preventDefault?t.preventDefault():t.returnValue=!1},this.listeners=[{element:this.element,events:{dragstart:function(t){return l.emit("dragstart",t)},dragenter:function(t){return i(t),l.emit("dragenter",t)},dragover:function(t){return i(t),l.emit("dragover",t)},dragleave:function(t){return l.emit("dragleave",t)},drop:function(t){return i(t),l.drop(t)},dragend:function(t){return l.emit("dragend",t)}}}],this.clickableElements.forEach(function(t){return l.listeners.push({element:t,events:{click:function(i){return t!==l.element||i.target===l.element||e.elementInside(i.target,l.element.querySelector(".dz-message"))?l.hiddenFileInput.click():void 0}}})}),this.enable(),this.options.init.call(this)},e.prototype.destroy=function(){var t;return this.disable(),this.removeAllFiles(!0),(null!=(t=this.hiddenFileInput)?t.parentNode:void 0)&&(this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput),this.hiddenFileInput=null),delete this.element.dropzone},e.prototype.updateTotalUploadProgress=function(){var t,e,i,r,n,o,a,s;if(r=0,i=0,t=this.getAcceptedFiles(),t.length){for(s=this.getAcceptedFiles(),o=0,a=s.length;a>o;o++)e=s[o],r+=e.upload.bytesSent,i+=e.upload.total;n=100*r/i}else n=100;return this.emit("totaluploadprogress",n,i,r)},e.prototype.getFallbackForm=function(){var t,i,r,n;return(t=this.getExistingFallback())?t:(r='<div class="dz-fallback">',this.options.dictFallbackText&&(r+="<p>"+this.options.dictFallbackText+"</p>"),r+='<input type="file" name="'+this.options.paramName+(this.options.uploadMultiple?"[]":"")+'" '+(this.options.uploadMultiple?'multiple="multiple"':void 0)+' /><button type="submit">Upload!</button></div>',i=e.createElement(r),"FORM"!==this.element.tagName?(n=e.createElement('<form action="'+this.options.url+'" enctype="multipart/form-data" method="'+this.options.method+'"></form>'),n.appendChild(i)):(this.element.setAttribute("enctype","multipart/form-data"),this.element.setAttribute("method",this.options.method)),null!=n?n:i)},e.prototype.getExistingFallback=function(){var t,e,i,r,n,o;for(e=function(t){var e,i,r;for(i=0,r=t.length;r>i;i++)if(e=t[i],/(^| )fallback($| )/.test(e.className))return e},o=["div","form"],r=0,n=o.length;n>r;r++)if(i=o[r],t=e(this.element.getElementsByTagName(i)))return t},e.prototype.setupEventListeners=function(){var t,e,i,r,n,o,a;for(o=this.listeners,a=[],r=0,n=o.length;n>r;r++)t=o[r],a.push(function(){var r,n;r=t.events,n=[];for(e in r)i=r[e],n.push(t.element.addEventListener(e,i,!1));return n}());return a},e.prototype.removeEventListeners=function(){var t,e,i,r,n,o,a;for(o=this.listeners,a=[],r=0,n=o.length;n>r;r++)t=o[r],a.push(function(){var r,n;r=t.events,n=[];for(e in r)i=r[e],n.push(t.element.removeEventListener(e,i,!1));return n}());return a},e.prototype.disable=function(){var t,e,i,r,n;for(this.clickableElements.forEach(function(t){return t.classList.remove("dz-clickable")}),this.removeEventListeners(),r=this.files,n=[],e=0,i=r.length;i>e;e++)t=r[e],n.push(this.cancelUpload(t));return n},e.prototype.enable=function(){return this.clickableElements.forEach(function(t){return t.classList.add("dz-clickable")}),this.setupEventListeners()},e.prototype.filesize=function(t){var e;return t>=1e11?(t/=1e11,e="TB"):t>=1e8?(t/=1e8,e="GB"):t>=1e5?(t/=1e5,e="MB"):t>=100?(t/=100,e="KB"):(t=10*t,e="b"),"<strong>"+Math.round(t)/10+"</strong> "+e},e.prototype._updateMaxFilesReachedClass=function(){return this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?this.element.classList.add("dz-max-files-reached"):this.element.classList.remove("dz-max-files-reached")},e.prototype.drop=function(t){var e,i;t.dataTransfer&&(this.emit("drop",t),e=t.dataTransfer.files,this.emit("selectedfiles",e),e.length&&(i=t.dataTransfer.items,i&&i.length&&(null!=i[0].webkitGetAsEntry||null!=i[0].getAsEntry)?this.handleItems(i):this.handleFiles(e)))},e.prototype.handleFiles=function(t){var e,i,r,n;for(n=[],i=0,r=t.length;r>i;i++)e=t[i],n.push(this.addFile(e));return n},e.prototype.handleItems=function(t){var e,i,r,n;for(r=0,n=t.length;n>r;r++)i=t[r],null!=i.webkitGetAsEntry?(e=i.webkitGetAsEntry(),e.isFile?this.addFile(i.getAsFile()):e.isDirectory&&this.addDirectory(e,e.name)):this.addFile(i.getAsFile())},e.prototype.accept=function(t,i){return t.size>1024*1024*this.options.maxFilesize?i(this.options.dictFileTooBig.replace("{{filesize}}",Math.round(t.size/1024/10.24)/100).replace("{{maxFilesize}}",this.options.maxFilesize)):e.isValidFile(t,this.options.acceptedFiles)?this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(i(this.options.dictMaxFilesExceeded.replace("{{maxFiles}}",this.options.maxFiles)),this.emit("maxfilesexceeded",t)):this.options.accept.call(this,t,i):i(this.options.dictInvalidFileType)},e.prototype.addFile=function(t){var i=this;return t.upload={progress:0,total:t.size,bytesSent:0},this.files.push(t),t.status=e.ADDED,this.emit("addedfile",t),this.options.createImageThumbnails&&t.type.match(/image.*/)&&t.size<=1024*1024*this.options.maxThumbnailFilesize&&this.createThumbnail(t),this.accept(t,function(e){return e?(t.accepted=!1,i._errorProcessing([t],e)):i.enqueueFile(t)})},e.prototype.enqueueFiles=function(t){var e,i,r;for(i=0,r=t.length;r>i;i++)e=t[i],this.enqueueFile(e);return null},e.prototype.enqueueFile=function(t){var i=this;if(t.accepted=!0,t.status!==e.ADDED)throw new Error("This file can't be queued because it has already been processed or was rejected.");return t.status=e.QUEUED,this.options.autoProcessQueue?setTimeout(function(){return i.processQueue()},1):void 0},e.prototype.addDirectory=function(t,e){var i,r,n=this;return i=t.createReader(),r=function(i){var r,o;for(r=0,o=i.length;o>r;r++)t=i[r],t.isFile?t.file(function(t){return n.options.ignoreHiddenFiles&&"."===t.name.substring(0,1)?void 0:(t.fullPath=""+e+"/"+t.name,n.addFile(t))}):t.isDirectory&&n.addDirectory(t,""+e+"/"+t.name)},i.readEntries(r,function(t){return"undefined"!=typeof console&&null!==console?"function"==typeof console.log?console.log(t):void 0:void 0})},e.prototype.removeFile=function(t){return t.status===e.UPLOADING&&this.cancelUpload(t),this.files=s(this.files,t),this.emit("removedfile",t),0===this.files.length?this.emit("reset"):void 0},e.prototype.removeAllFiles=function(t){var i,r,n,o;for(null==t&&(t=!1),o=this.files.slice(),r=0,n=o.length;n>r;r++)i=o[r],(i.status!==e.UPLOADING||t)&&this.removeFile(i);return null},e.prototype.createThumbnail=function(t){var e,i=this;return e=new FileReader,e.onload=function(){var r;return r=new Image,r.onload=function(){var e,n,o,a,s,l,h,c;return t.width=r.width,t.height=r.height,o=i.options.resize.call(i,t),null==o.trgWidth&&(o.trgWidth=i.options.thumbnailWidth),null==o.trgHeight&&(o.trgHeight=i.options.thumbnailHeight),e=document.createElement("canvas"),n=e.getContext("2d"),e.width=o.trgWidth,e.height=o.trgHeight,n.drawImage(r,null!=(s=o.srcX)?s:0,null!=(l=o.srcY)?l:0,o.srcWidth,o.srcHeight,null!=(h=o.trgX)?h:0,null!=(c=o.trgY)?c:0,o.trgWidth,o.trgHeight),a=e.toDataURL("image/png"),i.emit("thumbnail",t,a)},r.src=e.result},e.readAsDataURL(t)},e.prototype.processQueue=function(){var t,e,i,r;if(e=this.options.parallelUploads,i=this.getUploadingFiles().length,t=i,!(i>=e)&&(r=this.getQueuedFiles(),r.length>0)){if(this.options.uploadMultiple)return this.processFiles(r.slice(0,e-i));for(;e>t;){if(!r.length)return;this.processFile(r.shift()),t++}}},e.prototype.processFile=function(t){return this.processFiles([t])},e.prototype.processFiles=function(t){var i,r,n;for(r=0,n=t.length;n>r;r++)i=t[r],i.processing=!0,i.status=e.UPLOADING,this.emit("processing",i);return this.options.uploadMultiple&&this.emit("processingmultiple",t),this.uploadFiles(t)},e.prototype._getFilesWithXhr=function(t){var e,i;return i=function(){var i,r,n,o;for(n=this.files,o=[],i=0,r=n.length;r>i;i++)e=n[i],e.xhr===t&&o.push(e);return o}.call(this)},e.prototype.cancelUpload=function(t){var i,r,n,o,a,s,l;if(t.status===e.UPLOADING){for(r=this._getFilesWithXhr(t.xhr),n=0,a=r.length;a>n;n++)i=r[n],i.status=e.CANCELED;for(t.xhr.abort(),o=0,s=r.length;s>o;o++)i=r[o],this.emit("canceled",i);this.options.uploadMultiple&&this.emit("canceledmultiple",r)}else((l=t.status)===e.ADDED||l===e.QUEUED)&&(t.status=e.CANCELED,this.emit("canceled",t),this.options.uploadMultiple&&this.emit("canceledmultiple",[t]));return this.options.autoProcessQueue?this.processQueue():void 0},e.prototype.uploadFile=function(t){return this.uploadFiles([t])},e.prototype.uploadFiles=function(t){var r,n,o,a,s,l,h,c,u,d,f,p,g,v,m,y,x,b,_,w,k,C,A,S,E,B,T,$=this;for(m=new XMLHttpRequest,y=0,w=t.length;w>y;y++)r=t[y],r.xhr=m;m.open(this.options.method,this.options.url,!0),m.withCredentials=!!this.options.withCredentials,p=null,o=function(){var e,i,n;for(n=[],e=0,i=t.length;i>e;e++)r=t[e],n.push($._errorProcessing(t,p||$.options.dictResponseError.replace("{{statusCode}}",m.status),m));return n},g=function(e){var i,n,o,a,s,l,h,c,u;if(null!=e)for(n=100*e.loaded/e.total,o=0,l=t.length;l>o;o++)r=t[o],r.upload={progress:n,total:e.total,bytesSent:e.loaded};else{for(i=!0,n=100,a=0,h=t.length;h>a;a++)r=t[a],(100!==r.upload.progress||r.upload.bytesSent!==r.upload.total)&&(i=!1),r.upload.progress=n,r.upload.bytesSent=r.upload.total;if(i)return}for(u=[],s=0,c=t.length;c>s;s++)r=t[s],u.push($.emit("uploadprogress",r,n,r.upload.bytesSent));return u},m.onload=function(i){var r;if(t[0].status!==e.CANCELED&&4===m.readyState){if(p=m.responseText,m.getResponseHeader("content-type")&&~m.getResponseHeader("content-type").indexOf("application/json"))try{p=JSON.parse(p)}catch(n){i=n,p="Invalid JSON response from server."}return g(),200<=(r=m.status)&&300>r?$._finished(t,p,i):o()}},m.onerror=function(){return t[0].status!==e.CANCELED?o():void 0},f=null!=(S=m.upload)?S:m,f.onprogress=g,l={Accept:"application/json","Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"},this.options.headers&&i(l,this.options.headers);for(a in l)s=l[a],m.setRequestHeader(a,s);if(n=new FormData,this.options.params){E=this.options.params;for(d in E)v=E[d],n.append(d,v)}for(x=0,k=t.length;k>x;x++)r=t[x],this.emit("sending",r,m,n);if(this.options.uploadMultiple&&this.emit("sendingmultiple",t,m,n),"FORM"===this.element.tagName)for(B=this.element.querySelectorAll("input, textarea, select, button"),b=0,C=B.length;C>b;b++)h=B[b],c=h.getAttribute("name"),u=h.getAttribute("type"),(!u||"checkbox"!==(T=u.toLowerCase())&&"radio"!==T||h.checked)&&n.append(c,h.value);for(_=0,A=t.length;A>_;_++)r=t[_],n.append(""+this.options.paramName+(this.options.uploadMultiple?"[]":""),r,r.name);return m.send(n)},e.prototype._finished=function(t,i,r){var n,o,a;for(o=0,a=t.length;a>o;o++)n=t[o],n.status=e.SUCCESS,this.emit("success",n,i,r),this.emit("complete",n);return this.options.uploadMultiple&&(this.emit("successmultiple",t,i,r),this.emit("completemultiple",t)),this.options.autoProcessQueue?this.processQueue():void 0},e.prototype._errorProcessing=function(t,i,r){var n,o,a;for(o=0,a=t.length;a>o;o++)n=t[o],n.status=e.ERROR,this.emit("error",n,i,r),this.emit("complete",n);return this.options.uploadMultiple&&(this.emit("errormultiple",t,i,r),this.emit("completemultiple",t)),this.options.autoProcessQueue?this.processQueue():void 0},e}(r),t.version="3.7.1",t.options={},t.optionsForElement=function(e){return e.id?t.options[n(e.id)]:void 0},t.instances=[],t.forElement=function(t){if("string"==typeof t&&(t=document.querySelector(t)),null==(null!=t?t.dropzone:void 0))throw new Error("No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.");return t.dropzone},t.autoDiscover=!0,t.discover=function(){var e,i,r,n,o,a;for(document.querySelectorAll?r=document.querySelectorAll(".dropzone"):(r=[],e=function(t){var e,i,n,o;for(o=[],i=0,n=t.length;n>i;i++)e=t[i],/(^| )dropzone($| )/.test(e.className)?o.push(r.push(e)):o.push(void 0);return o},e(document.getElementsByTagName("div")),e(document.getElementsByTagName("form"))),a=[],n=0,o=r.length;o>n;n++)i=r[n],t.optionsForElement(i)!==!1?a.push(new t(i)):a.push(void 0);return a},t.blacklistedBrowsers=[/opera.*Macintosh.*version\/12/i],t.isBrowserSupported=function(){var e,i,r,n,o;if(e=!0,window.File&&window.FileReader&&window.FileList&&window.Blob&&window.FormData&&document.querySelector)if("classList"in document.createElement("a"))for(o=t.blacklistedBrowsers,r=0,n=o.length;n>r;r++)i=o[r],i.test(navigator.userAgent)&&(e=!1);else e=!1;else e=!1;return e},s=function(t,e){var i,r,n,o;for(o=[],r=0,n=t.length;n>r;r++)i=t[r],i!==e&&o.push(i);return o},n=function(t){return t.replace(/[\-_](\w)/g,function(t){return t[1].toUpperCase()})},t.createElement=function(t){var e;return e=document.createElement("div"),e.innerHTML=t,e.childNodes[0]},t.elementInside=function(t,e){if(t===e)return!0;for(;t=t.parentNode;)if(t===e)return!0;return!1},t.getElement=function(t,e){var i;if("string"==typeof t?i=document.querySelector(t):null!=t.nodeType&&(i=t),null==i)throw new Error("Invalid `"+e+"` option provided. Please provide a CSS selector or a plain HTML element.");return i},t.getElements=function(t,e){var i,r,n,o,a,s,l,h;if(t instanceof Array){n=[];try{for(o=0,s=t.length;s>o;o++)r=t[o],n.push(this.getElement(r,e))}catch(c){i=c,n=null}}else if("string"==typeof t)for(n=[],h=document.querySelectorAll(t),a=0,l=h.length;l>a;a++)r=h[a],n.push(r);else null!=t.nodeType&&(n=[t]);if(null==n||!n.length)throw new Error("Invalid `"+e+"` option provided. Please provide a CSS selector, a plain HTML element or a list of those.");return n},t.confirm=function(t,e,i){return window.confirm(t)?e():null!=i?i():void 0},t.isValidFile=function(t,e){var i,r,n,o,a;if(!e)return!0;for(e=e.split(","),r=t.type,i=r.replace(/\/.*$/,""),o=0,a=e.length;a>o;o++)if(n=e[o],n=n.trim(),"."===n.charAt(0)){if(-1!==t.name.indexOf(n,t.name.length-n.length))return!0}else if(/\/\*$/.test(n)){if(i===n.replace(/\/.*$/,""))return!0}else if(r===n)return!0;return!1},"undefined"!=typeof jQuery&&null!==jQuery&&(jQuery.fn.dropzone=function(e){return this.each(function(){return new t(this,e)})}),"undefined"!=typeof i&&null!==i?i.exports=t:window.Dropzone=t,t.ADDED="added",t.QUEUED="queued",t.ACCEPTED=t.QUEUED,t.UPLOADING="uploading",t.PROCESSING=t.UPLOADING,t.CANCELED="canceled",t.ERROR="error",t.SUCCESS="success",o=function(t,e){var i,r,n,o,a,s,l,h,c;if(n=!1,c=!0,r=t.document,h=r.documentElement,i=r.addEventListener?"addEventListener":"attachEvent",l=r.addEventListener?"removeEventListener":"detachEvent",s=r.addEventListener?"":"on",o=function(i){return"readystatechange"!==i.type||"complete"===r.readyState?(("load"===i.type?t:r)[l](s+i.type,o,!1),!n&&(n=!0)?e.call(t,i.type||i):void 0):void 0},a=function(){var t;try{h.doScroll("left")}catch(e){return t=e,setTimeout(a,50),void 0}return o("poll")},"complete"!==r.readyState){if(r.createEventObject&&h.doScroll){try{c=!t.frameElement}catch(u){}c&&a()}return r[i](s+"DOMContentLoaded",o,!1),r[i](s+"readystatechange",o,!1),t[i](s+"load",o,!1)}},t._autoDiscoverFunction=function(){return t.autoDiscover?t.discover():void 0},o(window,t._autoDiscoverFunction)}.call(this)}),t.alias("component-emitter/index.js","dropzone/deps/emitter/index.js"),t.alias("component-emitter/index.js","emitter/index.js"),"object"==typeof exports?module.exports=t("dropzone"):"function"==typeof define&&define.amd?define(function(){return t("dropzone")}):this.Dropzone=t("dropzone")}();