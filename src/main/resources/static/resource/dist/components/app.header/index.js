define(['common', 'core.notice'],function(Common,$, notice){
    var isInstalled = false;
    var vm = null;
    function install(Vue, $){
        if(!isInstalled){
            vm = new Vue({
                el:'#VueElement_Header',
                data:{
                    logoAbbreviatedLogo:null,
                    logoNormalog:null,
                    styleObject:{
                        background:'',
                        width:''
                    },
                    projectVisible:false,
                    notificationVisible:false,
                    avatarVisible:false,
                    notificationCount:null,
                    notifications:[],
                    isNarrow:false,
                    searchProjectText:'', //搜索项目的输入
                },
                watch: {
                    isNarrow: {
                        immediate:true,
                        handler: function(val, oldVal){
                            this.$emit('narrow-changed',val);
                        }
                    }
                },

                methods:{
                    menuNarrowClose:function () {
                        var me = this;
                        me.styleObject.width="50px";
                        if (me.logoAbbreviatedLogo) {
                            me.styleObject.background="url("+PUBLIC_PATH+"/logo/"+ me.logoAbbreviatedLogo + "/img) no-repeat 50% 50%";
                        }else {
                            me.styleObject.background="url("+PUBLIC_PATH+"/resource/css/fmone-v2.1/images/logo-collapse.png) no-repeat 50% 50%";
                        }
                        this.isNarrow = true;
                        this.$emit('narrow-changed',true);
                    },
                    menuNarrow:function(){
                        var me=this;
                            if (!me.isNarrow) {
                                me.styleObject.width="50px";
                                if (me.logoAbbreviatedLogo) {
                                    me.styleObject.background="url("+PUBLIC_PATH+"/logo/"+ me.logoAbbreviatedLogo + "/img) no-repeat 50% 50%";
                                }else {
                                    me.styleObject.background="url("+PUBLIC_PATH+"/resource/css/fmone-v2.1/images/logo-collapse.png) no-repeat 50% 50%";
                                }
                            }else {
                                me.styleObject.width="220px";
                                if (me.logoNormalog) {
                                    me.styleObject.background="url("+PUBLIC_PATH+"/logo/"+ me.logoNormalog+ "/img) no-repeat 30% 50%";
                                }else {
                                    me.styleObject.background="url("+PUBLIC_PATH+"/resource/css/fmone-v2.1/images/logo_bg.png) no-repeat 30% 50%";
                                }
                            }
                        this.isNarrow = !this.isNarrow;
                        $.cookie('mini_sidebar', this.isNarrow?'1':'0', {path:'/'});
                        window.leftMenuChange && window.leftMenuChange()
                    },
                    getNotifications:function(){
                        var me = this;
                        $.ajax({
                            url:"/uc001/messages/unread/3",
                            type:"get",
                            success:function(response){
                                if(response.data){
                                    if(response.data.total){
                                        me.notifications = response.data.list;

                                        $.each(me.notifications,function(index,item){
                                            item.timeAgo = Common.timeago(item.createdDate);
                                        });

                                        me.notificationCount = response.data.total;
                                    }else{
                                        me.notificationCount=null;
                                    }
                                }
                            }
                        });
                    },
                    enterProject:function(e, projectId){
                        e.preventDefault();
                        var me = this;
                        var accesstoken = Common.getAccessToken();

                        var url = location.protocol+"//"+location.host+PUBLIC_PATH;
                        if(projectId){
                            $.ajax({
                                url:"/main/projectCheck/"+projectId,
                                type:"get",
                                success:function(response){
                                    if(response.data){
                                        localStorage.current_project = projectId;
                                        url += "/main/home/"+projectId+"?current_project="+projectId+"&access_token=" + accesstoken;

                                        //百度追踪
                                        _hmt && _hmt.push(['_trackPageview', "/main/home/" + projectId + "?current_project=" + $(this).data("pid")]);

                                        location.assign(url);
                                    }else {
                                        me.$message({
                                            message: XiaI18n('js.project.noAuth'),
                                            type: 'warning'
                                        });
                                    }
                                }
                            });

                        }else{
                            localStorage.current_project = 0;
                            url += "/main_v2/index?access_token=" + accesstoken+"#__aurl=%2Fchart%2Fdashboard%2Findex_new";

                            //百度追踪
                            _hmt && _hmt.push(['_trackPageview', "/main_v2/index"]);
                            location.assign(url);
                        }

                    },
                    readMessage:function(){
                        //Common.openMenu("/pro002",{});
                        this.$emit('open-menu', '/uc002');
                        this.notificationVisible = false;
                    },
                    showPerson:function(e){
                        e.preventDefault();

                        this.$emit('open-menu', '/uc001');
                        this.avatarVisible = false;
                    },
                    exit : function () {
                        $.get("/logout", function (data) {
                            var href = location.href.split("#");
                            href = href[0].split("?")[0];

                            //百度追踪
                            _hmt && _hmt.push(['_trackPageview', href]);

                            window.location.href = href;
                        });
                    },
                    /*
                     * 搜索项目
                     * */
                    searchProject:function () {
                        var reg = new RegExp(this.searchProjectText, 'i');

                        if(window.PROJECT_SEAECH) {
                            var projectList = window.PROJECT_SEAECH;
                            for(var i = 0; i<projectList.length; i++) {
                                var flag = false;
                                for(var j = 0; j<projectList[i].projects.length; j++) {
                                    if(reg.test(projectList[i].projects[j].projectName)) {
                                        flag = true;
                                        $('[data-project-name='+projectList[i].projects[j].projectId +']').show();
                                        $('[data-project-name='+projectList[i].projects[j].projectId +']').parent().show();
                                    } else {
                                        $('[data-project-name='+projectList[i].projects[j].projectId +']').hide();
                                        if(!flag) {
                                            $('[data-project-name='+projectList[i].projects[j].projectId +']').parent().hide();
                                        }
                                    }
                                }
                                if(!flag) {
                                    $('[data-project-name='+projectList[i].projects[0].projectId +']').parent().prev().hide();
                                    //$('[data-group-name='+projectList[i].groupId +']').hide();
                                } else {
                                    $('[data-project-name='+projectList[i].projects[0].projectId +']').parent().prev().show();
                                    //$('[data-group-name='+projectList[i].groupId +']').show();
                                }
                            }
                        }
                    },

                    jump: function() {
                        window.location.href = PUBLIC_PATH + "/space/user/to/jump?access_token=" + getAccessToken();
                    }
                },
                mounted:function(){
                    this.getNotifications();
                    var me=this;
                    if(location.hash.indexOf('__aurl=%2Fchart%2Fdashboard%2Findex_new') > 0) {
                        this.isNarrow = true;
                    } else {
                        this.isNarrow = false;
                    }

                    $.get("/logo/CustomSettings",function (data) {
                            var  data=data.data[0];
                            if (me.isNarrow) {
                                me.styleObject.width="50px";
                                if (data && data.systemAbbreviatedLogo) {
                                    me.styleObject.background="url("+PUBLIC_PATH+"/logo/"+ data.systemAbbreviatedLogo + "/img) no-repeat ";
                                }else {
                                    me.styleObject.background="url("+PUBLIC_PATH+"/resource/css/fmone-v2.1/images/logo-collapse.png) no-repeat 50% 50%";
                                }

                            }
                            else {
                                me.styleObject.width="220px";
                                if (data && data.systemNormalLogo) {
                                    me.styleObject.background="url("+PUBLIC_PATH+"/logo/"+ data.systemNormalLogo + "/img) no-repeat  30% 50%";

                                    // console.log(me. styleObject.background);

                                }else {
                                    me.styleObject.background="url("+PUBLIC_PATH+"/resource/css/fmone-v2.1/images/logo_bg.png) no-repeat 30% 50%";
                                }
                            }


                        // console.log(me. styleObject);
                            if (data && data.systemNormalLogo) {
                                me.logoNormalog=data.systemNormalLogo;
                                // me.styleObject.background="url("+PUBLIC_PATH+"/logo/"+  me.logoNormalog + "/img) no-repeat  30% 50%";

                            }
                            if (data && data.systemAbbreviatedLogo) {
                                me.logoAbbreviatedLogo=data.systemAbbreviatedLogo;
                            }

                    });

                    $.ajax({
                        'url':'/main/header/menuSearch',
                        type:'POST',
                        data:JSON.stringify({searchText:''}),
                        success:function (res) {
                            if(res.data) {
                                window.PROJECT_SEAECH = res.data;
                            }
                        }
                    })
                }
            });
        }
       return vm;
    }

    return { install: install }
});