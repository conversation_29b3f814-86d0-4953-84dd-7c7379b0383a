/**
 * 依赖
 * moment.js
 * daterangepicker.js
 * https://github.com/moment/moment/tree/develop/locale
 */
var XiaDateRange = function(selector,options){
    var locale = XiaDateRange.regional[document.documentElement.lang];
	var defaultParams = {
			showDropdowns:true,
			format: 'YYYY-MM-DD',
			separator:" ~ ",
			locale: locale, 
			ranges: locale.ranges,
	};
	var settings = jQuery.extend({}, defaultParams, options);
	var daterange;
	
	var execute = function(){
		daterange = $(selector).daterangepicker(settings);
	}
	execute();
	
	this.daterange = daterange;
}
window.XiaDateRange = XiaDateRange;
XiaDateRange.regional = {};
XiaDateRange.regional["zh_CN"] = {
		applyLabel: '确定',
        cancelLabel: '取消',
        fromLabel: '从',
        toLabel: '到',
        weekLabel: '周',
        customRangeLabel: '当前时间段',
        daysOfWeek:  '日_一_二_三_四_五_六'.split('_'),
        monthNames: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),
        firstDay: 1,
        ranges:{
        	 '今天': [moment(), moment()],
             '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
             '过去7天': [moment().subtract(6, 'days'), moment()],
             '过去30天': [moment().subtract(29, 'days'), moment()],
             '本月': [moment().startOf('month'), moment().endOf('month')],
             '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
};
XiaDateRange.regional["en_US"] = {
		applyLabel: 'Apply',
        cancelLabel: 'Cancel',
        fromLabel: 'From',
        toLabel: 'To',
        weekLabel: 'W',
        customRangeLabel: 'Custom Range',
        daysOfWeek: 'Su_Mo_Tu_We_Th_Fr_Sa'.split('_'),
        monthNames: 'Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec'.split('_'),
        firstDay: 0,
        ranges:{
       	 	'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
       }
};