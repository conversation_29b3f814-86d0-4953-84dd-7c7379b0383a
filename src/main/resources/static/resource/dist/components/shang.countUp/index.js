/**
 * Created by lean on 2018/2/1.
 */
define(['countUp','vue'],function(CountUp,Vue){
    Vue.component('shang-count-up', {
        template: "<span></span>",
        props: {
            start: {
                type: Number,
                required: false,
                default: 0
            },
            end: {
                type: Number,
                required: true
            },
            decimals: {
                type: Number,
                required: false,
                default: 0
            },
            duration: {
                type: Number,
                required: false,
                default: 2
            },
            options: {
                type: Object,
                required: false
            },
            decimalsType:{
                type: String,
                required: false
            },
            callback: {
                type: Function,
                required: false,
                default: function() {}
            }
        },
        data: function(){
            return {
                instance: null
            };
        },
        computed: {
        },
        watch: {
            end: {
                handler: function(value) {
                    const that = this;
                    if (that.instance && that.instance.update) {
                        that.instance.update(value);
                    }
                },
                deep: false
            }
        },
        methods: {
            init:function() {
                const that = this;
                if (!that.instance) {
                    const dom = that.$el;
                    that.instance = new CountUp(
                        dom,
                        that.start,
                        that.end,
                        that.decimals,
                        that.duration,
                        that.options,
                        that.decimalsType
                    );
                    that.instance.start(function() {
                        that.callback(that.instance);
                    });
                }
            },
            destroy:function() {
                const that = this;
                that.instance = null;
            }
        },
        mounted:function() {
            const that = this;
            // console.log('mounted');
            that.init();
        },

        beforeDestroy:function() {
            const that = this;
            // console.log('beforeDestroy');
            that.destroy();
        },
        // destroyed() {
        // const that = this;
        // console.log('destroyed');
        // },
        start:function(callback) {
            const that = this;
            if (that.instance && that.instance.start) {
                that.instance.start(function() {
                    callback && callback(that.instance);
                });
            }
        },
        pauseResume:function() {
            const that = this;
            if (that.instance && that.instance.pauseResume) {
                that.instance.pauseResume();
            }
        },
        reset:function() {
            const that = this;
            if (that.instance && that.instance.reset) {
                that.instance.reset();
            }
        },
        update:function(newEndVal) {
            const that = this;
            if (that.instance && that.instance.update) {
                that.instance.update(newEndVal);
            }
        }
    });
});
