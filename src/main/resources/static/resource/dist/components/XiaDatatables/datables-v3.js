var XiaTableV3 = function (containerId, options) {
    var dataTable = "";
    var tableId = containerId + "_xia_table";
    var defaultP = {
        "addRows": {
            "isAuto": -1, //default=1,（-1=手动，0=固定）
            "minRows": 1, //default=0，（最少输入行数）
            "maxRows": 1, //default=0，（最大输入行数）
            "isShow": true//实现显示可输入的Input框
        },
        paging:false,
        isRealEdit:false,
        fnRowAddCallback:null,
        fnRowDelCallback:null,
        pageLength:100//默认100条
    };
    var autocompletes = new Array();
    var rowNums=0;
    var onComplete;
    var fuRowCallBack;
    var params = Object.clone(options);
    var settings = jQuery.extend({}, defaultP, options);
    
    /**
     * 创建table
     */
    var create_table = function () {
        /*setTimeout(function () {
            create_init_column();
        }, 300);*/
    	create_init_column();
    }


    var isAddButton = function () {
        if (settings["addRows"]["isAuto"] == -1) {
            var thLength = $("#" + tableId + "").find("thead th").length;
            var obj = $($("#" + tableId + "").find("thead th")[thLength - 1]);
            if (!obj.length) {
                obj = $("#" + tableId + " tbody>tr:last-child >td:last-child");
            }
            var addRid = tableId + '_addRow';
            obj.css("padding-top", "0px");
            obj.css("padding-bottom", "0px");
            obj.html('<button data-bb-handler="success" id="'
                + addRid
                + '" type="button" class="btn" style="float: none;"><i class="fa fa-plus" style="margin:auto;"></i></button>');
            $("#" + addRid).click(function () {
                addRowOp();
            })
        }
    }
    var create_table2 = function () {
        var table = '<table id="' + tableId + '" class="display dataTable no-footer" cellspacing="0" width="100%" role="grid">';
        var title = create_table_title();
        var column = "<tbody></tbody>";
        table += title + column + '</table>';
        $("#" + containerId).html(table);
        create_table();
    }

    var create_init_column = function () {
        if (settings["addRows"]["minRows"] > 0) {
            for (var i = 0; i < settings["addRows"]["minRows"]; i++) {
            	 addRowOp();
            }
        } else {
        	//addRowOp();
        }
        //删除“无数据”
        if (settings["addRows"]["isShow"]) {
        	if(settings["addRows"]['isAuto']==0 && settings["addRows"]['minRows']==0){
        		;
        	}else{
        		$("#" + tableId + " .dataTables_empty").parent().remove();
        	}
        }
    }


    var create_table_title = function () {
        var title = '<thead><tr>';
        if (settings["columns"] != undefined) {
            for (var i = 0; i < settings["columns"].length; i++) {
                if (settings["columns"][i] != undefined && settings["columns"][i].title != undefined) {
                    var width = "";
                    if (settings["columns"][i].width != undefined) {
                        width = "width:" + settings["columns"][i].width + ";";
                    }
                    title += '<th style="text-align: center;' + width + '">' + settings["columns"][i].title + '</th>';
                }
            }
            title += '<th  class="center operation" style="width:70px;padding-top: 0px;padding-bottom: 0px;"></th>';
        }
        title += '</tr></thead>';
        return title;
    }

    //创建行
    var create_table_column_tr = function (_rowNum) {
        var column = '<tr  role="row" data-rownum="'+_rowNum+'" class="'+(_rowNum%2==0?"odd":"even")+'">';
        if (params["columns"] != undefined) {
            for (var i = 0; i < params["columns"].length; i++) {
            	var tdH = buildTdHtml(_rowNum,i);
                column+='<td>'+(tdH?tdH:"&nbsp;")+'</td>';
                //@Aaron remove padding
            	//column+='<td style="  padding: 8px 10px;" >'+(tdH?tdH:"&nbsp;")+'</td>';
            }
            //操作列
            if (settings["addRows"]["isAuto"] == -1) {
            	 column += '<td class="center">';
                 //column += '<td class="center "style="width: 10%;  padding: 8px 10px;">';
                 if (settings.operate != undefined) {
                     column += additionalButton();
                 }
                 column += '<a class=" delete-' + tableId + '-row" style="float:none;" href="javascript:void(0);">'+XiaI18n("js_table_operation_delete")+'</a>';
                 column += '</td>';
            }
        }
        column += '</tr>';

        return column;
    }
    
    var buildTdHtml = function(_rowNum,i,value){
    	var column = "";
    	var title = $('#' + tableId + ' thead th').eq(i).text();
        if (params["columns"][i] != undefined && params["columns"][i].type != undefined) {
        	var _column = params["columns"][i];
        	var _column_type = _column.type;
        	if(value!=undefined && _column.realType){
        		_column_type = _column.realType;
        	}
            if (_column_type == "label"||!_column_type) {
            	column += '';
            } else if(_column_type == "text"){
            	column += '<input  style="width: 100%;border:0;"  placeholder="'+title+'" value="'+(value?value:"")+'"/>';
            }else if(_column_type == "date"){
            	column += '<input  style="width: 100%;border:0; " class="datatable-datetime" value="'+(value?value:"")+'"/>';
            }else if(_column_type == "auto"){
            	var _id = tableId+"_"+_rowNum+"_"+_column.data.replace(".","_");
            	column += '<input  style="width: 100%;border:0;" placeholder="'+title+'" id="'+_id+'"/>';
            }else if(_column_type == "checkbox" &&_column.changeValue){
            	var _html_checkbox = "";
            	for(var key in _column.changeValue){
            		_html_checkbox+="&nbsp;&nbsp;<input value='"+key+"' type='checkbox'  "+(_column.changeValue[key]==value?"checked":"")+"/>&nbsp;"+_column.changeValue[key]+"&nbsp;&nbsp;"
            	}
            	column += _html_checkbox;
            }else if(_column_type == "radio" &&_column.changeValue){
            	var _html_radio = "";
            	for(var key in _column.changeValue){
            		_html_radio+="&nbsp;<label><input value='"+key+"' type='radio'  "+(_column.changeValue[key]==value?"checked":"")+" name='"+_column.data+"."+_rowNum+"'/>&nbsp;"+_column.changeValue[key]+"</label>&nbsp;"
            	}
            	column +=_html_radio;
            }else if(_column_type == "select" &&_column.changeValue){
            	var _html_select = "<select style='width: 100%;'>";
            	for(var key in _column.changeValue){
            		_html_select+="<option value='"+key+"'  "+(_column.changeValue[key]==value?"selected":"")+">"+_column.changeValue[key]+"</option>";
            	}
            	_html_select +="</select>";
            	column += _html_select;
            }else{
            	column += '';
            }
        }
        return column;
    }

    var addRowOp = function () {
    	var _rowNum = $("#" + tableId + " tbody tr").length;
    	//超出行数限制
        if (settings["addRows"]["maxRows"] > 0 && settings["addRows"]["maxRows"] <= _rowNum) {
            return;
        }
        //追加行
        var column = create_table_column_tr(rowNums);
        var newColumn = $(column);
        newColumn.appendTo($("#" + tableId + " tbody"));
        //行删除
        newColumn.find('.delete-' + tableId + '-row').click(function(e){
        	var _nowAllRowNum = $("#" + tableId + " tbody tr").length;
        	if(settings["addRows"]["minRows"]<_nowAllRowNum){
        		delete autocompletes[rowNums];
        		$(this).parent().parent().remove();
        		
        		if(settings["fnRowDelCallback"]){
        			settings["fnRowDelCallback"](newColumn);
        		}
        	}
        	e.preventDefault();
        });
        //日历 autocomplete
        newColumn.find(".datatable-datetime").attr("readonly",true);
        // TODO delete datetime
        var _datenum = -1;
        for(var _i in settings["columns"]){
        	var _column = settings["columns"][_i];
        	if(_column.type=="date"){
        		_datenum++;
        		var _p = {};
        		if(_column.dataDate){
        			_p = _column.dataDate;
        		}
        		newColumn.find(".datatable-datetime").eq(_datenum).datepicker(_p);
        	}else if(_column.type=="auto"){
        		if(_column.dataAuto){
        			if(!autocompletes[rowNums]){
        				autocompletes[rowNums] = {};
        			}
        			_column.dataAuto.hideTrigger=true;
        			var _id = tableId+"_"+rowNums+"_"+_column.data.replace(".","_");
        			autocompletes[rowNums][_column.data] = new XiaAutoComplete("#" +_id, _column.dataAuto);
        		}
        	}
        }
        rowNums++;
        //callback
        if(settings["fnRowAddCallback"]){
        	settings["fnRowAddCallback"](newColumn);
        }
    }

    var additionalButton = function () {
        var html = '';
        if (settings.operate.data != undefined) {
            for (var i = 0; i < settings.operate.data.length; i++) {
                var obj = settings.operate.data[i];
                if (settings.operate.data[i].class.indexOf("delete") == -1) {
                        html += '<a  class=" ' + settings.operate.data[i]["class"] + ' " style="" href="javascript:void(0);">' + settings.operate.data[i].title + '</a>&nbsp;';
                }
            }
        }
        return html;
    }
    var getData = function(){
    	var data = {};
    	var index_old = [],index_edit_old={};
    
    	//新增数据
    	$("#"+tableId+" tbody tr").each(function(row){
    		var rowObj = $(this);
    		var newRow = rowObj.data("rownum");
    		var oldRow = rowObj.data("row");
    		var _rownum = newRow;
    		if(_rownum === undefined){
    			_rownum = oldRow;
    		}
    		if(oldRow!=undefined && settings['isRealEdit']){
    			index_edit_old[row]=oldRow;
    		}
    		if(oldRow!=undefined && !settings['isRealEdit']){
    			index_old.push(oldRow);
    		}else if(newRow!=undefined || settings['isRealEdit']){
        		rowObj.find("td").each(function(col){
    				if(col<settings["columns"].length){
    					if(!data[row]){
    						data[row] = {};
    					}
    					var _column = settings["columns"][col];
    					
    					var _column_type = _column.type;
    					if(oldRow!=undefined &&_column.realType){
    						_column_type = _column.realType;
    					}
    					if(_column_type=="text"||_column_type=="date"){
    						data[row][_column.data] = $(this).find("input").val();
    					}else if(_column_type=="label"){
    						data[row][_column.data] = $(this).text();
    					}else if(_column_type=="auto"){
    						if(autocompletes[""+_rownum] && autocompletes[""+_rownum][_column.data]){
    							data[row][_column.data] = autocompletes[""+_rownum][_column.data].autocomplete.getValue();
    						}else{
    							data[row][_column.data] = $(this).find("input").val();
    						}
    					}else if(_column_type=="checkbox"){
    						var _vals = [];
    						$(this).find("input:checked").each(function(){
    							_vals.push($(this).val());
    						});
    						data[row][_column.data] = _vals;
    					}else if(_column_type=="radio"){
    						data[row][_column.data] = $(this).find("input:checked").val();
    					}else if(_column_type=="select"){
    						data[row][_column.data] = $(this).find("select").val();
    					}
    				}
    			});
    		}
		});
    	//已有数据
    	var ajaSeleted = {};
    	if(index_old.length>0){
    		var ajadata = dataTable.dataTable.ajax.json();
    		for(var _k in index_old){
    			ajaSeleted[_k] = ajadata.data[index_old[_k]];
    		}
    	}
    	if(!$.isEmptyObject(index_edit_old)){//点.多级数据可能有问题
    		var ajadata = dataTable.dataTable.ajax.json();
    		for(var _k in index_edit_old){
    			ajaSeleted[_k] = ajadata.data[index_edit_old[_k]];
    			for(var _k2 in data[_k]){
    				ajaSeleted[_k][_k2] = data[_k][_k2];
    			}
    			delete data[_k];
    		}
    	}
    	
    	//empty 
    	if(!$.isEmptyObject(data)){
    		var _ks = [];
    		for(var _k in data){
    			if(!$.isEmptyObject(data[_k])){
    				var isempty = true;
    				for(_k2 in data[_k]){
    				     if(typeof(data[_k][_k2]) == "object"){
    	        			isempty = $.isEmptyObject(data[_k][_k2]);
    	        		}else{
    	        			if($.trim(data[_k][_k2])){
    	        				isempty = false;
    	        			}
    	        			if($.trim(data[_k][_k2]) ==="0"){
    	        				isempty = false;
    	        			}
    	        		}
    					if(!isempty){
    						break;
    					}
    				}
    				if(isempty){
    					_ks.push(_k);
    				}
    			}
        	}
    		if(!$.isEmptyObject(_ks)){
    			for(var _k in _ks){
    				delete data[_ks[_k]];
    			}
    		}
    	}
    	
    	return {"newData":data,"ajaxData":ajaSeleted};
    }
    
    var executeV3 = function (oncomplete) {
    	//先执行原有的
    	if (onComplete) {
        	onComplete();
        }
    	
    	rowNums = $("#"+tableId).find("tbody>tr").length;
        // 添加初始空白行
        if (settings["addRows"]["isShow"]) {
            create_table();

            //添加行按钮
            isAddButton();

            if (settings["onLoad"] != undefined) {
                settings["onLoad"]();
            }
        }
        //处理radio设置checked不选中问题
        if (settings["isRealEdit"]) {
        	$("#"+tableId).find("input[checked]").prop("checked",true);
        }
    }
    var executeV3NoRepository = function () {
        //添加初始空白行
        create_table2();

        //添加行按钮
        isAddButton();

        if (settings["onLoad"] != undefined) {
            settings["onLoad"]();
        }
        if (onComplete) {
        	onComplete();
        }
    }
    var myfuRowCallBack = function(row,data){
    	if (settings["columns"] != undefined) {
    		var rowObj = $(row).find("td");
            for (var i = 0; i < settings["columns"].length; i++) {
            	var value = rowObj.eq(i).text();
            	var _rowNum = "server_"+$(row).data("row");
            	var tdH = buildTdHtml(_rowNum,i,$.trim(value));
            	if($.trim(tdH)){
            		//auto
            		rowObj.eq(i).html(tdH);
            		if(settings["columns"][i].type == "auto" ){
            			// TODO 有时间再写
            		}
            	}
            }
    	}
    	if(fuRowCallBack){
    		fuRowCallBack(row,data);
    	}
    }

    var execute = function () {
        if (XiaTableV2 && (settings["repository"] != undefined || settings["ajax"] != undefined)) {
        	
            onComplete = settings["onComplete"];
            settings["onComplete"] = executeV3;
            //设置可编辑
            if(settings['isRealEdit']){
            	fuRowCallBack = settings["fnRowCallback"];
            	settings["fnRowCallback"] = myfuRowCallBack;
            }
            
            if (settings.operate == undefined && settings["addRows"]["isAuto"] == -1) {
                if (settings.operate != undefined) {
                    settings["operate"] = settings.operate;
                } else {
                    settings["operate"] = {
                        "title": "操作",
                        "data": [],
                        "width":"50px"
                    };
                }
            }

            dataTable = new XiaTableV2(containerId, settings);
            
        } else {
            executeV3NoRepository();
        }
    }

    execute();

    this.autocompletes = autocompletes;
    this.dataTable = dataTable;
    this.getData = getData;
}
window.XiaTableV3 = XiaTableV3;

//make flat object to normal object
// a = {'b.c.d':1,'b.c.e':2} ==> a = {b:{c:{d:1,e:2}}}
XiaTableV3.buildObject = function (obj) {
    if (!obj) {
        return obj;
    }
    var result = {};
    for (var i in obj) {
        var properties = i.split('.');
        if (properties&&properties.length>=2) {
            var length = properties.length;
            var temp = {};
            for (var j in properties) {
                var propertyName = properties[j];
                //first one
                if (j == 0) {
                    //init temp object
                    temp = result[propertyName];
                    if (!temp) {
                        result[propertyName] = {};
                        temp = result[propertyName];
                    }
                } else if (j == length - 1) {
                    temp[propertyName] = obj[i];
                } else {
                    //keep relation with parents
                    var parent = null;
                    for (var n = 0; n < j; n++) {
                        if (n == 0) {
                            parent = result[properties[n]];
                        } else {
                            parent = parent[properties[n]];
                        }
                    }
                    //if parent not exist, there should be an error
                    if (parent) {
                        //re-init temp
                        temp = parent[propertyName];
                        if (!temp) {
                            parent[propertyName] = {};
                            temp = parent[propertyName];
                        }
                    }
                }
            }
        } else {
            result[i] = obj[i];
        }
    }

    return result;
}
