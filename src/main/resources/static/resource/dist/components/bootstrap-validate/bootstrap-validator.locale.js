/**
 * Created by will.song on 2016/11/2.
 */
define(['jquery'],function($){
    var en_usLocale = {
        base64: {
            'default': 'Please enter a valid base 64 encoded'
        },
        between: {
            'default': 'Please enter a value between %s and %s',
            notInclusive: 'Please enter a value between %s and %s strictly'
        },
        callback: {
            'default': 'Please enter a valid value'
        },
        choice: {
            'default': 'Please enter a valid value',
            less: 'Please choose %s options at minimum',
            more: 'Please choose %s options at maximum',
            between: 'Please choose %s - %s options'
        },
        color: {
            'default': 'Please enter a valid color'
        },
        creditCard: {
            'default': 'Please enter a valid credit card number'
        },
        cusip: {
            'default': 'Please enter a valid CUSIP number'
        },
        cvv: {
            'default': 'Please enter a valid CVV number'
        },
        date: {
            'default': 'Please enter a valid date',
            min: 'Please enter a date after %s',
            max: 'Please enter a date before %s',
            range: 'Please enter a date in the range %s - %s'
        },
        different: {
            'default': 'Please enter a different value'
        },
        digits: {
            'default': 'Please enter only digits'
        },
        ean: {
            'default': 'Please enter a valid EAN number'
        },
        emailAddress: {
            'default': 'Please enter a valid email address'
        },
        file: {
            'default': 'Please choose a valid file'
        },
        greaterThan: {
            'default': 'Please enter a value greater than or equal to %s',
            notInclusive: 'Please enter a value greater than %s'
        },
        grid: {
            'default': 'Please enter a valid GRId number'
        },
        hex: {
            'default': 'Please enter a valid hexadecimal number'
        },
        hexColor: {
            'default': 'Please enter a valid hex color'
        },
        iban: {
            'default': 'Please enter a valid IBAN number',
            countryNotSupported: 'The country code %s is not supported',
            country: 'Please enter a valid IBAN number in %s',
            countries: {
                AD: 'Andorra',
                AE: 'United Arab Emirates',
                AL: 'Albania',
                AO: 'Angola',
                AT: 'Austria',
                AZ: 'Azerbaijan',
                BA: 'Bosnia and Herzegovina',
                BE: 'Belgium',
                BF: 'Burkina Faso',
                BG: 'Bulgaria',
                BH: 'Bahrain',
                BI: 'Burundi',
                BJ: 'Benin',
                BR: 'Brazil',
                CH: 'Switzerland',
                CI: 'Ivory Coast',
                CM: 'Cameroon',
                CR: 'Costa Rica',
                CV: 'Cape Verde',
                CY: 'Cyprus',
                CZ: 'Czech Republic',
                DE: 'Germany',
                DK: 'Denmark',
                DO: 'Dominican Republic',
                DZ: 'Algeria',
                EE: 'Estonia',
                ES: 'Spain',
                FI: 'Finland',
                FO: 'Faroe Islands',
                FR: 'France',
                GB: 'United Kingdom',
                GE: 'Georgia',
                GI: 'Gibraltar',
                GL: 'Greenland',
                GR: 'Greece',
                GT: 'Guatemala',
                HR: 'Croatia',
                HU: 'Hungary',
                IE: 'Ireland',
                IL: 'Israel',
                IR: 'Iran',
                IS: 'Iceland',
                IT: 'Italy',
                JO: 'Jordan',
                KW: 'Kuwait',
                KZ: 'Kazakhstan',
                LB: 'Lebanon',
                LI: 'Liechtenstein',
                LT: 'Lithuania',
                LU: 'Luxembourg',
                LV: 'Latvia',
                MC: 'Monaco',
                MD: 'Moldova',
                ME: 'Montenegro',
                MG: 'Madagascar',
                MK: 'Macedonia',
                ML: 'Mali',
                MR: 'Mauritania',
                MT: 'Malta',
                MU: 'Mauritius',
                MZ: 'Mozambique',
                NL: 'Netherlands',
                NO: 'Norway',
                PK: 'Pakistan',
                PL: 'Poland',
                PS: 'Palestine',
                PT: 'Portugal',
                QA: 'Qatar',
                RO: 'Romania',
                RS: 'Serbia',
                SA: 'Saudi Arabia',
                SE: 'Sweden',
                SI: 'Slovenia',
                SK: 'Slovakia',
                SM: 'San Marino',
                SN: 'Senegal',
                TN: 'Tunisia',
                TR: 'Turkey',
                VG: 'Virgin Islands, British'
            }
        },
        id: {
            'default': 'Please enter a valid identification number',
            countryNotSupported: 'The country code %s is not supported',
            country: 'Please enter a valid identification number in %s',
            countries: {
                BA: 'Bosnia and Herzegovina',
                BG: 'Bulgaria',
                BR: 'Brazil',
                CH: 'Switzerland',
                CL: 'Chile',
                CN: 'China',
                CZ: 'Czech Republic',
                DK: 'Denmark',
                EE: 'Estonia',
                ES: 'Spain',
                FI: 'Finland',
                HR: 'Croatia',
                IE: 'Ireland',
                IS: 'Iceland',
                LT: 'Lithuania',
                LV: 'Latvia',
                ME: 'Montenegro',
                MK: 'Macedonia',
                NL: 'Netherlands',
                RO: 'Romania',
                RS: 'Serbia',
                SE: 'Sweden',
                SI: 'Slovenia',
                SK: 'Slovakia',
                SM: 'San Marino',
                TH: 'Thailand',
                ZA: 'South Africa'
            }
        },
        identical: {
            'default': 'Please enter the same value'
        },
        imei: {
            'default': 'Please enter a valid IMEI number'
        },
        imo: {
            'default': 'Please enter a valid IMO number'
        },
        integer: {
            'default': 'Please enter a valid number'
        },
        ip: {
            'default': 'Please enter a valid IP address',
            ipv4: 'Please enter a valid IPv4 address',
            ipv6: 'Please enter a valid IPv6 address'
        },
        isbn: {
            'default': 'Please enter a valid ISBN number'
        },
        isin: {
            'default': 'Please enter a valid ISIN number'
        },
        ismn: {
            'default': 'Please enter a valid ISMN number'
        },
        issn: {
            'default': 'Please enter a valid ISSN number'
        },
        lessThan: {
            'default': 'Please enter a value less than or equal to %s',
            notInclusive: 'Please enter a value less than %s'
        },
        mac: {
            'default': 'Please enter a valid MAC address'
        },
        meid: {
            'default': 'Please enter a valid MEID number'
        },
        notEmpty: {
            'default': 'Please enter a value'
        },
        numeric: {
            'default': 'Please enter a valid float number'
        },
        phone: {
            'default': 'Please enter a valid phone number',
            countryNotSupported: 'The country code %s is not supported',
            country: 'Please enter a valid phone number in %s',
            countries: {
                BR: 'Brazil',
                CN: 'China',
                CZ: 'Czech Republic',
                DE: 'Germany',
                DK: 'Denmark',
                ES: 'Spain',
                FR: 'France',
                GB: 'United Kingdom',
                MA: 'Morocco',
                PK: 'Pakistan',
                RO: 'Romania',
                RU: 'Russia',
                SK: 'Slovakia',
                TH: 'Thailand',
                US: 'USA',
                VE: 'Venezuela'
            }
        },
        regexp: {
            'default': 'Please enter a value matching the pattern'
        },
        remote: {
            'default': 'Please enter a valid value'
        },
        rtn: {
            'default': 'Please enter a valid RTN number'
        },
        sedol: {
            'default': 'Please enter a valid SEDOL number'
        },
        siren: {
            'default': 'Please enter a valid SIREN number'
        },
        siret: {
            'default': 'Please enter a valid SIRET number'
        },
        step: {
            'default': 'Please enter a valid step of %s'
        },
        stringCase: {
            'default': 'Please enter only lowercase characters',
            upper: 'Please enter only uppercase characters'
        },
        stringLength: {
            'default': 'Please enter a value with valid length',
            less: 'Please enter less than %s characters',
            more: 'Please enter more than %s characters',
            between: 'Please enter value between %s and %s characters long'
        },
        uri: {
            'default': 'Please enter a valid URI'
        },
        uuid: {
            'default': 'Please enter a valid UUID number',
            version: 'Please enter a valid UUID version %s number'
        },
        vat: {
            'default': 'Please enter a valid VAT number',
            countryNotSupported: 'The country code %s is not supported',
            country: 'Please enter a valid VAT number in %s',
            countries: {
                AT: 'Austria',
                BE: 'Belgium',
                BG: 'Bulgaria',
                BR: 'Brazil',
                CH: 'Switzerland',
                CY: 'Cyprus',
                CZ: 'Czech Republic',
                DE: 'Germany',
                DK: 'Denmark',
                EE: 'Estonia',
                ES: 'Spain',
                FI: 'Finland',
                FR: 'France',
                GB: 'United Kingdom',
                GR: 'Greece',
                EL: 'Greece',
                HU: 'Hungary',
                HR: 'Croatia',
                IE: 'Ireland',
                IS: 'Iceland',
                IT: 'Italy',
                LT: 'Lithuania',
                LU: 'Luxembourg',
                LV: 'Latvia',
                MT: 'Malta',
                NL: 'Netherlands',
                NO: 'Norway',
                PL: 'Poland',
                PT: 'Portugal',
                RO: 'Romania',
                RU: 'Russia',
                RS: 'Serbia',
                SE: 'Sweden',
                SI: 'Slovenia',
                SK: 'Slovakia',
                VE: 'Venezuela',
                ZA: 'South Africa'
            }
        },
        vin: {
            'default': 'Please enter a valid VIN number'
        },
        zipCode: {
            'default': 'Please enter a valid postal code',
            countryNotSupported: 'The country code %s is not supported',
            country: 'Please enter a valid postal code in %s',
            countries: {
                AT: 'Austria',
                BR: 'Brazil',
                CA: 'Canada',
                CH: 'Switzerland',
                CZ: 'Czech Republic',
                DE: 'Germany',
                DK: 'Denmark',
                FR: 'France',
                GB: 'United Kingdom',
                IE: 'Ireland',
                IT: 'Italy',
                MA: 'Morocco',
                NL: 'Netherlands',
                PT: 'Portugal',
                RO: 'Romania',
                RU: 'Russia',
                SE: 'Sweden',
                SG: 'Singapore',
                SK: 'Slovakia',
                US: 'USA'
            }
        }
    };
    var zh_cnLocale = {
        base64: {
            'default': '请输入有效的Base64编码'
        },
        between: {
            'default': '请输入在 %s 和 %s 之间的数值',
            notInclusive: '请输入在 %s 和 %s 之间(不含两端)的数值'
        },
        callback: {
            'default': '请输入有效的值'
        },
        choice: {
            'default': '请输入有效的值',
            less: '请至少选中 %s 个选项',
            more: '最多只能选中 %s 个选项',
            between: '请选择 %s 至 %s 个选项'
        },
        creditCard: {
            'default': '请输入有效的信用卡号码'
        },
        cusip: {
            'default': '请输入有效的美国CUSIP代码'
        },
        cvv: {
            'default': '请输入有效的CVV代码'
        },
        date: {
            'default': '请输入有效的日期'
        },
        different: {
            'default': '请输入不同的值'
        },
        digits: {
            'default': '请输入有效的数字'
        },
        ean: {
            'default': '请输入有效的EAN商品编码'
        },
        emailAddress: {
            'default': '请输入有效的邮件地址'
        },
        file: {
            'default': '请选择有效的文件'
        },
        greaterThan: {
            'default': '请输入大于等于 %s 的数值',
            notInclusive: '请输入大于 %s 的数值'
        },
        grid: {
            'default': '请输入有效的GRId编码'
        },
        hex: {
            'default': '请输入有效的16进制数'
        },
        hexColor: {
            'default': '请输入有效的16进制颜色值'
        },
        iban: {
            'default': '请输入有效的IBAN(国际银行账户)号码',
            countryNotSupported: '不支持 %s 国家或地区',
            country: '请输入有效的 %s 国家或地区的IBAN(国际银行账户)号码',
            countries: {
                AD: '安道​​尔',
                AE: '阿联酋',
                AL: '阿尔巴尼亚',
                AO: '安哥拉',
                AT: '奥地利',
                AZ: '阿塞拜疆',
                BA: '波斯尼亚和黑塞哥维那',
                BE: '比利时',
                BF: '布基纳法索',
                BG: '保加利亚',
                BH: '巴林',
                BI: '布隆迪',
                BJ: '贝宁',
                BR: '巴西',
                CH: '瑞士',
                CI: '科特迪瓦',
                CM: '喀麦隆',
                CR: '哥斯达黎加',
                CV: '佛得角',
                CY: '塞浦路斯',
                CZ: '捷克共和国',
                DE: '德国',
                DK: '丹麦',
                DO: '多米尼加共和国',
                DZ: '阿尔及利亚',
                EE: '爱沙尼亚',
                ES: '西班牙',
                FI: '芬兰',
                FO: '法罗群岛',
                FR: '法国',
                GB: '英国',
                GE: '格鲁吉亚',
                GI: '直布罗陀',
                GL: '格陵兰岛',
                GR: '希腊',
                GT: '危地马拉',
                HR: '克罗地亚',
                HU: '匈牙利',
                IE: '爱尔兰',
                IL: '以色列',
                IR: '伊朗',
                IS: '冰岛',
                IT: '意大利',
                JO: '约旦',
                KW: '科威特',
                KZ: '哈萨克斯坦',
                LB: '黎巴嫩',
                LI: '列支敦士登',
                LT: '立陶宛',
                LU: '卢森堡',
                LV: '拉脱维亚',
                MC: '摩纳哥',
                MD: '摩尔多瓦',
                ME: '黑山',
                MG: '马达加斯加',
                MK: '马其顿',
                ML: '马里',
                MR: '毛里塔尼亚',
                MT: '马耳他',
                MU: '毛里求斯',
                MZ: '莫桑比克',
                NL: '荷兰',
                NO: '挪威',
                PK: '巴基斯坦',
                PL: '波兰',
                PS: '巴勒斯坦',
                PT: '葡萄牙',
                QA: '卡塔尔',
                RO: '罗马尼亚',
                RS: '塞尔维亚',
                SA: '沙特阿拉伯',
                SE: '瑞典',
                SI: '斯洛文尼亚',
                SK: '斯洛伐克',
                SM: '圣马力诺',
                SN: '塞内加尔',
                TN: '突尼斯',
                TR: '土耳其',
                VG: '英属维尔京群岛'
            }
        },
        id: {
            'default': '请输入有效的身份证件号码',
            countryNotSupported: '不支持 %s 国家或地区',
            country: '请输入有效的 %s 国家或地区的身份证件号码',
            countries: {
                BA: '波黑',
                BG: '保加利亚',
                BR: '巴西',
                CH: '瑞士',
                CL: '智利',
                CN: '中国',
                CZ: '捷克',
                DK: '丹麦',
                EE: '爱沙尼亚',
                ES: '西班牙',
                FI: '芬兰',
                HR: '克罗地亚',
                IE: '爱尔兰',
                IS: '冰岛',
                LT: '立陶宛',
                LV: '拉脱维亚',
                ME: '黑山',
                MK: '马其顿',
                NL: '荷兰',
                RO: '罗马尼亚',
                RS: '塞尔维亚',
                SE: '瑞典',
                SI: '斯洛文尼亚',
                SK: '斯洛伐克',
                SM: '圣马力诺',
                TH: '泰国',
                ZA: '南非'
            }
        },
        identical: {
            'default': '请输入相同的值'
        },
        imei: {
            'default': '请输入有效的IMEI(手机串号)'
        },
        imo: {
            'default': '请输入有效的国际海事组织(IMO)号码'
        },
        integer: {
            'default': '请输入有效的整数值'
        },
        ip: {
            'default': '请输入有效的IP地址',
            ipv4: '请输入有效的IPv4地址',
            ipv6: '请输入有效的IPv6地址'
        },
        isbn: {
            'default': '请输入有效的ISBN(国际标准书号)'
        },
        isin: {
            'default': '请输入有效的ISIN(国际证券编码)'
        },
        ismn: {
            'default': '请输入有效的ISMN(印刷音乐作品编码)'
        },
        issn: {
            'default': '请输入有效的ISSN(国际标准杂志书号)'
        },
        lessThan: {
            'default': '请输入小于等于 %s 的数值',
            notInclusive: '请输入小于 %s 的数值'
        },
        mac: {
            'default': '请输入有效的MAC物理地址'
        },
        meid: {
            'default': '请输入有效的MEID(移动设备识别码)'
        },
        notEmpty: {
            'default': '请填写必填项目'
        },
        numeric: {
            'default': '请输入有效的数值，允许小数'
        },
        phone: {
            'default': '请输入有效的电话号码',
            countryNotSupported: '不支持 %s 国家或地区',
            country: '请输入有效的 %s 国家或地区的电话号码',
            countries: {
                BR: '巴西',
                CN: '中国',
                DK: '丹麦',
                ES: '西班牙',
                FR: '法国',
                GB: '英国',
                MA: '摩洛哥',
                PK: '巴基斯坦',
                RO: '罗马尼亚',
                RU: '俄罗斯',
                TH: '泰国',
                US: '美国',
                VE: '委内瑞拉'
            }
        },
        regexp: {
            'default': '请输入符合正则表达式限制的值'
        },
        remote: {
            'default': '请输入有效的值'
        },
        rtn: {
            'default': '请输入有效的RTN号码'
        },
        sedol: {
            'default': '请输入有效的SEDOL代码'
        },
        siren: {
            'default': '请输入有效的SIREN号码'
        },
        siret: {
            'default': '请输入有效的SIRET号码'
        },
        step: {
            'default': '请输入在基础值上，增加 %s 的整数倍的数值'
        },
        stringCase: {
            'default': '只能输入小写字母',
            upper: '只能输入大写字母'
        },
        stringLength: {
            'default': '请输入符合长度限制的值',
            less: '最多只能输入 %s 个字符',
            more: '需要输入至少 %s 个字符',
            between: '请输入 %s 至 %s 个字符'
        },
        uri: {
            'default': '请输入一个有效的URL地址'
        },
        uuid: {
            'default': '请输入有效的UUID',
            version: '请输入版本 %s 的UUID'
        },
        vat: {
            'default': '请输入有效的VAT(税号)',
            countryNotSupported: '不支持 %s 国家或地区',
            country: '请输入有效的 %s 国家或地区的VAT(税号)',
            countries: {
                AT: '奥地利',
                BE: '比利时',
                BG: '保加利亚',
                BR: '巴西',
                CH: '瑞士',
                CY: '塞浦路斯',
                CZ: '捷克',
                DE: '德国',
                DK: '丹麦',
                EE: '爱沙尼亚',
                ES: '西班牙',
                FI: '芬兰',
                FR: '法语',
                GB: '英国',
                GR: '希腊',
                EL: '希腊',
                HU: '匈牙利',
                HR: '克罗地亚',
                IE: '爱尔兰',
                IS: '冰岛',
                IT: '意大利',
                LT: '立陶宛',
                LU: '卢森堡',
                LV: '拉脱维亚',
                MT: '马耳他',
                NL: '荷兰',
                NO: '挪威',
                PL: '波兰',
                PT: '葡萄牙',
                RO: '罗马尼亚',
                RU: '俄罗斯',
                RS: '塞尔维亚',
                SE: '瑞典',
                SI: '斯洛文尼亚',
                SK: '斯洛伐克',
                VE: '委内瑞拉',
                ZA: '南非'
            }
        },
        vin: {
            'default': '请输入有效的VIN(美国车辆识别号码)'
        },
        zipCode: {
            'default': '请输入有效的邮政编码',
            countryNotSupported: '不支持 %s 国家或地区',
            country: '请输入有效的 %s 国家或地区的邮政编码',
            countries: {
                BR: '巴西',
                CA: '加拿大',
                DK: '丹麦',
                GB: '英国',
                IT: '意大利',
                MA: '摩洛哥',
                NL: '荷兰',
                RO: '罗马尼亚',
                RU: '俄罗斯',
                SE: '瑞典',
                SG: '新加坡',
                US: '美国'
            }
        }
    };
    var locales=
    {
        zh_cn:zh_cnLocale,
        en_us:en_usLocale
    }

    function setLocale(lang){
        $.fn.bootstrapValidator.i18n = $.extend(true, $.fn.bootstrapValidator.i18n, locales[lang]);
    }

    return {setLocale:setLocale}
});