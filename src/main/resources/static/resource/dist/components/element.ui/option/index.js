module.exports =
/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};

/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {

/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId])
/******/ 			return installedModules[moduleId].exports;

/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			exports: {},
/******/ 			id: moduleId,
/******/ 			loaded: false
/******/ 		};

/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);

/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;

/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}


/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;

/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;

/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "/dist/";

/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(0);
/******/ })
/************************************************************************/
/******/ ({

/***/ 0:
/***/ function(module, exports, __webpack_require__) {

	module.exports = __webpack_require__(157);


/***/ },

/***/ 38:
/***/ function(module, exports) {

	module.exports = require("element-ui/lib/mixins/emitter");

/***/ },

/***/ 157:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	var ElOption = __webpack_require__(158);

	/* istanbul ignore next */
	ElOption.install = function (Vue) {
	  Vue.component(ElOption.name, ElOption);
	};

	module.exports = ElOption;

/***/ },

/***/ 158:
/***/ function(module, exports, __webpack_require__) {

	var __vue_exports__, __vue_options__

	/* script */
	__vue_exports__ = __webpack_require__(159)

	/* template */
	var __vue_template__ = __webpack_require__(160)
	__vue_options__ = __vue_exports__ = __vue_exports__ || {}
	if (
	  typeof __vue_exports__.default === "object" ||
	  typeof __vue_exports__.default === "function"
	) {
	__vue_options__ = __vue_exports__ = __vue_exports__.default
	}
	if (typeof __vue_options__ === "function") {
	  __vue_options__ = __vue_options__.options
	}

	__vue_options__.render = __vue_template__.render
	__vue_options__.staticRenderFns = __vue_template__.staticRenderFns

	module.exports = __vue_exports__


/***/ },

/***/ 159:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	exports.__esModule = true;

	var _emitter = __webpack_require__(38);

	var _emitter2 = _interopRequireDefault(_emitter);

	function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

	exports.default = {
	  mixins: [_emitter2.default],

	  name: 'el-option',

	  componentName: 'option',

	  props: {
	    value: {
	      required: true
	    },
	    label: [String, Number],
	    selected: {
	      type: Boolean,
	      default: false
	    },
	    disabled: {
	      type: Boolean,
	      default: false
	    }
	  },

	  data: function data() {
	    return {
	      index: -1,
	      groupDisabled: false,
	      queryPassed: true,
	      hitState: false
	    };
	  },


	  computed: {
	    currentLabel: function currentLabel() {
	      return this.label || (typeof this.value === 'string' || typeof this.value === 'number' ? this.value : '');
	    },
	    parent: function parent() {
	      var result = this.$parent;
	      while (!result.isSelect) {
	        result = result.$parent;
	      }
	      return result;
	    },
	    itemSelected: function itemSelected() {
	      if (Object.prototype.toString.call(this.parent.selected) === '[object Object]') {
	        return this === this.parent.selected;
	      } else if (Array.isArray(this.parent.selected)) {
	        return this.parent.value.indexOf(this.value) > -1;
	      }
	    },
	    currentSelected: function currentSelected() {
	      return this.selected || (this.parent.multiple ? this.parent.value.indexOf(this.value) > -1 : this.parent.value === this.value);
	    }
	  },

	  watch: {
	    currentSelected: function currentSelected(val) {
	      if (val === true) {
	        this.dispatch('select', 'addOptionToValue', this);
	      }
	    }
	  },

	  methods: {
	    handleGroupDisabled: function handleGroupDisabled(val) {
	      this.groupDisabled = val;
	    },
	    hoverItem: function hoverItem() {
	      if (!this.disabled && !this.groupDisabled) {
	        this.parent.hoverIndex = this.parent.options.indexOf(this);
	      }
	    },
	    selectOptionClick: function selectOptionClick() {
	      if (this.disabled !== true && this.groupDisabled !== true) {
	        this.dispatch('select', 'handleOptionClick', this);
	      }
	    },
	    queryChange: function queryChange(query) {
	      this.queryPassed = new RegExp(query, 'i').test(this.currentLabel);
	      if (!this.queryPassed) {
	        this.parent.filteredOptionsCount--;
	      }
	    },
	    resetIndex: function resetIndex() {
	      var _this = this;

	      this.$nextTick(function () {
	        _this.index = _this.parent.options.indexOf(_this);
	      });
	    }
	  },

	  created: function created() {
	    this.parent.options.push(this);
	    this.parent.optionsCount++;
	    this.parent.filteredOptionsCount++;
	    this.index = this.parent.options.indexOf(this);

	    if (this.currentSelected === true) {
	      this.dispatch('select', 'addOptionToValue', [this, true]);
	    }

	    this.$on('queryChange', this.queryChange);
	    this.$on('handleGroupDisabled', this.handleGroupDisabled);
	    this.$on('resetIndex', this.resetIndex);
	  },
	  beforeDestroy: function beforeDestroy() {
	    this.dispatch('select', 'onOptionDestroy', this);
	  }
	}; //
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//

/***/ },

/***/ 160:
/***/ function(module, exports) {

	module.exports={render:function (){with(this) {
	  return _h('li', {
	    directives: [{
	      name: "show",
	      rawName: "v-show",
	      value: (queryPassed),
	      expression: "queryPassed"
	    }],
	    staticClass: "el-select-dropdown__item",
	    class: {
	      'selected': itemSelected, 'is-disabled': disabled || groupDisabled, 'hover': parent.hoverIndex === index
	    },
	    on: {
	      "mouseenter": hoverItem,
	      "click": function($event) {
	        $event.stopPropagation();
	        selectOptionClick($event)
	      }
	    }
	  }, [_t("default", [_h('span', [_s(currentLabel)])])])
	}},staticRenderFns: []}

/***/ }

/******/ });