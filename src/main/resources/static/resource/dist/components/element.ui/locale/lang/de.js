'use strict';

exports.__esModule = true;
exports.default = {
  el: {
    datepicker: {
      now: 'jetzt',
      today: 'heute',
      cancel: 'abbrechen',
      clear: 'leeren',
      confirm: 'OK',
      selectDate: 'Datum wählen',
      selectTime: 'Zeit wählen',
      startDate: 'Startdatum',
      startTime: 'Startzeit',
      endDate: 'Enddatum',
      endTime: 'Endzeit',
      year: '',
      month1: 'Januar',
      month2: 'Februar',
      month3: 'M<PERSON>rz',
      month4: 'April',
      month5: 'Mai',
      month6: 'Juni',
      month7: 'Juli',
      month8: 'August',
      month9: 'September',
      month10: 'Oktober',
      month11: 'November',
      month12: 'Dezmeber',
      // week: 'Woche',
      weeks: {
        sun: 'So',
        mon: 'Mo',
        tue: 'Di',
        wed: 'Mi',
        thu: 'Do',
        fri: 'Fr',
        sat: 'Sa'
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: '<PERSON><PERSON>r',
        apr: 'Apr',
        may: 'Mai',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aug',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Nov',
        dec: 'Dez'
      }
    },
    select: {
      loading: 'es lädt',
      noMatch: 'nicht gefunden',
      noData: 'keine Datei',
      placeholder: 'wählen'
    },
    pagination: {
      goto: 'zu',
      pagesize: '/Seite',
      total: 'gesamt {total}',
      pageClassifier: ''
    },
    messagebox: {
      confirm: 'OK',
      cancel: 'abbrechen',
      error: 'Fehler'
    },
    upload: {
      delete: 'löschen',
      preview: 'vorschauen',
      continue: 'weiter hochzuladen'
    },
    table: {
      emptyText: 'keine Daten',
      confirmFilter: 'filtern',
      resetFilter: 'rücksetzen',
      clearFilter: 'alles'
    },
    tree: {
      emptyText: 'keine Daten'
    }
  }
};