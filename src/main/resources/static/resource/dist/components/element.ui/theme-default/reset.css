.el-checkbox__inner::after,.el-checkbox__inner.is-checked::after {
    box-sizing: content-box;
}
.fmone-v2 .el-input__inner{
    border-radius: 2px;
}
.fmone-v2 .el-button--success{
    border:1px solid #1ab394;
    color: #1ab394;
    background-color: #ffffff;
    padding: 7px 18px;
}
.fmone-v2 .el-button--success:hover{
    color: #fff;
    background-color: #1ab394;
    border-color: #1ab394;
}
.fmone-v2 .el-button--danger{
    border: 1px solid #ff5353;
    color: #ff5353;
    background-color: #ffffff;
    padding: 7px 18px;
}
.fmone-v2 .el-button--danger:hover{
    color: #ffffff;
    background-color: #ff5353;
    border-color: #ff5353;
}
.fmone-v2 .el-input__inner:focus {
    outline: 0;
    border-color: #1ab394;
}
.fmone-v2 .el-textarea__inner{
    resize: none;
    border-radius: 2px;
}
.fmone-v2 .el-textarea__inner:focus {
    outline: 0;
    border-color: #1ab394;
    resize: none;
}
.fmone-v2 input[readonly=readonly]{
    background-color: #eeeeee;
}.fmone-v2 .el-table .asc .sort-caret.ascending {
    border-bottom-color: #475669;
}
.fmone-v2 .el-table .desc .sort-caret.descending {
    border-top-color: #475669;
}
.fmone-v2 input[type="file"].el-upload__input{
    display:none;
}