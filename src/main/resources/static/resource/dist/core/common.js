define(['jquery','core.messagebox','app.timeago','facilityone'],function($,MessageBox){

    var refreshAccessToken = function(){
        if(FO.get_uri_param("access_token") && FO.get_uri_param("access_token")!=localStorage.access_token){
            localStorage.access_token = FO.get_uri_param("access_token");
        }
    };
    refreshAccessToken();

    var common = {
        getAccessToken : function () {
            var accesstoken = localStorage.access_token;
            if (!accesstoken) {
                accesstoken = FO.get_uri_param("access_token");
            }
            return accesstoken;
        },

        dateFormat : function(date, format) {
            if (!date)
                return;
            if (!format)
                format = "yyyy-MM-dd";

            switch (typeof date) {
                case "string":
                    //"2015-05-13T16:00:00.000+0000"
                    var reg=/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.?\d{0,3}[Z\-+]?(\d{2}:?\d{2})?/;
                    var _zone = 0;
                    var _zone_minute = 0;
                    var _date_date = date.split("\.");
                    if(reg.test(date)){
                        var reg_Z=/[+|-](\d{0,4})/;
                        if(_date_date.length == 2){
                            if(_date_date[1].indexOf("+") >=0){
                                _zone = _date_date[1].split("+")[1];
                            }else if(_date_date[1].indexOf("-") >=0){
                                _zone = -_date_date[1].split("-")[1];
                            }else if(_date_date[1].indexOf("Z") >=0){
                                _zone = _date_date[1].split("Z")[1];
                            }
                            _zone = parseInt(_zone);
                            var _n_date = new Date();
                            var _n_zone = reg_Z.exec(_n_date.toString())[1];
                            _zone = _zone+parseInt(_n_zone);
                            _zone_minute = _zone % 100;
                            _zone = _zone / 100;
                        }
                        _date_date[0] = _date_date[0].replace("T"," ");
                    }
                    //兼容IE
                    _date_date[0] = _date_date[0].replace(/-/g,"/");
                    date = new Date(_date_date[0]);
                    //时区变化
                    date.setHours(_zone+date.getHours());
                    date.setMinutes(_zone_minute+date.getMinutes());
                    break;
                case "number":
                    date = new Date(date);
                    break;
            }

            if (!date instanceof Date)
                return;
            var dict = {
                "yyyy" : date.getFullYear(),
                "M" : date.getMonth() + 1,
                "d" : date.getDate(),
                "H" : date.getHours(),
                "m" : date.getMinutes(),
                "s" : date.getSeconds(),
                "MM" : ("" + (date.getMonth() + 101)).substr(1),
                "dd" : ("" + (date.getDate() + 100)).substr(1),
                "HH" : ("" + (date.getHours() + 100)).substr(1),
                "mm" : ("" + (date.getMinutes() + 100)).substr(1),
                "ss" : ("" + (date.getSeconds() + 100)).substr(1)
            };
            return format.replace(/(yyyy|MM?|dd?|HH?|ss?|mm?)/g, function() {
                return dict[arguments[0]];
            });
        },

        timeago: function(date){
            return $.timeago(date);
        },

        openMenu: function(url, params){
            var isInPage = false;
            /*$(".mymenu a").each(function(){
                if($(this).attr("href") == url){
                    isInPage = true;
                    return;
                }
            });*/
            var hashValue = $.hashAjax.option.get(encodeURIComponent(url));
            if(hashValue){
                isInPage = true;
            }

            if(!isInPage){
                MessageBox.alert({ message: XiaI18n("ajax_no_permission") });
                return ;
            }

            if(FO.newVersionUrls.indexOf(url)==-1){
                var newUrl=window.location.pathname.replace('main_v2','main') +
                    window.location.search+"#__aurl="+ encodeURIComponent(url);
                window.location.assign(newUrl);
            }
            else{
                $.hashAjax.request(url, params);
            }

            //百度追踪
            _hmt && _hmt.push(['_trackPageview', url]);

            //是否在当前页面
            var hash = $.hash.getObject();
            var aurl = hash["__aurl"];
            if(aurl && aurl[0]==="/"){//浏览器兼容
                aurl = encodeURIComponent(aurl);
            }
            if(url && url[0]==="/"){//浏览器兼容
                url = encodeURIComponent(url);
            }
            if(aurl!=url){
               // closeAllMenu();
               // menu_active();
            }
        }
    }

    return common;
})