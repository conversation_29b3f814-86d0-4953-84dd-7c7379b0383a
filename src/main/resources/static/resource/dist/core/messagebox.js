/**
 * Created by will.song on 2017/1/4.
 */
define(['vue'],function(Vue) {
    var helper = new Vue();

    var messageBox = {
        alert:function(options){
            var title = options.title;
            var message = options.message;
            var callback = options.callback
            if(callback) {
                helper.$alert(message, title, {callback:callback});
            } else {
                helper.$alert(message, title);
            }
        }
    }

    return messageBox;
});