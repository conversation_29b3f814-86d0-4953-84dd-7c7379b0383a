/**
 * facilityone.js
 */

var FO = {
    //获取url参数
    get_uri_param: function (key) {
        var uri_params = this.get_uri_params();
        return uri_params[key];
    },
    //获取url所有参数
    get_uri_params: function () {
        var query_string = {};
        var query = window.location.search.substring(1);
        if (query) {
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                query_string[pair[0]] = pair[1];
            }
        }
        return query_string;
    },
    //-- 浏览器工具 --//
    browser: {
        // 检测是否是IE浏览器
        isIE: function () {
            var _uaMatch = $.uaMatch(navigator.userAgent);
            var _browser = _uaMatch.browser;
            if (_browser == 'msie') {
                return true;
            } else {
                return false;
            }
        },
        // 检测是否是chrome浏览器
        isChrome: function () {
            var _uaMatch = $.uaMatch(navigator.userAgent);
            var _browser = _uaMatch.browser;
            if (_browser == 'chrome') {
                return true;
            } else {
                return false;
            }
        },
        // 检测是否是Firefox浏览器
        isMozila: function () {
            var _uaMatch = $.uaMatch(navigator.userAgent);
            var _browser = _uaMatch.browser;
            if (_browser == 'mozilla') {
                return true;
            } else {
                return false;
            }
        },
        // 检测是否是Firefox浏览器
        isOpera: function () {
            var _uaMatch = $.uaMatch(navigator.userAgent);
            var _browser = _uaMatch.browser;
            if (_browser == 'opera') {
                return true;
            } else {
                return false;
            }
        }
    },
    /*newVersionUrls: [
        '/chart/dashboard',
        '/contract001',
        '/contract002',
        '/contract003',
        '/contract004',
        '/contract005',
        '/vendor001',
        '/contract006',
        '/epayment001',
        '/epayment002',
        '/epayment003',
        '/epayment004',
        '/report071',
        '/monitoring001',
        '/monitoring002',
        '/monitoring003',
        '/monitoring004',
        '/monitoring005',
        '/monitoring006',
        '/monitoring007',
        '/monitoring008',
        '/sys007',
        '/under001',
        '/under002',
        '/ezviz001',
        '/ezviz002'
    ],*/
    report: {
        showTime: function (containerID, time) {
            var text = XiaI18n("js.termination.time") + " ",
                container = document.getElementById(containerID);

            if (time) {
                text += time;
            }
            else {
                var date = new Date();
                date.setDate(date.getDate() - 1);
                var nowYear = date.getFullYear();
                var nowMonth = date.getMonth() + 1;
                var nowDay = date.getDate();
                nowMonth = nowMonth < 10 ? "0" + nowMonth : nowMonth;
                nowDay = nowDay < 10 ? "0" + nowDay : nowDay;
                text += nowYear + "-" + nowMonth + "-" + nowDay + " " + XiaI18n("js.termination.yesterday");
            }
            if (container) {
                var textNode = document.createTextNode(text);
                container.appendChild(textNode);
            }
        }
    },
    isHome: function () {
        var str = "/main_v2/home";
        if (location.pathname.indexOf(str)>=0) {
            return true
        }
        return false;
    },
    utils:{
        isEmptyObject:function(obj) {
            for (var key in obj) {
                return false;
            }
            return true;
        },
    },
    newVersionUrls: [
        '/chart/dashboard/index_new',
        '/knowledge003',
        '/knowledge004',
        '/contract001',
        '/contract002',
        '/contract003',
        '/contract004',
        '/contract005',
        '/contract006',
        '/contract007',
        '/contract008',
        '/vendor001',
        '/epayment001',
        '/epayment002',
        '/epayment004',
        '/report071',
        '/monitoring001',
        '/monitoring002',
        '/monitoring003',
        '/monitoring004',
        '/monitoring005',
        '/monitoring006',
        '/monitoring007',
        '/monitoring008',
        '/sys007',
        '/bus001',
        '/bus002',
        '/bus003',
        '/bus004',
        '/bus005',
        '/bus006',
        '/visitor001',
        '/visitor002',
        '/visitor003',
        '/knowledge001V2',
        '/knowledge002V2',
        '/knowledge003V2',
        '/knowledge004V2',
        '/under001',
        '/under002',
        '/ezviz001',
        '/ezviz002',
        '/ene051',
        '/ene052',
        '/sys007',
        '/wo011',
        '/decision001',
        '/decision002',
        '/iot001',
        '/iot002',
        '/iot003',
        "/asset004",
        "/asset005",
        "/asset007",
        "/asset008",
        '/asset010',
        '/asset011',
        '/fs/view/h5/map/choose',
    ],
    vuePage: {
        register: function (instance) {
            FO.currentVueInstance = instance;
        },
        unregister: function () {
            if (FO.currentVueInstance!=null) {
                FO.currentVueInstance.$destroy();
            }
        }
    },
    currentVueInstance:null
}

window.FO = FO;

//-- Javascript对象扩展--开始-//
/**
 * 去掉开头、结尾的空格
 *
 * @return {}
 */
String.prototype.trim = function () {
    return this.replace(/(^\s+)|\s+$/g, "");
};

/**
 * 转换字符串为json对象
 */
String.prototype.toJson = function () {
    return eval('(' + this + ')');
};

/**
 * 把文本转换为HTML代码
 * @param {Object} text    原始文本
 */
String.prototype.toHtml = function () {
    var textold;
    var text = this;
    text = text.replace(/\\n /g, "<br/>");
    text = text.replace(/\ /g, "&nbsp;");
    return text;
},

/**
 * 把HTML代码转换为文本
 * @param {Object} text    原始HTML代码
 */
    String.prototype.toText = function () {
        var textold;
        var text = this;
        //text = text.replace(/\&/g, "&amp;");
        text = text.replace(/\"/g, "&quot;");
        text = text.replace(/\'/g, "&#039;");
        text = text.replace(/\</g, "&lt;");
        text = text.replace(/\>/g, "&gt;");
        return text;
    }

/**
 * 对象和数组的深拷贝
 */
Object.clone = function (sObj) {
    if (typeof sObj !== "object") {
        return sObj;
    }
    var s = {};
    if (sObj.constructor == Array) {
        s = [];
    }
    for (var i in sObj) {
        s[i] = Object.clone(sObj[i]);
    }
    return s;
}

/**
 * format="yyyy-MM-dd hh:mm:ss";
 */
Date.prototype.format = function (format) {
    var o = {
        "M+": this.getMonth() + 1,
        "d+": this.getDate(),
        "H+": this.getHours(),
        "m+": this.getMinutes(),
        "s+": this.getSeconds(),
        "q+": Math.floor((this.getMonth() + 3) / 3),
        "S": this.getMilliseconds()
    }

    if (/(y+)/.test(format)) {
        format = format.replace(RegExp.$1, (this.getFullYear() + "")
            .substr(4 - RegExp.$1.length));
    }

    for (var k in o) {
        if (new RegExp("(" + k + ")").test(format)) {
            format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k]
                : ("00" + o[k]).substr(("" + o[k]).length));
        }
    }
    return format;
}

var imgUrlList = [];//相册图片srclist