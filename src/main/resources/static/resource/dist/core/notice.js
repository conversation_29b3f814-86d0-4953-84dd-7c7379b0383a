define(['vue'],function(Vue){
    var helper =  new Vue();

    var Notice = {
        error: function (message) {
            helper.$notify({
                type:'error',
                title:XiaI18n('js.sys004.error'),
                message:message,
                duration:0
            });
        },
        info:function(message){
            helper.$notify({
                type:'info',
                title:XiaI18n('js.sys004.message'),
                message:message,
                duration:3000
            });
        },
        success:function(message){
            helper.$notify({
                type:'success',
                title:XiaI18n('js.sys004.success'),
                message:message,
                duration:3000
            });
        },
        warning:function(message){
            helper.$notify({
                type:'warning',
                title:XiaI18n('js.sys004.warning'),
                message:message,
                duration:0
            });
        }
    }

    window.DEBUG_MSG = function(msg,tag){
        if(DEBUG && Notice){
            Notice.error(XiaI18n("ajax_error"))
        }
        else if(DEBUG){
            alert(tag+"："+msg)
        }
        console.error(tag+"："+msg);
    }

    window.DEBUG_LOG = function(msg){
        if(DEBUG){
            console.log(msg);
        }
    }

    return Notice;
})