/**
 * Created by will.song on 2016/10/28.
 */
define(['vue',
        'jquery',
        'ELEMENT',
        'FMOne',
        'core.notice',
        'core.messagebox',
        'app.header',
        'app.menu',
        'common',
        'ELEMENT.locale.zh-cn',
        'ELEMENT.locale.en',
        'jquery.cookie',
        'jquery.hash'],
    function(Vue, $, ELEMENT, FMOne, Notice, MessageBox, header, menu, common, cn, en ){
        Vue.config.errorHandler = function (err, vm) {
            console.log(err);
        }

        Vue.filter("date", function(value, format) {   //全局方法 Vue.filter() 注册一个自定义过滤器,必须放在Vue实例化前面
            return common.dateFormat(value, format);
        });
        
        var jquery_ajax_complete_error = function(XMLHttpRequest, textStatus){
            // 401 token过期重新请求
            if (XMLHttpRequest.status == "401") {
                var msg = "";
                try {
                    var Result = $.parseJSON(XMLHttpRequest.responseText);
                    msg = Result.message.split(" ")[0];
                } catch (e) {
                    var wwwAuthenticate = XMLHttpRequest.getResponseHeader("WWW-Authenticate");
                    if(wwwAuthenticate){
                        // if(wwwAuthenticate.toString().indexOf('expired_token')>-1){
                            msg = "expired_token";
                        // }
                    }else{
                        msg = XMLHttpRequest.responseText.split(" ")[0];
                    }
                }
                if (msg == "Authorization") {
                    MessageBox.alert({ message: XiaI18n("ajax_no_permission") });
                } else if (msg == "expired_token") {
                    MessageBox.alert({
                        message: XiaI18n("not_operate_long_time"),
                        callback: function () {
                            window.location.reload();
                        }
                    });
                } else {
                    var currentproject = getCurrentProject();
                    var currentprojectUrl = '';
                    if(currentproject){
                        currentprojectUrl = "&current_project=" + currentproject;
                    }
                    $.ajax({
                        url: "/oauth2/token?client_id=00000000&client_secret=11111111&grant_type=refresh_token&redirect_uri=main_v2/index&refresh_token="
                        + localStorage.refresh_token + currentprojectUrl,
                        contentType: "application/x-www-form-urlencoded",
                        type: "POST",
                        success: function (response) {
                            if (response.access_token) {

                                localStorage.access_token = response.access_token;
                                localStorage.refresh_token = response.refresh_token;

                                var hparam = $.hash.getObject();
                                var dataUrl = hparam[__aurl];
                                if (FO) {
                                    if (FO.browser.isChrome()) {
                                        dataUrl = decodeURIComponent(dataUrl);
                                    } else if (FO.browser.isMozila()) {
                                        //url = encodeURIComponent(url);
                                    }
                                }
                                var uparam = FO.get_uri_params();
                                if (uparam["access_token"]) {
                                    delete uparam["access_token"];
                                }
                                common.openMenu(dataUrl, uparam);
                            }
                        }
                    });
                }
            } else if (XMLHttpRequest.status == "200") {
                location.reload();
            } else {
                try {
                    var Result = $.parseJSON(XMLHttpRequest.responseText);
                } catch (e) {
                    DEBUG_MSG && DEBUG_MSG(XMLHttpRequest.responseText, "ajax-parse-response-error");
                }
                if (Result.code == "311") {//ValidationException
                    if (Result.data) {
                        var errorMsgBuilder = function (name, msg) {
                            return '<small class="help-block s-form-valid-error"  style="display: block;">' + msg + '</small>';
                        }
                        for (var d in Result.data) {
                            var input = $(".form-group input[name='" + d + "']");
                            if (!input.length) {
                                input = $(".form-group input[valid-name='" + d + "']");
                            }
                            if (input.length) {
                                var msg = errorMsgBuilder(input.attr("name"), Result.data[d]);
                                input.parent().parent(".form-group").addClass("has-error");
                                input.parent().parent(".form-group").removeClass("has-success");
                                input.parent().find(".s-form-valid-error").remove();
                                input.keyup(function () {
                                    input.parent().find(".s-form-valid-error").remove();
                                });
                                $(msg).insertAfter(input);
                            }
                        }
                    }
                } else if (Result.code == "312") {//OptimisticLockException
                    var obj = this;
                    if (obj.versionCallback) {
                        bootbox.alert(XiaI18n("ajax_data_version_old"), function () {
                            eval(obj.versionCallback);
                        });
                    }
                }else if (Result.code == "310" || Result.code == "313") {
                    var msg =Result.message.split("\n")[0];
                    Notice.error(msg)
                }else if (Result.code == "500") {
                    var msg = Result.message.split("\n")[0];
                    Notice.error(msg || XiaI18n("ajax_error"));
                }else if (Result.message) {
                    DEBUG_MSG && DEBUG_MSG(Result.message, "ajax-error");
                }
            }
        }

        $.ajaxSetup({
            contentType: "application/json",
            dataType: "json",
            processData: false,
            cache: false,//关闭浏览器get请求的缓存（IE）
            complete: function (XMLHttpRequest, textStatus) {
                if ("success" == textStatus) {
                    try {
                        var Result = $.parseJSON(XMLHttpRequest.responseText);
                        if (undefined != Result.code && Result.code == "200") {// Result.class格式数据
                            if (Result.status == "success") {
                                Result.message && Notice.success(Result.message);
                            } else if (Result.status == "fail") {
                                Result.message && Notice.warning(Result.message)
                            } else if (Result.status == "error") {
                                Result.message && Notice.error(Result.message);
                            }
                        } else if (Result.message) {
                            Result.message && Notice.error(Result.message);
                        }

                    } catch (e) {
                        ;//不做处理
                    }
                } else{//异常情况
                    jquery_ajax_complete_error(XMLHttpRequest, textStatus);
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                //jquery_ajax_complete_error(XMLHttpRequest, textStatus);
            },
            beforeSend: function (xhr) {
                var finalCurrentProject = null;
                var currentproject = localStorage.current_project;
                var currentprojectFromUrl = FO.get_uri_param("current_project");
                if(!currentproject&&currentprojectFromUrl){
                    finalCurrentProject = currentprojectFromUrl;
                }else if(!currentprojectFromUrl&&currentproject){
                    finalCurrentProject = currentproject;
                }else{
                    if(currentproject!=currentprojectFromUrl){
                        finalCurrentProject = currentprojectFromUrl;
                    }else{
                        finalCurrentProject = currentproject;
                    }
                }
                var accesstoken = localStorage.access_token;
                if(isWhiteUser == "true"){
                    accesstoken = FO.get_uri_param("access_token");
                }
                var tempProject = finalCurrentProject;
                var currentproject = tempProject?tempProject:0;
                xhr.setRequestHeader('Authorization', 'Bearer ' + accesstoken);
                xhr.setRequestHeader('CurrentProject', currentproject);
            }
        });

        $.hashAjax.init();

        var lang = { 'zh_CN': cn, 'en_US': en };
        var locale = document.documentElement.lang ? lang[document.documentElement.lang] : lang['zh_CN'];

        Vue.use(ELEMENT, { locale: locale });
        Vue.use(FMOne);

        var headerVM = header.install(Vue, $);
        var menuVM = menu.install(Vue, $);

        //menuVM.selectMenu = $.hashAjax.getHashValue().path;

        headerVM.$on('open-menu', function(url){
            var hashValue = $.hashAjax.option.get(encodeURIComponent((url)));
            if(hashValue){
                menuVM.openMenu(hashValue.path);
                //menuVM.selectMenu = hashValue.path;
            }
            else{
                MessageBox.alert({ message: XiaI18n("ajax_no_permission") });
            }
        });

        headerVM.$on('narrow-changed', function(isNarrow){
           menuVM.isNarrow = isNarrow;
        });

        return {header:headerVM, menu: menuVM};
});
