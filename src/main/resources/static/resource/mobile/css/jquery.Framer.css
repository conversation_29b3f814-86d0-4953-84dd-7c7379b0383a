@font-face {
  font-family: 'close';
  src: url("../fonts/close.eot?s9u2ac");
  src: url("../fonts/close.eot?#iefixs9u2ac") format("embedded-opentype"), url("../fonts/close.woff?s9u2ac") format("woff"), url("../fonts/close.ttf") format("truetype"), url("../fonts/close.svg?s9u2ac") format("svg");
  font-weight: normal;
  font-style: normal;
}
div#frm_overlay {
  background: #000;
  display: none;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1999;
  opacity: 0;
  -webkit-transform: translate3d(0, 0, 0);
}

div#framer {
  position: absolute;
  display: none;
  z-index: 2000;
}
div#framer #framerContainer {
  position: relative;
  background-color: #000;
  padding: 0;
}
div#framer.anim_base {
  -moz-backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
div#framer.show {
  opacity: 1;
}
div#framer img {
  display: block;
}
div#framer object {
  display: block;
  outline: none;
  overflow: hidden;
}
div#framer .close_btn {
  font-family: "close";
  speak: none;
  line-height: 26px;
  font-size: 22px;
  font-style: normal;
  font-weight: normal;
  text-transform: none;
  text-align: center;
  position: absolute;
  width: 28px;
  height: 28px;
  overflow: hidden;
  right: -14px;
  top: -14px;
  display: none;
  cursor: pointer;
  z-index: 2001;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.8);
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
div#framer .close_btn:hover {
  color: #bbb;
  background-color: rgba(0, 0, 0, 0.6);
}
div#framer .close_btn:before {
  content: "\e600";
}
div#framer.fade_in_scale #framerContainer {
  -moz-transform: scale(0.7);
  -ms-transform: scale(0.7);
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
  opacity: 0;
}
div#framer.fade_in_scale.show #framerContainer {
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -webkit-transform: scale(1);
  transform: scale(1);
  opacity: 1;
}
div#framer.slide_in_right #framerContainer {
  -moz-transform: translateX(20%);
  -ms-transform: translateX(20%);
  -webkit-transform: translateX(20%);
  transform: translateX(20%);
  -moz-transition-timing-function: cubic-bezier(0.25, 0.5, 0.5, 0.9);
  -o-transition-timing-function: cubic-bezier(0.25, 0.5, 0.5, 0.9);
  -webkit-transition-timing-function: cubic-bezier(0.25, 0.5, 0.5, 0.9);
  transition-timing-function: cubic-bezier(0.25, 0.5, 0.5, 0.9);
  opacity: 0;
}
div#framer.slide_in_right.show #framerContainer {
  -moz-transform: translateX(0);
  -ms-transform: translateX(0);
  -webkit-transform: translateX(0);
  transform: translateX(0);
  opacity: 1;
}
div#framer.slide_in_bottom #framerContainer {
  -moz-transform: translateY(20%);
  -ms-transform: translateY(20%);
  -webkit-transform: translateY(20%);
  transform: translateY(20%);
  opacity: 0;
}
div#framer.slide_in_bottom.show #framerContainer {
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
}
div#framer.sign_3d {
  -moz-perspective: 1300px;
  -webkit-perspective: 1300px;
  perspective: 1300px;
}
div#framer.sign_3d #framerContainer {
  -moz-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -moz-transform: rotateX(-60deg);
  -ms-transform: rotateX(-60deg);
  -webkit-transform: rotateX(-60deg);
  transform: rotateX(-60deg);
  -moz-transform-origin: 50% 0 0;
  -webkit-transform-origin: 50% 0 0;
  transform-origin: 50% 0 0;
  opacity: 0;
}
div#framer.sign_3d.show #framerContainer {
  -moz-transform: rotateX(0deg);
  -ms-transform: rotateX(0deg);
  -webkit-transform: rotateX(0deg);
  transform: rotateX(0deg);
  opacity: 1;
}

div#loading {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 2000;
  top: 0;
  left: 0;
}

div#frmTitle {
  position: absolute;
  top: 0;
  left: 0;
}

div#frm_description {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
}

iframe {
  margin: 0;
  padding: 0;
  border-style: none;
  display: block;
  border-width: 0;
}

div#framer_inner {
  width: 100%;
  height: 30px;
  background-color: #000;
}

div#framer_error {
  width: 300px;
  min-height: 18px;
  background-color: #eb4c2d;
  font-size: 18px;
  line-height: 18px;
  color: #fff;
  padding: 6px;
  text-align: center;
}

video {
  display: block;
}
