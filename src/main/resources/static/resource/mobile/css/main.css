*{font-family: "Microsoft YaHei";}
        .ui-bar-a, .ui-page-theme-a .ui-bar-inherit, html .ui-bar-a .ui-bar-inherit, html .ui-body-a .ui-bar-inherit, html body .ui-group-theme-a .ui-bar-inherit{
            background-color: #FDFDFD;
            color: #4C4C4C;
        }
        .ui-navbar li .ui-btn{
            font-size: 1em;
        }

        #detail .dataContainer{padding: 0 1.2em}
        .data .title{width: 20%;text-align: right;padding-right: 10px;font-weight: normal;color: #999;font-size: 1em}
        .data .value{width: 66%;font-size: 1em;font-weight: normal;padding: 7px;color: #4C4C4C;}
        .data .edit{display: none;font-weight: normal;}

        #source table{margin-left: 16px;}
/*        .ui-content{background-color: #FDFDFD;}*/
        #detail table{margin-left: -16px;}
        #index a.ui-btn{background-color: #FDFDFD;}

        #detail .inspect{white-space: pre-wrap;line-height: 1em;}

        #body .subInfo{font-size: 1em;color:#636363}

        #body .ui-btn-active{font-weight: normal;/*color: #4C4C4C*/;text-shadow:none;}
        #body .ui-listview{font-weight: normal;}

        .edit-BtnGroup .ui-btn{width: 38%;margin: 8px 0;}
        .edit-BtnGroup .ui-btn:first-child{float: left;}
        .edit-BtnGroup .ui-btn:last-child{float: right;}

        .ui-listview > li p.time{text-align: right;}
        .ui-listview .status{font-size: 0.8em;padding: 2px 6px;/*background-color:#E5E5E5;*/border-radius: 2px;top: 1.4em;right: 6em;margin-right: 6em;color: #545454;float: right;}
        .ui-listview > li .describe{margin-left: 1em;list-style: none;font-weight: normal;font-size: 0.9em;margin-top:-2.2em;}
        .ui-listview > li .describe li{
            line-height: 1.5em;overflow : hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;
white-space: pre-wrap;}
        .ui-listview > li p.type{text-shadow: none;position: absolute;right: 0;top: 0;padding: 3px 10px;color: #fff;border-radius: 10px 0 0 10px;font-size: 0.8em;}
        .ui-listview > li p.type.consult{background-color: #60C32C;}
        .ui-listview > li p.type.complain{background-color: #F9A11D;}
        .ui-listview > li p.type.repairs{background-color: #F34D4C;}
        .ui-btn-icon-right:after{opacity: 0.6;}
        .ui-listview .nextPage{text-align: center;margin: 0;padding: 0;}
        .ui-listview .nextPage-btn{margin: 0;border: 0;padding: 1em;font-weight: normal;}

        .header-title{padding: 10px;text-align: center;font-size: 1.2em;font-weight: bold;}

        #detail .ui-listview .record{padding: 0 1em;}

        .dialogTitle{background-color: #20B2AA;color: #FFF;text-shadow:none;padding: 10px;font-size: 1em;}

        #evaluate .ui-btn, label.ui-btn{font-weight: normal;}
        #evaluate .ui-page-theme-a .ui-btn{background-color: transparent;border: none;}

        .number{font-size:1em;padding-right:1em;font-weight:normal;}
        #detail .nobg{background-color: transparent;border: 0;}
        #evaluate .nobg{background-color: transparent;border: 0;}
        #textarea{min-height: 150px;}
        #evaluate .ui-radio .nobg{background-color: transparent;border: 0;margin-top: -20px;}
        #evaluate .ui-btn.ui-checkbox-off:after, .ui-btn.ui-checkbox-on:after, .ui-btn.ui-radio-off:after, .ui-btn.ui-radio-on:after{
            margin: -10px 0 0 -1px;
        }
        #evaluate .ui-radio .ui-btn.ui-radio-on:after{width: 9px;height: 9px;}
        #delDetBtn{background-color: #FF6347;color: #fff;font-weight: normal;text-shadow:none;border:#FF6347;padding: 1em;}
        #subDetBtn{background-color: #FFA500;color: #fff;font-weight: normal;text-shadow:none;border:#FFA500;padding: 1em;}
        #evaluate{height: 100%;}
        .shade{width: 100%;height: 100%;background-color: #fff;position: absolute;z-index: 100000;opacity: 0.8;display: none;}
        #evaluate #evalFooter{position: absolute;width: 100%;bottom: 0;}
        .ui-loading .ui-loader{background-color: transparent;}
        .nodata{text-align: center;color: #939393;}
        #index .ui-content{background-color: #F9F9F9;}
        .nodataLogo{margin: 50% auto 2em;text-align: center;display: block;}
        /*.image #framerContainer{width: 90%;margin: 0 auto;}*/
        .image#framer{width: 95%}
        td.title.detail{vertical-align:top;padding-top:7px;}
      #video > video{
        width: 50px;
        height: 50px;
      }
/*时间�?*/
 #timeline .timeline-item:after,#timeline .timeline-item:before {
    content: '';
    display: block;
    width: 100%;
    clear: both;
}
*, #timeline:before, #timeline:after {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
}
body, html {
    height: 100%}
body {
    background: #f9f9f9;
    background-size: cover;
    margin: 0;
    padding: 0;
    font-family: helvetica, arial, tahoma, verdana;
    line-height: 20px;
    font-size: 14px;
    color: #726f77;
    -webkit-font-smoothing: antialiased;
}
img {
    max-width: 100%}
a {
    text-decoration: none;
}
.container {
    max-width: 1100px;
    margin: 0 auto;
}
h1, h2, h3, h4 {
    font-family: "Dosis", arial, tahoma, verdana;
    font-weight: 600;
}

#timeline {
    width: 90%;
    margin: 10px auto;
    position: relative;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    -ms-transition: all 0.4s ease;
    transition: all 0.4s ease;
}
#timeline:before {
    content: "";
    width: 3px;
    height: 100%;
    background: #20B2AA;
    top: 0;
    position: absolute;
}
#timeline:after {
    content: "";
    clear: both;
    display: table;
    width: 100%}
#timeline .timeline-item {
    margin-bottom: 40px;
    position: relative;
}
#timeline .timeline-item .timeline-icon {
    background: #20B2AA;
    width: 10px;
    height: 10px;
    position: absolute;
    top: 0;
    overflow: hidden;
    margin-left: -4px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%}
#timeline .timeline-item .timeline-icon img {
    position: relative;
    top: 14px;
    left: 14px;
}
#timeline .timeline-item .timeline-content {
    width: 45%;
    background: #fff;
    padding: 20px;
    -webkit-box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
    -ms-box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    border-radius: 5px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    transition: all 0.3s ease;
    color: #fff;
}
#timeline .timeline-item .timeline-content h2 {
    padding: 15px;
    background: #20B2AA;
    color: #fff;
    margin: -20px -20px 0 -20px;
    font-weight: 500;
    -webkit-border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    -ms-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
}
#timeline .timeline-item .timeline-content:before {
    content: '';
    position: absolute;
    left: 45%;
    top: 20px;
    width: 0;
    height: 0;
    border-top: 7px solid transparent;
    border-bottom: 7px solid transparent;
    border-left: 7px solid #20B2AA;
}
#timeline .timeline-item .timeline-content.right {
    float: right;
    background-color: #20B2AA;
    text-shadow:none;
    word-break:break-all;
    white-space: pre-line;
    padding-top: 0;
    box-shadow: none;
}
#timeline .timeline-item .timeline-content.right:before {
    content: '';
    right: 45%;
    left: inherit;
    border-left: 0;
    border-right: 7px solid #20B2AA;
}
.btn {
    padding: 5px 15px;
    text-decoration: none;
    background: transparent;
    border: 2px solid #f27c7c;
    color: #f27c7c;
    display: inline-block;
    position: relative;
    text-transform: uppercase;
    font-size: 12px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    border-radius: 5px;
    -webkit-transition: background 0.3s ease;
    -moz-transition: background 0.3s ease;
    -ms-transition: background 0.3s ease;
    transition: background 0.3s ease;
    -webkit-box-shadow: 2px 2px 0 #f27c7c;
    -moz-box-shadow: 2px 2px 0 #f27c7c;
    -ms-box-shadow: 2px 2px 0 #f27c7c;
    box-shadow: 2px 2px 0 #f27c7c;
}
.btn:hover {
    box-shadow: none;
    top: 2px;
    left: 2px;
    -webkit-box-shadow: 2px 2px 0 transparent;
    -moz-box-shadow: 2px 2px 0 transparent;
    -ms-box-shadow: 2px 2px 0 transparent;
    box-shadow: 2px 2px 0 transparent;
}
@media screen and (max-width: 768px) {
    #timeline {
    padding: 0;
}
#timeline:before {
    left: 0;
}
#timeline .timeline-item .timeline-content {
    width: 90%;
    float: right;
}
#timeline .timeline-item .timeline-content:before, #timeline .timeline-item .timeline-content.right:before {
    left: 10%;
    margin-left: -6px;
    border-left: 0;
    border-right: 7px solid #20B2AA;
}
#timeline .timeline-item .timeline-icon {
    left: 0;
    top:20px;
}
#timeline .timeline-item:last-child{
    margin-bottom: 0;
}



/*弹出�?*/
  .popbg .container{
    background-color: #FFF;
    width: 300px;
    min-height: 100px;
    border-radius: 10px;
    padding: 2em 2em 1em;
    position: relative;
    font-size: 1.2em;
  }
  .popbg .container .text{
    margin: 0 auto;
    display: block;
    text-align: center;
    
  }
  .popbg .container .btngroup{
    margin: 1.5em auto 0;
    left: 55px;
    text-align: center;
    width: 190px;
    height: 30px;
  }
  .popbg .container .btngroup button{
    width: 80px;
    height: 30px;
    font-size: 0.9em;
    border-radius: 5px;
    border: none;
    background-color: #20B2AA;
    color: #FFF;
  }
  .popbg .container .btngroup button:first-child{
    float: left;
  }
  .popbg .container .btngroup button:last-child{
    float: right;
  }
/*自动消失弹出�?*/
  .popbgshade{
    width: 100%;
    position: absolute;
    top: 0;
    text-align: center;  
    background-color: rgba(32,178,170,0.9);
    z-index: 1000;
  }
  .popbgshade .container{
    padding: 20px;
    font-size: 1.2em;
    line-height: 1.5em;
    text-shadow:none;
    color: #fff;
  }

#audio > audio{
    float: left;
    margin-top: 10px;
}

 #image > img{
    width: 50px;
    height: 50px;
}
.popbg{
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    position: absolute;
    z-index: 1000;
    top:0;
}
#index h2{
  font-weight: normal;
}
#index .ui-content{
  padding: 0 14px;
}
#index p.time{
  position: absolute;
  right: 1em;
  bottom: 0.5em;
  font-size: 0.8em;
  color: #8A8A8A;
}
#index .describe{
  width: 75%;
}
#evaluateBtn{
  background-color: #FFA500;
  padding: 1em;
  color: #FFF;
  text-shadow:none;
}

#audio > audio{
    float: left;
    margin-top: 10px;
}
#audio > .audioItem,#video > .videoItem{
  width: 34px;
  float: left;
  margin-right: 10px;
}
section{
  width: 100%;
  height: 50px;
  line-height: 50px;
}
section > img {
    padding-top: 12px;
}
}
.videoclass video{
    z-index: 20000;
}
#submitEvaluate{
    background-color: #20B2AA;
    color: #FFF;
    text-shadow: none;
    padding: 1em;
}
#cancelEvaluate{
    background-color: #FFA500;
    color: #FFF;
    text-shadow: none;
    padding: 1em;
}

