@charset "UTF-8";
div#container {
  width: 100%;
  padding-top: 0px;
}

section {
  display: block;
  width: 900px;
  margin: 0 auto 0px;
}
section section {
  margin-bottom: 10px;
}
section section:last-child {
  margin-bottom: 0;
}
section h1 {
  color: #579eeb;
  font-size: 28px;
  font-weight: 300;
  margin: 0 0 30px;
  padding: 0;
  letter-spacing: 1px;
}
section h2 {
  font-size: 24px;
  margin: 0 0 15px;
  padding: 0 0 8px;
  font-weight: 100;
  letter-spacing: 1px;
  border-bottom: 1px solid #d8dde6;
}
section h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 100;
  margin-bottom: 20px;
  letter-spacing: 1px;
}
section h3 span {
  font-size: 11px;
  font-weight: 300;
}
section .options {
  margin: 40px 0;
}
section .options:last-child {
  margin-bottom: 0;
}
section dl {
  margin: 0 0 -20px;
  overflow: hidden;
}
section dt {
  width: 156px;
  padding-right: 12px;
  padding-left: 12px;
  padding-bottom: 8px;
  display: inline-block;
  border-bottom: 1px solid #d8dde6;
  margin: 0 0 20px;
}
section dd {
  width: 696px;
  padding-right: 12px;
  padding-left: 12px;
  padding-bottom: 8px;
  display: inline-block;
  border-bottom: 1px solid #d8dde6;
  margin: 0 0 20px;
}

div#inline_sample,
#api_contents {
  display: none;
  width: 800px;
  height: 500px;
  color: #787878;
  background-color: #fff;
  padding: 50px;
}

section#ajaxContents {
  width: 300px;
  height: 300px;
  padding: 30px;
  margin: 0;
  background-color: #fff;
}

code, pre {
  padding: 10px;
  background-color: #f2f8ff;
  border: 1px solid #c1dbf5;
  display: block;
  margin-bottom: 15px;
}
.framer > img{
  width: 50px;
  height: 50px;
}
#video a,#video video{
  display: inline;
}
