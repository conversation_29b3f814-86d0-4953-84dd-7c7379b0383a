 #timeline .timeline-item:after,#timeline .timeline-item:before {
    content: '';
    display: block;
    width: 100%;
    clear: both;
}
*, #timeline:before, #timeline:after {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
}
body, html {
    height: 100%}
body {
    background: #f9f9f9;
    background-size: cover;
    margin: 0;
    padding: 0;
    font-family: helvetica, arial, tahoma, verdana;
    line-height: 20px;
    font-size: 14px;
    color: #726f77;
    -webkit-font-smoothing: antialiased;
}
img {
    max-width: 100%}
a {
    text-decoration: none;
}
.container {
    max-width: 1100px;
    margin: 0 auto;
}
h1, h2, h3, h4 {
    font-family: "Dosis", arial, tahoma, verdana;
    font-weight: 600;
}

#timeline {
    width: 90%;
    margin: 10px auto;
    position: relative;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    -ms-transition: all 0.4s ease;
    transition: all 0.4s ease;
}
#timeline:before {
    content: "";
    width: 3px;
    height: 100%;
    background: #20B2AA;
    top: 0;
    position: absolute;
}
#timeline:after {
    content: "";
    clear: both;
    display: table;
    width: 100%}
#timeline .timeline-item {
    margin-bottom: 40px;
    position: relative;
}
#timeline .timeline-item .timeline-icon {
    background: #20B2AA;
    width: 10px;
    height: 10px;
    position: absolute;
    top: 0;
    overflow: hidden;
    margin-left: -4px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%}
#timeline .timeline-item .timeline-icon img {
    position: relative;
    top: 14px;
    left: 14px;
}
#timeline .timeline-item .timeline-content {
    width: 45%;
    background: #fff;
    padding: 20px;
    -webkit-box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
    -ms-box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    border-radius: 5px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    transition: all 0.3s ease;
    color: #fff;
}
#timeline .timeline-item .timeline-content h2 {
    padding: 15px;
    background: #20B2AA;
    color: #fff;
    margin: -20px -20px 0 -20px;
    font-weight: 500;
    -webkit-border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    -ms-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
}
#timeline .timeline-item .timeline-content:before {
    content: '';
    position: absolute;
    left: 45%;
    top: 20px;
    width: 0;
    height: 0;
    border-top: 7px solid transparent;
    border-bottom: 7px solid transparent;
    border-left: 7px solid #20B2AA;
}
#timeline .timeline-item .timeline-content.right {
    float: right;
    background-color: #20B2AA;
    text-shadow:none;
    word-break:break-all;
    white-space: pre-line;
    padding-top: 0;
}
#timeline .timeline-item .timeline-content.right:before {
    content: '';
    right: 45%;
    left: inherit;
    border-left: 0;
    border-right: 7px solid #20B2AA;
}
.btn {
    padding: 5px 15px;
    text-decoration: none;
    background: transparent;
    border: 2px solid #f27c7c;
    color: #f27c7c;
    display: inline-block;
    position: relative;
    text-transform: uppercase;
    font-size: 12px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    border-radius: 5px;
    -webkit-transition: background 0.3s ease;
    -moz-transition: background 0.3s ease;
    -ms-transition: background 0.3s ease;
    transition: background 0.3s ease;
    -webkit-box-shadow: 2px 2px 0 #f27c7c;
    -moz-box-shadow: 2px 2px 0 #f27c7c;
    -ms-box-shadow: 2px 2px 0 #f27c7c;
    box-shadow: 2px 2px 0 #f27c7c;
}
.btn:hover {
    box-shadow: none;
    top: 2px;
    left: 2px;
    -webkit-box-shadow: 2px 2px 0 transparent;
    -moz-box-shadow: 2px 2px 0 transparent;
    -ms-box-shadow: 2px 2px 0 transparent;
    box-shadow: 2px 2px 0 transparent;
}
@media screen and (max-width: 768px) {
    #timeline {
    padding: 0;
}
#timeline:before {
    left: 0;
}
#timeline .timeline-item .timeline-content {
    width: 90%;
    float: right;
}
#timeline .timeline-item .timeline-content:before, #timeline .timeline-item .timeline-content.right:before {
    left: 10%;
    margin-left: -6px;
    border-left: 0;
    border-right: 7px solid #20B2AA;
}
#timeline .timeline-item .timeline-icon {
    left: 0;
    top:20px;
}
#timeline .timeline-item:last-child{
    margin-bottom: 0;
}
}
