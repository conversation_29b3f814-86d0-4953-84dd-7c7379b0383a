var getCanvasTools = function () {
    function v2(x, y) {
        return {
            x: x || 0,
            y: y || 0
        };
    }
    v2.add = function (lhs, rhs) {
        return v2(lhs.x + rhs.x, lhs.y + rhs.y);
    };
    v2.sub = function (lhs, rhs) {
        return v2(lhs.x - rhs.x, lhs.y - rhs.y);
    };
    v2.mul = function (v, scaling) {
        return v2(v.x * scaling, v.y * scaling);
    };
    v2.div = function (v, scaling) {
        return v2(v.x / scaling, v.y / scaling);
    };
    v2.lerp = function (from, to, ratio) {
        return v2(from.x + (to.x - from.x) * ratio, from.y + (to.y - from.y) * ratio);
    };
    v2.sqrDistance = function (lhs, rhs) {
        var dx = lhs.x - rhs.x;
        var dy = lhs.y - rhs.y;
        return dx * dx + dy * dy;
    };
    v2.distance = function (lhs, rhs) {
        return Math.sqrt(v2.sqrDistance(lhs, rhs));
    };
    v2.dir = function (lhs, rhs) {
        var len = v2.distance(v2, { x: 0, y: 0 });
        return v2(v2.x / len, v2.y / len);
    };
    function Curve(points) {
        this.points = points || [];
        this.beziers = [];
        this.ratios = [];
        this.progresses = [];
        this.length = 0;
        this.computeBeziers();
    }
    Curve.prototype.computeBeziers = function () {
        this.beziers.length = 0;
        this.ratios.length = 0;
        this.progresses.length = 0;
        this.length = 0;
        for (var i = 1; i < this.points.length; i++) {
            var startPoint = this.points[i - 1];
            var endPoint = this.points[i];
            var bezier = new Bezier();
            bezier.start = startPoint.pos;
            bezier.startCtrlPoint = startPoint.out;
            bezier.end = endPoint.pos;
            bezier.endCtrlPoint = endPoint.in;
            this.beziers.push(bezier);
            this.length += bezier.getLength();
        }
        var current = 0;
        for (var i = 0; i < this.beziers.length; i++) {
            var bezier = this.beziers[i];
            this.ratios[i] = bezier.getLength() / this.length;
            this.progresses[i] = current = current + this.ratios[i];
        }

        return this.beziers;
    };
    function Bezier() {
        this.start = v2();
        this.end = v2();
        this.startCtrlPoint = v2(); // cp0, cp1
        this.endCtrlPoint = v2();   // cp2, cp3
    }
    // Get point at relative position in curve according to arc length
    // - u [0 .. 1]
    Bezier.prototype.getPointAt = function (u) {
        var t = this.getUtoTmapping(u);
        return this.getPoint(t);
    };
    function bezierAt(C1, C2, C3, C4, t) {
        var t1 = 1 - t;
        return C1 * t1 * t1 * t1 +
            C2 * 3 * t1 * t1 * t +
            C3 * 3 * t1 * t * t +
            C4 * t * t * t;
    }
    // Get point at time t
    //  - t [0 .. 1]
    Bezier.prototype.getPoint = function (t) {
        var x = bezierAt(this.start.x, this.startCtrlPoint.x, this.endCtrlPoint.x, this.end.x, t);
        var y = bezierAt(this.start.y, this.startCtrlPoint.y, this.endCtrlPoint.y, this.end.y, t);
        return new v2(x, y);
    };
    // Get total curve arc length
    Bezier.prototype.getLength = function () {
        var lengths = this.getLengths();
        return lengths[lengths.length - 1];
    };
    // Get list of cumulative segment lengths
    Bezier.prototype.getLengths = function (divisions) {
        if (!divisions) divisions = (this.__arcLengthDivisions) ? (this.__arcLengthDivisions) : 200;
        if (this.cacheArcLengths
            && (this.cacheArcLengths.length === divisions + 1)) {
            //console.log( "cached", this.cacheArcLengths );
            return this.cacheArcLengths;
        }
        var cache = [];
        var current, last = this.getPoint(0);
        var p, sum = 0;
        cache.push(0);
        for (p = 1; p <= divisions; p++) {
            current = this.getPoint(p / divisions);
            sum += v2.distance(current, last);
            cache.push(sum);
            last = current;
        }
        this.cacheArcLengths = cache;
        return cache; // { sums: cache, sum:sum }; Sum is in the last element.
    };
    Bezier.prototype.getUtoTmapping = function (u, distance) {
        var arcLengths = this.getLengths();
        var i = 0, il = arcLengths.length;
        var targetArcLength; // The targeted u distance value to get
        if (distance) {
            targetArcLength = distance;
        } else {
            targetArcLength = u * arcLengths[il - 1];
        }
        //var time = Date.now();
        // binary search for the index with largest value smaller than target u distance
        var low = 0, high = il - 1, comparison;
        while (low <= high) {
            i = Math.floor(low + (high - low) / 2); // less likely to overflow, though probably not issue here, JS doesn't really have integers, all numbers are floats
            comparison = arcLengths[i] - targetArcLength;
            if (comparison < 0) {
                low = i + 1;
                continue;
            } else if (comparison > 0) {
                high = i - 1;
                continue;
            } else {
                high = i;
                break;
                // DONE
            }
        }
        i = high;
        //console.log('b' , i, low, high, Date.now()- time);
        if (arcLengths[i] == targetArcLength) {
            var t = i / (il - 1);
            return t;
        }
        // we could get finer grain at lengths, or use simple interpolatation between two points
        var lengthBefore = arcLengths[i];
        var lengthAfter = arcLengths[i + 1];
        var segmentLength = lengthAfter - lengthBefore;
        // determine where we are between the 'before' and 'after' points
        var segmentFraction = (targetArcLength - lengthBefore) / segmentLength;
        // add that fractional amount to t
        var t = (i + segmentFraction) / (il - 1);
        return t;
    };

    function drawPoint(ctx, pos, strokeColor, fillColor, radius) {
        var radius = radius || 5;
        ctx.lineWidth = 1;
        ctx.strokeStyle = strokeColor;
        ctx.fillStyle = fillColor;
        ctx.beginPath();
        ctx.arc(pos.x, pos.y, radius, 0, Math.PI * 2, true);
        ctx.fill();
        ctx.stroke();
    }
    function drawLine(ctx, from, to, color) {
        ctx.lineWidth = 2;
        ctx.strokeStyle = color || "#000";
        ctx.beginPath();
        ctx.moveTo(from.x, from.y);
        ctx.lineTo(to.x, to.y);
        ctx.stroke();
    }
    function getBezierPos(bezier, progress) {
        var p01 = v2.lerp(bezier.start, bezier.startCtrlPoint, progress);
        var p12 = v2.lerp(bezier.startCtrlPoint, bezier.endCtrlPoint, progress);
        var p23 = v2.lerp(bezier.endCtrlPoint, bezier.end, progress);
        var p012 = v2.lerp(p01, p12, progress);
        var p123 = v2.lerp(p12, p23, progress);
        var p0123 = v2.lerp(p012, p123, progress);
        return p0123;
    }
    function drawEndPoint(ctx, pos) {
        ctx.beginPath();
        ctx.lineWidth = 6;
        ctx.lineCap = "round";
        ctx.moveTo(pos.x, pos.y);
        ctx.lineTo(pos.x, pos.y);
        ctx.stroke();
    }

    var Speed = 0.03;

    function animate(options) {
        var animateId = window.requestAnimationFrame(
            function () {
                animate(options)
            });
        var curve = options.curve;
        var ctx = options.context;
        var bezierProgress = options.bezierProgress;
        var bezierIndex = options.bezierIndex;
        var linePoints = options.linePoints;

        bezierProgress += Speed;
        if (bezierProgress > curve.progresses[bezierIndex]) {
            bezierIndex++;
            bezierIndex %= curve.beziers.length;
        }
        var realProgress = (bezierProgress - (bezierIndex > 0 ? curve.progresses[bezierIndex - 1] : 0)) / curve.ratios[bezierIndex];
        var pos = curve.beziers[bezierIndex].getPointAt(realProgress);
        if (pos.x == 0) {
            drawEndPoint(ctx, { x: options.endPos.x, y: options.endPos.y });
            window.cancelAnimationFrame(animateId);
            return;
        }

        if (linePoints.length < 2) {
            linePoints.push(pos);
        }

        if (linePoints.length == 2) {
            drawLine(ctx, linePoints[0], linePoints[1], options.color);
            linePoints.splice(0, 1);
        }

        options.bezierProgress = bezierProgress;
        options.bezierIndex = bezierIndex;
        options.linePoints = linePoints;
    }

    var reportCanvas = {
        initUpTrendChart:function (id,color) {
            var can = document.getElementById(id);


            if (can.getContext) {
                var initPoints = [
                    {
                        pos: v2(0, 26),
                        out: v2(10, 26)
                    },
                    {
                        in: v2(12, 8),
                        pos: v2(22, 8),
                        out: v2(30, 8)
                    },
                    {
                        in: v2(31, 21),
                        pos: v2(39, 21),
                        out: v2(47, 21)
                    },
                    {
                        in: v2(49, 0),
                        pos: v2(60, 2)
                    }
                ];

                var ctx = can.getContext('2d');
                ctx.clearRect(0, 0, can.width, can.height);

                var curve = new Curve(initPoints);
                animate({
                    color: color,
                    endPos: { x: 51, y: 7 },
                    curve: curve,
                    context: ctx,
                    bezierProgress: 0,
                    bezierIndex: 0,
                    linePoints: []
                });
            }
        },
        initDownTrendChart:function (id,color) {
            var can = document.getElementById(id);
            if(can.getContext) {
                var ctx = can.getContext('2d');
                ctx.clearRect(0, 0, can.width, can.height);

                var initPoints = [
                    {
                        pos: v2(0, 22),
                        out: v2(17, 21)
                    },
                    {
                        in: v2(20, 1),
                        pos: v2(29, 2),
                        out: v2(38, 0)
                    },
                    {
                        in: v2(41, 19),
                        pos: v2(59, 20)
                    }
                ];

                var curve = new Curve(initPoints);
                animate({
                    color: color, endPos: { x: 49, y: 17 }, curve: curve, context: ctx,
                    bezierProgress: 0,
                    bezierIndex: 0,
                    linePoints: []
                });
            }
        },
        initGentleTrendChart:function (id,color) {
            var can = document.getElementById(id);
            if(can.getContext) {
                var ctx = can.getContext('2d');
                ctx.clearRect(0, 0, can.width, can.height);

                var initPoints = [
                    {
                        pos: v2(0, 20),
                        out: v2(4, 20)
                    },
                    {
                        in: v2(3, 12),
                        pos: v2(6, 12),
                        out: v2(9, 12)
                    },
                    {
                        in: v2(8, 20),
                        pos: v2(11, 20),
                        out: v2(14, 20)
                    },
                    {
                        in: v2(15, 0),
                        pos: v2(19, 2),
                        out: v2(23, 0)
                    },
                    {
                        in: v2(23, 20),
                        pos: v2(28, 20),
                        out: v2(28, 20)
                    },
                    {
                        in: v2(57, 20),
                        pos: v2(57, 20)
                    }
                ];

                var curve = new Curve(initPoints);
                animate({
                    color: color, endPos: { x: 46, y: 20 }, curve: curve, context: ctx,
                    bezierProgress: 0,
                    bezierIndex: 0,
                    linePoints: []
                });

            }
        },
        initTrendChart:function (id,color,type) {
            $('#'+id).html("");
            switch (type){
                case 'up':
                    this.initUpTrendChart(id,color);
                    break;
                case 'down':
                    this.initDownTrendChart(id,color);
                    break;
                case 'gentle':
                    this.initGentleTrendChart(id,color);
                    break;
                default :
                    this.initGentleTrendChart(id,color);
                    break;
            }
        }
    }

    return reportCanvas;
}
