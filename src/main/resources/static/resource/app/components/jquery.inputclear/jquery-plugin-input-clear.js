/**
 * 需要在input的父级div增加class：xia-input-clear
 */
var XiaInputClear = {
	globalClass:".xia-input-clear",
	addClearButton: function(obj,fun){
		var html = '<i class="fa fa-times-circle-o" style="display:none;"></i>';
		obj.find("i.fa.fa-times-circle-o").remove();
		obj.append(html);
		var clearbutton = obj.find("i.fa.fa-times-circle-o");
		var input = obj.find("input[type='text']").first();
		input.mouseover(function(){
			clearbutton.show();
		});
		input.mouseout(function(){
			clearbutton.hide();
		});
		clearbutton.click(function(){
			obj.find("input").each(function(){
				var oldval = $(this).val();
				if(oldval){
					$(this).val("");
					//触发change事件
					$(this).trigger("change");
				}
				//兼容datepicker
				$(this).attr("title") && $(this).attr("title","");
			});
			if(fun!=undefined && fun!=null){
				fun();
			}
		});
		clearbutton.mouseover(function(){
			clearbutton.show();
		});
		clearbutton.mouseout(function(){
			clearbutton.hide();
		});
	},
	execute:function(){
		$(XiaInputClear.globalClass).each(function(){
			XiaInputClear.addClearButton($(this),null);
		});
	}
}
window.XiaInputClear = XiaInputClear;