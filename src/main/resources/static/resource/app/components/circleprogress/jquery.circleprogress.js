(function ($) {
    /*
     * Tween.js
     * t: current time����ǰʱ�䣩
     * b: beginning value����ʼֵ��
     * c: change in value���仯����
     * d: duration������ʱ�䣩
     */
    var Tween = {
        Linear: function(t, b, c, d) { return c*t/d + b; },
        Quad: {
            easeIn: function(t, b, c, d) {
                return c * (t /= d) * t + b;
            },
            easeOut: function(t, b, c, d) {
                return -c *(t /= d)*(t-2) + b;
            },
            easeInOut: function(t, b, c, d) {
                if ((t /= d / 2) < 1) return c / 2 * t * t + b;
                return -c / 2 * ((--t) * (t-2) - 1) + b;
            }
        },
        Cubic: {
            easeIn: function(t, b, c, d) {
                return c * (t /= d) * t * t + b;
            },
            easeOut: function(t, b, c, d) {
                return c * ((t = t/d - 1) * t * t + 1) + b;
            },
            easeInOut: function(t, b, c, d) {
                if ((t /= d / 2) < 1) return c / 2 * t * t*t + b;
                return c / 2*((t -= 2) * t * t + 2) + b;
            }
        },
        Quart: {
            easeIn: function(t, b, c, d) {
                return c * (t /= d) * t * t*t + b;
            },
            easeOut: function(t, b, c, d) {
                return -c * ((t = t/d - 1) * t * t*t - 1) + b;
            },
            easeInOut: function(t, b, c, d) {
                if ((t /= d / 2) < 1) return c / 2 * t * t * t * t + b;
                return -c / 2 * ((t -= 2) * t * t*t - 2) + b;
            }
        },
        Quint: {
            easeIn: function(t, b, c, d) {
                return c * (t /= d) * t * t * t * t + b;
            },
            easeOut: function(t, b, c, d) {
                return c * ((t = t/d - 1) * t * t * t * t + 1) + b;
            },
            easeInOut: function(t, b, c, d) {
                if ((t /= d / 2) < 1) return c / 2 * t * t * t * t * t + b;
                return c / 2*((t -= 2) * t * t * t * t + 2) + b;
            }
        },
        Sine: {
            easeIn: function(t, b, c, d) {
                return -c * Math.cos(t/d * (Math.PI/2)) + c + b;
            },
            easeOut: function(t, b, c, d) {
                return c * Math.sin(t/d * (Math.PI/2)) + b;
            },
            easeInOut: function(t, b, c, d) {
                return -c / 2 * (Math.cos(Math.PI * t/d) - 1) + b;
            }
        },
        Expo: {
            easeIn: function(t, b, c, d) {
                return (t==0) ? b : c * Math.pow(2, 10 * (t/d - 1)) + b;
            },
            easeOut: function(t, b, c, d) {
                return (t==d) ? b + c : c * (-Math.pow(2, -10 * t/d) + 1) + b;
            },
            easeInOut: function(t, b, c, d) {
                if (t==0) return b;
                if (t==d) return b+c;
                if ((t /= d / 2) < 1) return c / 2 * Math.pow(2, 10 * (t - 1)) + b;
                return c / 2 * (-Math.pow(2, -10 * --t) + 2) + b;
            }
        },
        Circ: {
            easeIn: function(t, b, c, d) {
                return -c * (Math.sqrt(1 - (t /= d) * t) - 1) + b;
            },
            easeOut: function(t, b, c, d) {
                return c * Math.sqrt(1 - (t = t/d - 1) * t) + b;
            },
            easeInOut: function(t, b, c, d) {
                if ((t /= d / 2) < 1) return -c / 2 * (Math.sqrt(1 - t * t) - 1) + b;
                return c / 2 * (Math.sqrt(1 - (t -= 2) * t) + 1) + b;
            }
        },
        Elastic: {
            easeIn: function(t, b, c, d, a, p) {
                var s;
                if (t==0) return b;
                if ((t /= d) == 1) return b + c;
                if (typeof p == "undefined") p = d * .3;
                if (!a || a < Math.abs(c)) {
                    s = p / 4;
                    a = c;
                } else {
                    s = p / (2 * Math.PI) * Math.asin(c / a);
                }
                return -(a * Math.pow(2, 10 * (t -= 1)) * Math.sin((t * d - s) * (2 * Math.PI) / p)) + b;
            },
            easeOut: function(t, b, c, d, a, p) {
                var s;
                if (t==0) return b;
                if ((t /= d) == 1) return b + c;
                if (typeof p == "undefined") p = d * .3;
                if (!a || a < Math.abs(c)) {
                    a = c;
                    s = p / 4;
                } else {
                    s = p/(2*Math.PI) * Math.asin(c/a);
                }
                return (a * Math.pow(2, -10 * t) * Math.sin((t * d - s) * (2 * Math.PI) / p) + c + b);
            },
            easeInOut: function(t, b, c, d, a, p) {
                var s;
                if (t==0) return b;
                if ((t /= d / 2) == 2) return b+c;
                if (typeof p == "undefined") p = d * (.3 * 1.5);
                if (!a || a < Math.abs(c)) {
                    a = c;
                    s = p / 4;
                } else {
                    s = p / (2  *Math.PI) * Math.asin(c / a);
                }
                if (t < 1) return -.5 * (a * Math.pow(2, 10* (t -=1 )) * Math.sin((t * d - s) * (2 * Math.PI) / p)) + b;
                return a * Math.pow(2, -10 * (t -= 1)) * Math.sin((t * d - s) * (2 * Math.PI) / p ) * .5 + c + b;
            }
        },
        Back: {
            easeIn: function(t, b, c, d, s) {
                if (typeof s == "undefined") s = 1.70158;
                return c * (t /= d) * t * ((s + 1) * t - s) + b;
            },
            easeOut: function(t, b, c, d, s) {
                if (typeof s == "undefined") s = 1.70158;
                return c * ((t = t/d - 1) * t * ((s + 1) * t + s) + 1) + b;
            },
            easeInOut: function(t, b, c, d, s) {
                if (typeof s == "undefined") s = 1.70158;
                if ((t /= d / 2) < 1) return c / 2 * (t * t * (((s *= (1.525)) + 1) * t - s)) + b;
                return c / 2*((t -= 2) * t * (((s *= (1.525)) + 1) * t + s) + 2) + b;
            }
        },
        Bounce: {
            easeIn: function(t, b, c, d) {
                return c - Tween.Bounce.easeOut(d-t, 0, c, d) + b;
            },
            easeOut: function(t, b, c, d) {
                if ((t /= d) < (1 / 2.75)) {
                    return c * (7.5625 * t * t) + b;
                } else if (t < (2 / 2.75)) {
                    return c * (7.5625 * (t -= (1.5 / 2.75)) * t + .75) + b;
                } else if (t < (2.5 / 2.75)) {
                    return c * (7.5625 * (t -= (2.25 / 2.75)) * t + .9375) + b;
                } else {
                    return c * (7.5625 * (t -= (2.625 / 2.75)) * t + .984375) + b;
                }
            },
            easeInOut: function(t, b, c, d) {
                if (t < d / 2) {
                    return Tween.Bounce.easeIn(t * 2, 0, c, d) * .5 + b;
                } else {
                    return Tween.Bounce.easeOut(t * 2 - d, 0, c, d) * .5 + c * .5 + b;
                }
            }
        }
    }
    Math.tween = Tween;
    var circleAnimates={};

    $.fn.circleProgress = function (options) {
        var me = this;

        var defualts = {
                title: {
                    text: "",
                    fontSize: 14,
                    fontFamily: "Microsoft YaHei",
                    fontColor: "#ccc",
                    margin: "0 0 0 0"
                },
                value: {
                    text: "",
                    fontSize: 18,
                    fontFamily: "Microsoft YaHei",
                    fontColor: "#99c827",
                    formatter: function (value) {
                        return value;
                    },
                    margin: "0 0 0 0"
                },
                border: {
                    width: 5,
                    color: '#99c827',
                    bgColor:'#F6F6F6'
                },
                backgroundColor:"#fff",
                raduis: 80,
                min: 0,
                max: 100,
                animation:{duration:800}
            },
            options = $.extend(true, {}, defualts, options);

        var width = this.width(),
            height = this.height(),
            center={
                x:width/2,
                y:height/2,
            };

        var scale= height/2>=90?1: height/2/90,
            significantDigits=0;

        options.raduis*=scale;
        options.title.fontSize*=scale;
        options.value.fontSize*=scale;
        options.border.width*=scale;


        me.canvasContext = {};

        me.init = function () {
            var c=this.find("canvas"),
                animateFunc=circleAnimates[me.attr("id")];

            if(c.length==0) {
                c = $("<canvas>").attr("id", this.attr("id") + "_canvas")
                    .attr({"width": width*2, "height": height*2})
                    .css("width",width).css("height",height);
                this.html("");
                this.append(c);
            }
            else{
                c.attr({"width": width*2, "height": height*2}).css("width",width).css("height",height);
            }

            if(animateFunc){
                window.cancelAnimationFrame(animateFunc);
            }

            var process = 0,
                ctx = c[0].getContext('2d');

            ctx.scale(2,2);
            me.canvasContext = ctx;
            me.process = 0;

            ctx.clearRect(0,0, c[0].width,c[0].height);

            ctx.beginPath();
            ctx.arc(center.x, center.y, options.raduis, 0, Math.PI * 2);
            ctx.closePath();
            ctx.fillStyle = options.border.bgColor;
            ctx.fill();

            if (options.max == options.min) {
                me.process = 100
            }

            if(/\d+/.test(options.value.text)){
                me.total= parseFloat(options.value.text);
                var splitValues=options.value.text.toString().split('.');
                if( splitValues.length>1){
                    significantDigits = splitValues[1].length;
                }
            }

            me.rectMeasure = measureText(ctx);

            animate();
        }

        function animate() {
            var start = new Date().getTime();
            var runTime=0;
            var _run = function() {
                runTime = new Date().getTime() - start;

                var runTimeValue = Tween.Linear(runTime, 0, me.total, options.animation.duration);
                me.process = Tween.Linear(runTime, options.min/options.max*100,me.total/options.max*100, options.animation.duration);

                if(runTimeValue>me.total){
                    runTimeValue=me.total;
                }
                if(me.process > me.total){
                    if(options.min==options.max){
                        me.process=options.max;
                    }
                    else{
                        me.process = me.total;
                    }
                }

                runTimeValue= significantDigits>0?runTimeValue.toFixed(significantDigits): parseInt(runTimeValue);

                drawCricle(me.canvasContext,me.process);
                drawTitle(me.canvasContext);
                drawValue(me.canvasContext, runTimeValue);

                if (runTime < options.animation.duration) {
                   var animateFunc= requestAnimationFrame(_run);
                    circleAnimates[me.attr("id")]= animateFunc;
                }
            };

            if(me.total){
                _run();
            }
            else{
                drawCricle(me.canvasContext,  me.process);
                drawTitle(me.canvasContext);
                drawValue(me.canvasContext, options.value.text);
            }
        }

        function measureText(ctx) {
            ctx.font = buildFont(options.title);
            var titleMeasure = ctx.measureText(options.title.text);
            var titleMargin = buildMargin(options.title.margin, options.title.text);

            ctx.font = buildFont(options.value);
            var valueMeasure = ctx.measureText(options.value.text);
            var valueMargin = buildMargin(options.value.margin, options.value.text);

            var titleHeight= options.title.text===""?0: options.title.fontSize ;
            var valueHeight= options.value.text===""?0: options.value.fontSize ;

            var rectMeasure = {
                width: titleMeasure.width > valueMeasure.width ? titleMeasure.width : valueMeasure.width,
                height: titleHeight + valueHeight
                + titleMargin.top + titleMargin.bottom
                + valueMargin.top + valueMargin.bottom
            };
            return rectMeasure;
        }

        function buildMargin(margin,value) {
            var marginValues = margin.split(" ");
            if(value===""){
                return {
                    top:0, right:0, bottom:0, left:0
                }
            }
            return {
                top: parseInt(marginValues[0]),
                right: parseInt(marginValues[1]),
                bottom: parseInt(marginValues[2]),
                left: parseInt(marginValues[3]),
            }
        }

        function drawCricle(ctx, percent) {
            ctx.beginPath();
            ctx.moveTo(center.x, center.y);
            ctx.arc(center.x, center.y, options.raduis, Math.PI * 1.5, Math.PI * (1.5 + 2 * percent / 100));
            ctx.closePath();
            ctx.fillStyle = options.border.color;
            ctx.fill();

            ctx.beginPath();
            ctx.arc(center.x, center.y, options.raduis - options.border.width, 0, Math.PI * 2);
            ctx.closePath();
            ctx.fillStyle = options.backgroundColor;// '#fff';
            ctx.fill();
        }

        function drawTitle(ctx) {
            ctx.font = buildFont(options.title);
            ctx.fillStyle = options.title.fontColor;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            //ctx.moveTo(100, 200 - me.rectMeasure.height / 2);
            ctx.fillText(options.title.text, center.x, (height - me.rectMeasure.height)/2 + options.title.fontSize/2);

        }

        function drawValue(ctx, runtimeValue) {
            var titleMargin = buildMargin(options.title.margin),
                valueMargin = buildMargin(options.value.margin),
                titleHeight=options.title.text===""?0: options.title.fontSize,
                formatter=options.value.formatter,
                formatterText=runtimeValue;

            ctx.beginPath();
            ctx.font = buildFont(options.value);
            ctx.fillStyle = options.value.fontColor;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            if (typeof formatter === 'function'){
                formatterText=options.value.formatter.call(this, runtimeValue);
            }

            ctx.fillText(formatterText, center.x,
                (height - me.rectMeasure.height) / 2 + valueMargin.top + titleMargin.top + titleMargin.bottom
                + titleHeight + options.value.fontSize / 2
            );
        }

        function buildFont(fontStyle) {
            return fontStyle.fontSize + "px " + fontStyle.fontFamily;
        }


        me.init();


    }
})(jQuery);
