/**
 * 依赖jquery
 * MagicSuggest.js
 */
var XiaAutoComplete = function(selector,options){
	var defaultParams = {
        	data:"/common/tabledata/",
        	dataUrlParams:{
        		columns:[
        		        // {name: "name", searchText: ""}
        		 ],
        		offset: 0,
        		pageNumber: 1,
        		pageSize: 999,
        		conditions:null
        	},
        	//renderer:function(v){};
        	//placeholder:"输入",
        	//name:"",
            //resultAsString: true,
            maxSelection: 1,
            //allowFreeEntries:false,
            //valueField:"id",
            //resultsField:"data",
            //hideTrigger:false,
            //maxSuggestions:10,
        	displayField:'name',
        	mode:"remote",
        	maxSelectionRenderer:function(v){
        		return "";
        	}
        	
	};
	var isColumnDataNull = function(data,key){
		var _clumn_data_null = false;
		//不检测，分隔的
		if(key.indexOf(",") == -1){
			var _clumn_data = key.split(".");
			if (_clumn_data.length > 1) {
				var _clumn_data_key = "data";
				for(var _cd_i in _clumn_data){
					_clumn_data_key += "."+_clumn_data[_cd_i]
					if (eval(_clumn_data_key) == null) {
						_clumn_data_null = true;
						break;
					}
				}
			}else{
				if (eval("data."+key) == null) {
					_clumn_data_null = true;
				}
			}
		}
		return _clumn_data_null;
	}
	//url
	if(undefined != options['repository']){
		defaultParams.data += options['repository']+"/likesearch";
	}
	//自定义
	if(undefined != options['ajax']){
		defaultParams.data = options['ajax'];
	}
	if(undefined != options['column']){
		var placeholder = "";
		var searchType = options['column']["isInt"]?"int":"";
		
		defaultParams.dataUrlParams.columns[0] = {"name":options['column']["field"],"searchText":"","orderType":searchType};
		placeholder += options['column']["title"]+"，";
			
		defaultParams.placeholder = placeholder.substr(0,placeholder.length-1);
		defaultParams.displayField = options['column']["field"];
		
		defaultParams.renderer = function(el){
			if(!isColumnDataNull(el,options['column']["field"])){
				return eval("el."+options['column']["field"]);
			}
		} 
		defaultParams.selectionRenderer = function(el){
			if(!isColumnDataNull(el,options['column']["field"])){
				return eval("el."+options['column']["field"]);
			}
		}
	}
	if(undefined != options['columns']){
		var placeholder = "";
		$.each(options['columns'],function(index,option){
			var searchType = options['columns'][index]["isInt"]?"int":"";
			defaultParams.dataUrlParams.columns[index] = {"name":options['columns'][index]["field"],"searchText":"","orderType":searchType};
			placeholder += options['columns'][index]["title"]+"，";
		});
			
		defaultParams.placeholder = placeholder.substr(0,placeholder.length-1);
		defaultParams.displayField = options['columns'][0]["field"];
		//处理展示文本
		var getTextDefaultWithColumns=function(el){
			var text = "";
			$.each(options['columns'],function(index,option){
				var field = options['columns'][index]["field"];
				if(!isColumnDataNull(el,field)){
					text += eval("el."+field)+"，";
				}
			});
			return text.substr(0,text.length-1);
		}
		//下拉选项文本
		defaultParams.renderer = function(el){
			return getTextDefaultWithColumns(el);
		} 
		//添加到输入框选中后文本
		defaultParams.selectionRenderer = function(el){
			return getTextDefaultWithColumns(el);
		}
	}
	if(undefined != options['conditions']){
		defaultParams.dataUrlParams.conditions = options['conditions'];
	}
	var settings = jQuery.extend({}, defaultParams, options);
	this.autocomplete = $(selector).magicSuggest(settings);
	if(undefined != options['onSelectionChange']){
		$(this.autocomplete).on("selectionchange",function(e,obj,records){
			options['onSelectionChange'](records);
		});
	}
	if(undefined != options['onSelectionChangeMData']){
		$(this.autocomplete).on("selectionchange",function(e,obj,records){
			options['onSelectionChangeMData'](e,obj,records);
		});
	}
}
window.XiaAutoComplete = XiaAutoComplete;
