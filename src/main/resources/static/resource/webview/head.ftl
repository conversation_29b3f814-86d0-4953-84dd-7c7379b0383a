<#assign  _STATIC_RE_="${baseStaticPath}/resource">
<meta charset="UTF-8">
<meta name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<link rel="shortcut icon" href="${baseStaticPath}/favicon.ico"/>
<link rel="bookmark" href="${baseStaticPath}/favicon.ico"/>
<link href="${_STATIC_RE_}/css/cloud-admin.css${_STATIC_VERSION_}" type="text/css" rel="stylesheet" />
<link href="${_STATIC_RE_}/css/style.css${_STATIC_VERSION_}" type="text/css" rel="stylesheet" />
<link href="${_STATIC_RE_}/css/themes/default.css${_STATIC_VERSION_}" type="text/css" rel="stylesheet" id="skin-switcher" />
<link href="${_STATIC_RE_}/css/responsive.css${_STATIC_VERSION_}" type="text/css" rel="stylesheet" />
<link href="${_STATIC_RE_}/css/themes/standard.css${_STATIC_VERSION_}" type="text/css" rel="stylesheet" />
<link href="${_STATIC_RE_}/css/fmone-v2/index.css${_STATIC_VERSION_}" type="text/css" rel="stylesheet" />
<script type="text/javascript">
    var getCurrentProject = function () {
        var finalCurrentProject = null;
        var currentproject = localStorage.current_project;
        var currentprojectFromUrl = FO.get_uri_param("current_project");
        if(!currentproject&&currentprojectFromUrl){
            finalCurrentProject = currentprojectFromUrl;
        }else if(!currentprojectFromUrl&&currentproject){
            currentprojectFromUrl = 0;
            finalCurrentProject = currentprojectFromUrl;
        }else{
            if(currentproject!=currentprojectFromUrl){
                finalCurrentProject = currentprojectFromUrl;
            }else{
                if (currentprojectFromUrl == undefined) {
                    currentprojectFromUrl = 0;
                }
                if (currentproject == undefined) {
                    currentproject = 0;
                }
                finalCurrentProject = currentproject;
            }
        }
        return finalCurrentProject;
    };
    var PUBLIC_PATH = '${baseStaticPath}';
</script>

<script type="text/javascript">
    window.iframeAutoHeight = function() {
        try {
            var iframe= window.top.window.document.querySelectorAll("iframe")[0];
            if(navigator.userAgent.indexOf("MSIE")>0||navigator.userAgent.indexOf("rv:11")>0||navigator.userAgent.indexOf("Firefox")>0){
                iframe.height=iframe.contentWindow.document.body.scrollHeight;
            }else{
                iframe.height=iframe.contentWindow.document.documentElement.scrollHeight;
            }
        } catch (e) {

        }
    }
</script>
