:root {
    /* Colors */
}
@font-face{
    font-family: 'ltjt';
    src : url('fonts/兰亭黑简.ttf');
}
@font-face{
    font-family: 'FZLTZHK';
    src : url('fonts/FZLTZHK.ttf');
}
@font-face{
    font-family: 'FZLTZCHK';
    src : url('fonts/FZLTZCHK.ttf');
}
@font-face{
    font-family: 'FZLTCHJ';
    src : url('fonts/方正兰亭粗黑简.ttf');
}
ul, li {
    list-style: none;
    padding: 0;
    margin: 0;
}
a {
    text-decoration: none;
}
.clearfix:before, .clearfix:after {
    content: " ";
    display: table;
}
.clearfix:after {
    clear: both;
}
*, *:before, *:after {
    box-sizing: border-box;
}
.text-link {
    color: #149bd9;
}
.text-normal {
    color: #666;
}
.table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    margin-bottom: 20px;
}
th {
    text-align: left;
}
.table > thead > tr > th {
    vertical-align: bottom;
    border-bottom: 1px solid #dddddd;
}
.table-bordered > thead > tr > th, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > td, .table-bordered {
    border: 1px solid #ddd;
}
input::-ms-clear {
    display: none;
}
input::-ms-reveal {
    display: none;
}
@charset "UTF-8";
:root {
    /* Colors */
}
.header {
    color: #666;
    background-color: #fff;
    height: 60px;
}
.header-logo {
    display: block;
    /*background: url(./images/logo_bg.png) no-repeat;*/
    background-position: 30% 50%;
    /*border-right: 1px solid #e7eaec;*/
    height: 100%;
    width: 220px;
    float: left;
}
.header-logo-narrow {
    background-position: 50% 50%;
    width: 50px;
    background-size: 36px !important;
}
.header-menu {
    /*border-bottom: 1px solid #e7eaec;*/
    margin-left: 220px;
    height: 60px;
}
.header-menu-narrow {
    margin-left: 50px;
}
.header-narrow {
    float: left;
    cursor: pointer;
}
.header-narrow .fa {
    font-size: 20px;
    line-height: 60px;
    padding: 0 20px;
}
.header-narrow:hover {
    color: #333;
}
.header-nav {
    float: right;
}
.header-nav-item {
    float: left;
    height: 100%;
    line-height: 60px;
    padding: 0 20px;
    border-left: 1px solid transparent;
    border-right: 1px solid transparent;
}
.header-nav-item .fa:first-child {
    margin-right: 10px;
}
.header-nav-item.open {
    background-color: #f3f3f3;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
}
.header-nav-item:hover {
    cursor: pointer;
    color: #333;
    background-color: #f3f3f3;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
}

.header-nav-item .header-notification .el-badge__content {
    background-color: #ff9e36;
    position: absolute;
    left: 5px;
    right: auto;
    transform:translateY(-50%);
}

.header-icon-narrow {
    cursor: pointer;
    line-height: 60px;
}
.header-avatar {
    position: relative;
}
.header-avatar-image {
    width: 32px;
    height: 32px;
    background-color: #ccc;
    border: 1px solid #e7eaec;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    margin-top: -16px;
    text-align: center;
    line-height: 32px;
}
.header-avatar-image img{
    border-radius: 50%;
}
.header-avatar-image .fa.fa-user {
    margin-right: 0;
}
.header-avatar-info {
    margin-left: 45px;
}
.header-logo--narrow {
    width: 50px;
}
.header-collapse .header-logo {
    background: url(images/logo-collapse.png) no-repeat;
    background-position: 50%;
    margin-left: 0;
    width: 50px;
}
.header-collapse .header-menu {
    margin-left: 50px;
}
.header-popover {
    padding: 2px;
}
.header-project {}
.header-project-main {
    height: 30px;
    line-height: 28px;
    margin-bottom: 20px;
    padding-left: 12px;
}
.header-project-main-link {
    border: 1px solid #e5e5e5;
    /*width: 80px;*/
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    border-radius: 3px;
    font-size: 14px;
    margin-left: 20px;
    color: #999;
    padding:0 11px;
}
.header-project-main-link.header-project-main-selected {
    background-color: #1ab394;
    border: 1px solid #1ab394;
    color: #fff;
}
.header-project-main-link:hover {
    border: 1px solid #1ab394;
    cursor: pointer;
    color: #1ab394;
}
.header-project-main-link:hover a {
    color: #1ab394;
}
.header-project-group {
    border-bottom: 1px solid #e5e5e5;
    color: #333;
    font-size: 14px;
    font-weight: 700;
    padding: 8px 0;
    margin-left: 12px;

}
.header-project-items {
    padding: 10px 0;
}
/*.header-project-items .new-line {
    margin-left: 0;
}
.header-project-items .end-line {
    margin-right: 0;
}*/
.header-project-item {
    border: 1px solid #e5e5e5;
    height: 30px;
    line-height: 28px;
    width: 120px;
    float: left;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-radius: 2px;
    margin-left: 14px;
    margin-right: 14px;
    margin-top: 10px;
    margin-bottom: 10px;
    padding: 0 6px;
}
.header-project-item:hover {
    border: 1px solid #1ab394;
    background: #fff;
    cursor: pointer;
}
.header-project-item:hover a {
    color: #1ab394;
}
.header-project-item-selected {
    background-color: #1ab394;
    border: 1px solid #1ab394;
}
.header-project-item-selected a {
    color: #fff;
}
.header-project-link {
    color: #999;
    font-size: 14px;
}
.header-notification {}
.header-notification-popover {
    padding: 0 0 20px;
}
.header-notification-item {
    border-bottom: 1px solid #e5e5e5;
    font-size: 14px;
    padding: 18px 20px 14px 20px;
    color: #666;
}
.header-notification-item:hover {
    background-color: #f3f3f3;
    cursor: pointer;
}
.header-notification-item:first-child {}
.header-notification-item-image {
    float: left;
    border: 1px solid #ccc;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    text-align: center;
    line-height: 34px;
}
.header-notification-item-content {
    line-height: 34px;
}
.header-notification-item-message {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 50px;
}
.header-notification-item-time {
    font-size: 12px;
    color: #999;
    text-align: right;
}
.header-notification-more {
    color: #666;
    text-align: center;
    padding: 20px 0 0px;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
}
.header-notification-more .fa-angle-right {
    color: #999;
    margin-left: 8px;
    font-size: 16px;
}
.header-notification-more:hover {
    color: #1ab394;
}
.header-notification-more:hover .fa-angle-right {
    color: #1ab394;
}
.header-user {
    border-bottom: 1px solid #e5e5e5;
    padding: 20px;
    font-size: 14px;
}
.header-user-popover {
    padding: 0;
}
.header-user-image {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    border: 1px solid #e5e5e5;
    float: left;
}
.header-user-info {
    margin-left: 130px;
    position: relative;
    height: 96px;
}
.header-user-name {
    font-size: 16px;
    padding-top: 20px;
}
.header-user-links {
    padding: 0 20px 20px 20px;
}
.header-user-links-group {
    border-bottom: 1px solid #e5e5e5;
    padding: 30px 0 8px;
}
.header-user-links-group:last-child {
    border-bottom-width: 0;
}
.header-user-link {
    padding: 8px 0;
}
.header-user-link a {
    font-size: 14px;
    color: #666;
}
.header-user-link a:hover {
    color: #1ab394;
}
.header-user-link:before {
    content: " ";
    border: 1px solid #999;
    width: 11px;
    height: 11px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 14px;
    color: #999;
}
.header-logout {
    color: #ff5b5b;
    position: absolute;
    bottom: 0;
    right: 0;
}
.header-logout a {
    color: #ff5b5b;
}
@charset "UTF-8";
:root {
    /* Colors */
}
.menu {
    color: #fff;
    height: 100%;
    /*background-color: #2F4050;
    border-right: 1px solid #2F4050;*/
    position: absolute;
    left: 0;
    width: 220px;
}
.menu-narrow {
    width: 50px;
}
.el-submenu__title .el-submenu__icon-arrow {
    margin-top: -4px;
}
.menu-narrow .el-menu-item__title-label, .menu-narrow .el-submenu__title-label, .menu-narrow .el-submenu__icon-arrow {
    display: none;
}
.menu-narrow .el-menu-item, .menu-narrow .el-submenu__title {
    padding: 13px 20px 14px 16px;
}
.menu a {
    color: #a4b8c6;
    text-decoration: none;
    display: inline-block;
}
.menu i.fa {
    width: 16px;
    height: 16px;
    vertical-align: top;
    margin-right: 10px;
    line-height: 25px;
    margin-top: 2px;
    filter: brightness(0) saturate(100%) invert(14%) sepia(9%) saturate(1041%) hue-rotate(353deg) brightness(93%) contrast(93%);
}
.menu .is-opened {}
.menu .is-opened i.s-menu-asset {
    background-image: url(images/common/icon_asset.png);
}
.menu .is-opened i.s-menu-contract {
    background-image: url(images/common/icon_contract.png);
}

/*.menu .is-opened i.s-menu-home {*/
    /*background-image: url(images/common/icon_home.png);*/
/*}*/
.menu .el-menu-item.is-active i.s-menu-home{
    background-image: url(images/common/icon_home.png);
}

.menu .is-opened i.s-menu-visitor {
    background-image: url(images/common/icon_visitor.png);
}
.menu .is-opened i.s-menu-parking {
    background-image: url(images/common/cl_icon2.png);
}

.menu .is-opened i.s-menu-bulletin {
    background-image: url(images/common/icon_bulletin.png);
}

.menu .is-opened i.s-menu-service {
    background-image: url(images/common/icon_service.png);
}

.menu .is-opened i.s-menu-workorder {
    background-image: url(images/common/icon_wo.png);
}

.menu .is-opened i.s-menu-epayment {
    background-image: url(images/common/icon_payment.png);
}

.menu .is-opened i.s-menu-sign {
    background-image: url(images/common/icon_sign.png);
}

.menu .is-opened i.s-menu-patrol {
    background-image: url(images/common/icon_patrol.png);
}

.menu .is-opened i.s-menu-preventive {
    background-image: url(images/common/icon_pm.png);
}

.menu .is-opened i.s-menu-undertake {
    background-image: url(images/common/icon_under.png);
}

.menu .is-opened i.s-menu-seedling {
    background-image: url(images/common/icon_seedling.png);
}

.menu .is-opened i.s-menu-seedlingIssues {
    background-image: url(images/common/icon_seedlingIssues.png);
}

.menu .is-opened i.s-menu-inventory {
    background-image: url(images/common/icon_inventory.png);
}

.menu .is-opened i.s-menu-vendor {
    background-image: url(images/common/icon_vendor.png);
    width: 17px;
    margin-right: 9px;
}

.menu .is-opened i.s-menu-energy {
    background-image: url(images/common/icon_energy.png);
}

.menu .is-opened i.s-menu-report {
    background-image: url(images/common/icon_report.png);
}

.menu .is-opened i.s-menu-knowledge {
    background-image: url(images/common/icon_kb.png);
}

.menu .is-opened i.s-menu-organize {
    background-image: url(images/common/icon_org.png);
}

.menu .is-opened i.s-menu-user {
    background-image: url(images/common/icon_user.png);
}

.menu .is-opened i.s-menu-system {
    background-image: url(images/common/icon_sys.png);
}

.menu .is-opened i.fa-th-large {
    background-image: url(images/common/icon_projectm.png);
}

.menu .is-opened i.s-menu-ezviz {
    background-image: url(images/common/icon_ezviz.png);
}

.menu .is-opened i.s-menu-initialize {
    background-image: url(images/common/icon_initialize.png);
}

.menu i.s-menu-home {
    background-image: url(images/common-img/icon_home.png);
}
.menu i.s-menu-bulletin {
    background-image: url(images/common-img/icon_bulletin.png);
}
.menu i.s-menu-service {
    background-image: url(images/common-img/icon_service.png);
}
.menu i.s-menu-workorder {
    background-image: url(images/common-img/icon_wo.png);
}
.menu i.s-menu-sign {
    background-image: url(images/common-img/icon_sign.png);
}
.menu i.s-menu-patrol {
    background-image: url(images/common-img/icon_patrol.png);
}
.menu i.s-menu-preventive {
    background-image: url(images/common-img/icon_pm.png);
}
.menu i.s-menu-asset {
    background-image: url(images/common-img/icon_asset.png);
}
.menu i.s-menu-monitoring {
    background-image: url(images/common-img/icon_monitoring.png);
}
.menu i.s-menu-contract {
    background-image: url(images/common-img/icon_contract.png);
}
.menu i.s-menu-vendor {
    background-image: url(images/common-img/icon_vendor.png);
    width: 17px;
    margin-right: 9px;
}
.menu i.s-menu-inventory {
    background-image: url(images/common-img/icon_inventory.png);
}
.menu i.s-menu-energy {
    background-image: url(images/common-img/icon_energy.png);
}
.menu i.s-menu-knowledge {
    background-image: url(images/common-img/icon_kb.png);
}
.menu i.s-menu-report {
    background-image: url(images/common-img/icon_report.png);
}
.menu i.s-menu-organize {
    width: 17px;
    background-image: url(images/common-img/icon_org.png);
}
.menu i.s-menu-system {
    background-image: url(images/common-img/icon_sys.png);
}
.menu i.s-menu-user {
    background-image: url(images/common-img/icon_user.png);
}
.menu i.s-menu-epayment {
    background-image: url(images/common-img/icon_payment.png);
}
.menu i.s-menu-visitor {
    width: 17px;
    background-image: url(images/common-img/icon_visitor.png);
}
.menu i.s-menu-parking {
    width: 17px;
    background-image: url(images/common-img/cl_icon1.png);
}
.menu i.s-menu-undertake {
    background-image: url(images/common-img/icon_under.png);
}
.menu i.s-menu-seedling {
    background-image: url(images/common-img/icon_seedling.png);
}
.menu i.s-menu-seedlingIssues {
    background-image: url(images/common-img/icon_seedlingIssues.png);
}
.menu i.fa-th-large {
    background-image: url(images/common-img/icon_projectm.png);
}
.menu i.s-menu-ezviz {
    background-image: url(images/common-img/icon_ezviz02.png);
}
.menu i.s-menu-initialize {
    background-image: url(images/common-img/icon_initialize.png);
}
.menu i.s-menu-decision {
    background-image: url(images/common-img/icon-decision.png);
}
.menu .is-opened i.s-menu-decision {
    background-image: url(images/common/icon-decision.png);
}
.menu i.fa-th-large:before {
    content: "";
}
.menu .el-menu-item {
    border-left: 0px solid transparent;
}
.menu .collapse-transition .el-menu-item.is-active {
    border-right: 3px solid #00ACB7;
    background: #E1FAFF;
    color: #00ACB7;
}

.menu>ul>li.el-menu-item.is-active:first-child {
    text-indent: -3px;
}

.menu .el-menu-item.is-active a {
    color: #fff;
    text-indent: -3px;
}
.menu-popper .el-menu {
    border-radius: 0;
}
.el-submenu .el-submenu .el-menu-item {
    padding-left: 66px;
    padding-right: 0;
}
.el-submenu .el-submenu .el-submenu__title {
    padding-left: 50px;
    padding-right: 0;
}
.el-menu--dark .el-menu-item, .el-menu--dark .el-submenu__title {
    height: auto;
    line-height: normal;
    line-height: initial;
    white-space: normal;
}
.el-menu-item, .el-submenu__title {
    padding: 13px 20px 14px 20px;
}
.el-submenu .el-menu-item {
    padding: 13px 0px 14px 50px;
}
@charset "UTF-8";
:root {
    /* Colors */
}
.main-content {
    margin-left: 220px;
    background-color: #F3F3F4;
}
.main-content-narrow {
    margin-left: 50px;
}
:root {
    /* Colors */
}
.page {
    background-color: #fff;
}
.page-head {
    overflow: auto;
    zoom: 1;
    line-height: 59px;
    border-bottom: 1px solid #e7eaec;
}
.page-head-title {
    padding: 0 25px;
    float: left;
    color: #666;
}
.page .title-buttons {
    float: right;
    padding: 0 0 0 12px;
}
.page-body {
    padding: 25px;
}
.no-padding {
    margin-left: -25px;
    margin-right: -25px;
}
.page-nav-tabs {
    float: left;
    cursor: pointer;
}
.page-nav-tabs ul > li {
    float: left;
}
.page-nav-tabs ul > li > a {
    padding: 0px 26px;
    color: #666;
    min-width: 120px;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    display: block;
}
.page-nav-tabs ul > li > a:hover {
    color: #1ab394;
}
.page-nav-tabs ul > li.active {
    border-bottom: 2px solid #1ab394;
}
.page-nav-tabs ul > li.active > a {
    color: #1ab394;
    border-left: 1px solid #e7eaec;
    border-right: 1px solid #e7eaec;
}
.page-nav-tabs ul > li:first-child {}
.page-nav-tabs ul > li:first-child.active >a {
    border-left: 1px solid transparent;
}
.page-tab-content {
    min-height: 670px;
}
.page-tab-content >.active {
    display: block;
}
.page-tab-pane {
    display: none;
    position: relative;
}
.page-content-container {
    padding: 8px 15px 10px;
}
.page-split-line {
    background: url(images/line.png);
    height: 1px;
    margin-left: -25px;
    margin-right: -25px;
    margin-bottom: 20px;
}
/*! normalize.css v5.0.0 | MIT License | github.com/necolas/normalize.css */
/**
 * 1. Change the default font family in all browsers (opinionated).
 * 2. Correct the line height in all browsers.
 * 3. Prevent adjustments of font size after orientation changes in
 *    IE on Windows Phone and in iOS.
 */
/* Document
   ========================================================================== */
html {
    font-family: 'Microsoft YaHei';
    /* 1 */
    line-height: 1.15;
    /* 2 */
    -ms-text-size-adjust: 100%;
    /* 3 */
    -webkit-text-size-adjust: 100%;
    /* 3 */
}
/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers (opinionated).
 */
body {
    margin: 0;
    font-size: 14px;
}
/**
 * Add the correct display in IE 9-.
 */
article, aside, footer, header, nav, section {
    display: block;
}
/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
    font-size: 2em;
    margin: 0.67em 0;
}
/* Grouping content
   ========================================================================== */
/**
 * Add the correct display in IE 9-.
 * 1. Add the correct display in IE.
 */
figcaption, figure, main {
    /* 1 */
    display: block;
}
/**
 * Add the correct margin in IE 8.
 */
figure {
    margin: 1em 40px;
}
/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
    box-sizing: content-box;
    /* 1 */
    height: 0;
    /* 1 */
    overflow: visible;
    /* 2 */
}
/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
    font-family: monospace, monospace;
    /* 1 */
    font-size: 1em;
    /* 2 */
}
/* Text-level semantics
   ========================================================================== */
/**
 * 1. Remove the gray background on active links in IE 10.
 * 2. Remove gaps in links underline in iOS 8+ and Safari 8+.
 */
a {
    background-color: transparent;
    /* 1 */
    -webkit-text-decoration-skip: objects;
    /* 2 */
}
/**
 * Remove the outline on focused links when they are also active or hovered
 * in all browsers (opinionated).
 */
a:active, a:hover {
    outline-width: 0;
}
/**
 * 1. Remove the bottom border in Firefox 39-.
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
    border-bottom: none;
    /* 1 */
    text-decoration: underline;
    /* 2 */
    /*text-decoration: underline dotted;*/
    /* 2 */
}
/**
 * Prevent the duplicate application of `bolder` by the next rule in Safari 6.
 */
b, strong {
    font-weight: inherit;
}
/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b, strong {
    font-weight: bolder;
}
/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code, kbd, samp {
    font-family: monospace, monospace;
    /* 1 */
    font-size: 1em;
    /* 2 */
}
/**
 * Add the correct font style in Android 4.3-.
 */
dfn {
    font-style: italic;
}
/**
 * Add the correct background and color in IE 9-.
 */
mark {
    background-color: #ff0;
    color: #000;
}
/**
 * Add the correct font size in all browsers.
 */
small {
    font-size: 80%;
}
/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}
sub {
    bottom: -0.25em;
}
sup {
    top: -0.5em;
}
/* Embedded content
   ========================================================================== */
/**
 * Add the correct display in IE 9-.
 */
audio, video {
    display: inline-block;
}
/**
 * Add the correct display in iOS 4-7.
 */
audio:not([controls]) {
    display: none;
    height: 0;
}
/**
 * Remove the border on images inside links in IE 10-.
 */
img {
    border-style: none;
}
/**
 * Hide the overflow in IE.
 */
svg:not(:root) {
    overflow: hidden;
}
/* Forms
   ========================================================================== */
/**
 * 1. Change the font styles in all browsers (opinionated).
 * 2. Remove the margin in Firefox and Safari.
 */
button, input, optgroup, select, textarea {
    font-family: "微软雅黑", "Helvetica Neue", Helvetica, Arial, sans-serif;
    /* 1 */
    font-size: 100%;
    /* 1 */
    line-height: 1.15;
    /* 1 */
    margin: 0;
    /* 2 */
}
/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button, input {
    /* 1 */
    overflow: visible;
}
/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button, select {
    /* 1 */
    text-transform: none;
}
/**
 * 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`
 *    controls in Android 4.
 * 2. Correct the inability to style clickable types in iOS and Safari.
 */
button, html [type="button"], [type="reset"], [type="submit"] {
    -webkit-appearance: button;
    /* 2 */
}
/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner {
    border-style: none;
    padding: 0;
}
/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring, [type="button"]:-moz-focusring, [type="reset"]:-moz-focusring, [type="submit"]:-moz-focusring {
    outline: 1px dotted ButtonText;
}
/**
 * Change the border, margin, and padding in all browsers (opinionated).
 */
fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em;
}
/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
    box-sizing: border-box;
    /* 1 */
    color: inherit;
    /* 2 */
    display: table;
    /* 1 */
    max-width: 100%;
    /* 1 */
    padding: 0;
    /* 3 */
    white-space: normal;
    /* 1 */
}
/**
 * 1. Add the correct display in IE 9-.
 * 2. Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
    display: inline-block;
    /* 1 */
    vertical-align: baseline;
    /* 2 */
}
/**
 * Remove the default vertical scrollbar in IE.
 */
textarea {
    overflow: auto;
}
/**
 * 1. Add the correct box sizing in IE 10-.
 * 2. Remove the padding in IE 10-.
 */
[type="checkbox"], [type="radio"] {
    box-sizing: border-box;
    /* 1 */
    padding: 0;
    /* 2 */
}
/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type="number"]::-webkit-inner-spin-button, [type="number"]::-webkit-outer-spin-button {
    height: auto;
}
/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type="search"] {
    -webkit-appearance: textfield;
    /* 1 */
    outline-offset: -2px;
    /* 2 */
}
/**
 * Remove the inner padding and cancel buttons in Chrome and Safari on macOS.
 */
[type="search"]::-webkit-search-cancel-button, [type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}
/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
    -webkit-appearance: button;
    /* 1 */
    font: inherit;
    /* 2 */
}
/* Interactive
   ========================================================================== */
/*
 * Add the correct display in IE 9-.
 * 1. Add the correct display in Edge, IE, and Firefox.
 */
details, menu {
    display: block;
}
/*
 * Add the correct display in all browsers.
 */
summary {
    display: list-item;
}
/* Scripting
   ========================================================================== */
/**
 * Add the correct display in IE 9-.
 */
canvas {
    display: inline-block;
}
/**
 * Add the correct display in IE.
 */
template {
    display: none;
}
/* Hidden
   ========================================================================== */
/**
 * Add the correct display in IE 10-.
 */
[hidden] {
    display: none;
}
@charset "UTF-8";
:root {
    /* Colors */
}
.report-panel {
    border-top: 3px solid #e7eaec;
    background: #fff;
    color: #666;
}
.report-panel-head {
    padding: 0 20px;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #e7eaec;
}
.report-panel-head-icon {
    width: 16px;
    height: 16px;
    display: inline-block;
    line-height: 1;
    vertical-align: middle;
    padding: 1px 10px;
    background-repeat: no-repeat;
}
.report-panel-head-icon.icon-contract {
    background-image: url(images/report/icon-calendar.png);
}
.report-panel-body {
    padding: 20px;
}
.report-list > li {
    margin-top: 20px;
}
.report-list > li:first-child {
    margin-top: 0;
}
.report-table {
    font-size: 12px;
    color: #666;
}
.report-table > thead th {
    background-color: #f2f2f2;
    font-weight: 400;
}
.report-table.table-bordered {
    border-color: #eaeaea;
}
.report-table.table > thead > tr > th, .report-table.table > tbody > tr > th, .report-table.table > tbody > tr > td {
    padding: 8px 8px 8px 15px;
}
.report-table.table-bordered > thead > tr > th {
    border-bottom-width: 1px;
    border-bottom-color: transparent;
}
.report-table.table-bordered > thead > tr > th, .report-table.table-bordered > tbody > tr > th, .report-table.table-bordered > tbody > tr > td {
    border-color: #eaeaea;
}
.section {
    border-bottom: 1px dashed #ccc;
    padding: 18px 0;
}
.section .mark {
    line-height: 1;
    margin-top: 8px;
}
.section .section-title {
    font-weight: 700;
    display: inline-block;
}
.section .section-title .section-icon {
    font-style: normal;
    color: #666;
}
.section .section-title .section-icon-contractInfo {
    background: url(images/contract-info.png)no-repeat;
    padding-left: 30px;
}
.section .badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: 700;
    color: #fff;
    line-height: 1;
    vertical-align: baseline;
    white-space: nowrap;
    text-align: center;
    background-color: #999;
    border-radius: 10px;
    font-weight: 400;
    text-shadow: 0 1px 0 rgba(0,0,0,0.2);
}
.section .badge.badge-warning {
    background-color: #ffa13d;
    color: #FDF8E5;
    margin-left: 30px;
}
.section .section-icon-contractContent {
    background: url(images/contract-content.png) no-repeat;
    padding-left: 30px;
}
.section .section-icon-contractOperate {
    background: url(images/operate.png) no-repeat;
    padding-left: 30px;
}
.section .section-icon-contractDeviceList {
    background: url(images/device-list.png) no-repeat;
    padding-left: 30px;
}
.mark {
    line-height: 1;
    margin-top: 8px;
}
.contract-info-content {
    margin-left: 35px;
    margin-top: 20px;
    margin-right: 35px;
}
.contract-info-content .grid-content {
    color: #666;
    line-height: 34px;
}
.contract-info-content .contract-operate {
    color: #666;
    line-height: 34px;
}
.contract-info-content .contract-operate label {
    width: 130px;
    display: inline-block;
}
.contract-info-content .contract-operate a {
    color: #1C83C6;
}
.contract-info-content .contract-operate .contractFile {
    margin-left: 137px;
    margin-top: -12px;
}
.contract-info-content .contract-title {
    color: #666;
    font-size: 14px;
    padding-bottom: 20px;
}
.contract-info-content .contract-article {
    color: #666;
    font-size: 14px;
    word-wrap: break-word;
    margin-bottom: 20px;
}
.contract-info-content .contract-file a {
    color: #1C83C6;
}
.contract-info-content .contract-file span {
    padding-left: 30px;
}
.print-title {
    text-align: center;
}
.print-title h2 {
    display: inline-block;
    margin-top: 100px;
    font-size: 30px;
    margin-bottom: 100px;
}
.print-title .print-button {
    display: inline-block;
    margin-top: 100px;
    float: right;
    margin-right: 20px;
}
.print-contract-info {
    color: #666;
}
.print-contract-info .grid-content {
    line-height: 30px;
}
.print-contract-label {
    color: #666;
    line-height: 30px;
}
.print-contract {}
.print-contract .contract-article {
    margin-left: 95px;
    line-height: 18px;
}
.print-contract .contract-file {
    margin-left: 35px;
    margin-bottom: 20px;
}
.print-contract .contract-operate {
    margin-left: 65px;
    line-height: 30px;
}
.print-contract .contract-file a {
    color: #1C83C6;
}
.print-contract .contract-operate a {
    color: #1C83C6;
}
.print-contract .contract-operate label {
    width: 130px;
    display: inline-block;
}
@charset "UTF-8";
:root {
    /* Colors */
}
.btn {
    padding: 8px 15px;
    position: static;
    position: initial;
    border: 1px solid;
    border-radius: 2px;
    background: none;
    cursor: pointer;
    display: inline-block;
    transition: all 0.3s;
    outline: none;
}
.btn-upload.with-icon {
    border: 1px solid #E8E8E8;
    background-color: #f5f5f5;
    color: #666;
}
.btn-upload-icon {
    background: url(images/icon_plus.png) no-repeat;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    padding-right: 26px;
}
.el-button--add, .el-button--reduce {
    background-color: #1ab394;
    border: 1px solid #1ab394;
}
.el-button--add:hover, .el-button--reduce:hover {
    background: #18a689;
    border: 1px solid #1ab394;
}
.el-pager li {
    font-size: 14px;
    color: #666;
    /*#666*/
    margin: 0 10px 0 0;
    border-radius: 2px;
    border: 1px solid #d3dce6;
}
.el-pagination button, .el-pagination span {
    font-size: 14px;
}
.el-checkbox__inner {
    width: 13px;
    height: 13px;
    border-radius: 2px;
}
.el-pagination button.disabled {
    color: #999;
    /*#999*/
    background-color: #F3F3F4;
    /*#f3f3f4*/
    border-color: #F3F3F4;
}
.el-pagination .btn-next, .el-pagination .btn-prev {
    color: #1ab394;
    border: 1px solid #1ab394;
}
.el-pager li:hover {
    border: 1px solid #1ab394;
}
.el-pager li.active+li {
    border-left: 1px solid #d3dce6;
}
.el-pager li.active+li:hover {
    border-left: 1px solid #1ab394;
}
.el-pagination__rightwrapper ul>li:first-child {
    margin-left: 10px;
}
.el-checkbox__inner::after {
    height: 6px;
    left: 3px;
    width: 3px;
    top: 0;
}
.el-checkbox__input .el-checkbox__inner.is-checked {
    border-color: #1ab394;
}
.el-checkbox__inner.is-focus {
    border-color: #1ab394;
}
.el-checkbox__inner:not(.is-disabled):hover {
    border-color: #1ab394;
}
.el-pagination--small .btn-next, .el-pagination--small .btn-prev, .el-pagination--small .el-pager li, .el-pagination--small .el-pager li:last-child {
    height: 24px;
    line-height: 22px;
}
.el-pagination__sizes .el-select.is-small .el-input__inner {
    font-size: 12px;
}
.el-select.is-small input {
    height: 24px;
}
.el-pagination.el-pagination--small span {
    font-size: 12px;
    height: 24px;
    line-height: 24px;
}
.el-pagination--small .el-pager li.active+li {
    padding-left: 3px;
}
.el-pagination--small .el-pager li.active+li:hover {
    border-color: #1ab394;
}
.el-upload__file {
    transition: all .5s cubic-bezier(.55,0,.1,1);
    font-size: 14px;
    color: #475669;
    line-height: 32px;
    box-sizing: border-box;
    border-radius: 0;
    white-space: normal;
    text-overflow: ellipsis;
    position: relative;
    display: inline-block;
    margin-right: 40px;
}

/* components */
.vue-scrollbar-transition, .vue-scrollbar__scrollbar-vertical, .vue-scrollbar__scrollbar-horizontal {
    transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -webkit-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
}
.vue-scrollbar-transition--scrollbar {
    transition: opacity 0.5s linear;
    -moz-transition: opacity 0.5s linear;
    -webkit-transition: opacity 0.5s linear;
    -o-transition: opacity 0.5s linear;
}
.vue-scrollbar {}
.vue-scrollbar__wrapper {
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}
.vue-scrollbar__wrapper:hover .vue-scrollbar {}
.vue-scrollbar__wrapper:hover .vue-scrollbar__scrollbar-vertical, .vue-scrollbar__wrapper:hover .vue-scrollbar__scrollbar-horizontal {
    opacity: 1;
}
.vue-scrollbar__scrollbar-vertical, .vue-scrollbar__scrollbar-horizontal {
    opacity: 0.5;
    position: absolute;
    background: transparent;
}
.vue-scrollbar__scrollbar-vertical .scrollbar, .vue-scrollbar__scrollbar-horizontal .scrollbar {
    position: relative;
    background: rgba(0,0,0,0.5);
    cursor: default;
    border-radius: 7px;
}
.vue-scrollbar__scrollbar-vertical:hover, .vue-scrollbar__scrollbar-horizontal:hover {
    background: rgba(0,0,0,0.3);
}
.vue-scrollbar__scrollbar-vertical {
    width: 7px;
    height: 100%;
    top: 0;
    right: 0;
}
.vue-scrollbar__scrollbar-vertical .scrollbar {
    width: 7px;
}
.vue-scrollbar__scrollbar-horizontal {
    height: 7px;
    width: 100%;
    bottom: 0;
    right: 0;
}
.vue-scrollbar__scrollbar-horizontal .scrollbar {
    height: 10px;
}
.shang-scroller {
    overflow: auto;
}
.shang-scroller::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
.shang-scroller::-webkit-scrollbar-button {
    width: 0;
    height: 0;
    display: none;
}
.shang-scroller::-webkit-scrollbar-corner {
    background-color: transparent;
}
.shang-scroller::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0,0,0,0.5);
    border-radius: 10px;
    -webkit-box-shadow: inset 1px 1px 0 rgba(0,0,0,0.1);
}
.shang-scroller::-webkit-scrollbar-thumb {
    background-color: rgba(0,0,0,0.3);
    border-radius: 10px;
    -webkit-box-shadow: inset 1px 1px 0 rgba(0,0,0,0.1);
}
.shang-scroller::-webkit-scrollbar-track {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px transparent;
}
.shang-number {}
.shang-number-keys {
    padding: 3px;
}
.shang-number-row {}
.shang-number-cell {
    height: 50px;
    width: 50px;
    border: 1px solid #ccc;
    display: inline-block;
    margin: 3px;
    line-height: 50px;
    text-align: center;
    transition: all .3s;
    cursor: pointer;
    border-radius: 3px;
    font-size: 18px;
}
.shang-number-cell:hover {
    background: #ccc;
    color: #fff;
}
.close-image {
    background-image: url(images/dialog_x.png);
    background-repeat: no-repeat;
    height: 11px;
    width: 11px;
    display: inline-block;
}
.close-image:hover {
    background-image: url(images/dialog_x_hover.png);
}
.shang-circle {
    position: relative;
    width: 100%;
    height: 100%;
}
.shang-circle-tooltip {
    position: fixed;
    z-index: 1;
    padding: 8px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 5px;
    color: #fff;
    display: none;
}
.shang-circle-number {
    position: absolute;
    top: 38%;
    z-index: 1;
    width: 100%;
    font-family: 'FZLTZHK';
    font-size: 24px;
    text-align: center;
}
/*
// common.css 
@import url('common.css');

// style.css
@import url('style.css');

// default.css
@import url('default.css');

// cloud-admin.css
@import url('cloud-admin.css');

// magicsuggest-min.css
@import url('magicsuggest-min.css');

// datatables.css
@import url('datatables.css');

// temp.css
@import url('temp.css');

@import url('normalize.css');
*/

/* element-ui-cascade */
.collape-col {
    background-color: rgb(245, 245, 245);
}
.collape-col .page-head {
    background-color: #fff;
}
.collape-col .page-body {
    background-color: #fff;
}
.el-tree {
    border: none !important;
}
.el-button{
    padding: 8px 18px !important;
}
.el-button--text{
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.el-menu--dark,
.el-menu--dark .el-submenu .el-menu {
    background-color: #ffffff!important;
}
.el-menu--dark .el-menu-item:hover,
.el-menu--dark .el-submenu__title:hover,
.el-menu--dark .el-submenu .el-menu .el-menu-item:hover{
    background-color: #E1FAFF;
}

.el-menu--dark .el-menu-item, .el-menu--dark .el-submenu__title,
.el-menu--dark .is-opened>.el-submenu__title {
    color: #333333;
}
.menu span {
    color: #333333;
    text-decoration: none;
    display: inline-block;
}