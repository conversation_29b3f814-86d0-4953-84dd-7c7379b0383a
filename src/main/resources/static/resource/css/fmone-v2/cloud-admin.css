.bootbox.fmone-v2 .modal-content .modal-body{
    padding: 20px 30px 20px 30px;
    margin: 0px;
}

.fmone-v2 .col-sm-label-mini {
    float: left;
    width: 102px;
    padding: 0px 20px 0px 0px;
    margin: 0px;
    line-height: 34px;
    text-align: right;
}

.bootbox.fmone-v2 .bootbox-body .form-horizontal .control-label{
    height: 34px;
    line-height: 34px;
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 0px;
}

.bootbox.fmone-v2 .form-horizontal .form-group .form-control{
    width: 342px;
}

.bootbox.fmone-v2 .form-horizontal .form-group .col-sm-8{
    margin-left: -15px;
}
.bootbox.fmone-v2 .form-horizontal .form-group .col-sm-8 #picture{
    width: 342px;
}

#content .fmone-v2 .btn.btn-success,
.bootbox.fmone-v2 .btn.btn-success,
.bootbox.fmone-v2 .modal-content .modal-footer .col-sm-10 .btn-success{
    color: #ffffff;
    background-color: #1ab394;
    border: 1px solid #1ab394;
    border-radius: 3px;
    font-size: 14px;
}

.bootbox.fmone-v2 .btn-default-fo,
.bootbox.fmone-v2 .modal-content .modal-footer .col-sm-10 .btn-default-fo{
    color: #666666;
    background-color: #f5f5f5;
    border: 1px solid #e6e6e6;
    margin: 0px 0px 0px 10px;
    border-radius:3px;
}

.bootbox.fmone-v2 .btn-default-fo:hover {
    color: #666;
    border-color: #e6e6e6;
    background: #e6e6e6;
}

.bootbox.fmone-v2 .btn-default-fo:active {
    color: #2e2e2e;
    border-color: #999;
    outline: 0;
}

.bootbox.fmone-v2 .modal-header .btn-default-fo{
    color: #333;
    background-color: #fff;
    border: 1px solid #e6e6e6;
}

.bootbox.fmone-v2 .modal-dialog .control-label{
    float: left;
    width: 120px;
    padding: 0px 20px 0px 0px;
    margin: 0px;
    line-height: 34px;
    text-align: right;
}


.bootbox.fmone-v2 .input-sm{
    font-size: 14px;
}

.bootbox.fmone-v2 select.input-sm{
    height: 34px;
    line-height: 34px;
}

.bootbox.fmone-v2 .modal-footer{
    margin-top: 0px;
}

#content .fmone-v2 .s-button-tools{
    margin-left:0px;
    margin-right: 0px;
}

.fmone-v2 .control-label{
    color: #666666;
    font-size: 14px;
}



.fmone-v2 .add-note{
    font-size: 12px;
    margin-top: 10px;
    margin-bottom: 17px;
}

.fmone-v2 .note-color{
    color: #ff3d3d;

}
.fmone-v2 .other-detail{
    color: #b2b2b2;
    font-size: 12px;
    margin-top: 10px;
}


/*公告详情*/
.fmone-v2 .tab-notice{
    padding-left: 5px;
    padding-right: 5px;
}

.fmone-v2 .notice-title{
    font-size: 26px;

    color: #666666;
}

.fmone-v2 .notice-author{
    margin-top: 30px;
    margin-bottom: 10px;
    color: #999;
}

.fmone-v2 .notice-reader{
    margin-bottom: 20px;
    color: #1c83c6;
}


.fmone-v2 .notice-img img{
    width: 639px;
    height:380px;
}
.fmone-v2 .notice-article{
    margin-top: 36px;
    font-size: 14px;
    color: #666;
    word-wrap: break-word;
}
.fmone-v2 .notice-file{
    margin-top: 30px;
    background-color: #ebeff0;
    display: inline-block;
    padding: 5px 10px;
}



/*库存*/
.fmone-v2 .require{
    color: red;
    vertical-align: top;
    padding-right: 5px;
    font-size: 20px;
}

.fmone-v2 .bomb-box-title{
    color: #000;
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    margin-top: 10px;
    position: relative;
}

.fmone-v2 .bomb-box-form{
    line-height: 36px;
}

.fmone-v2 .bomb-box-number{
    position: absolute;
    right:0;
    font-size:16px;
    font-weight: normal;
    bottom:0;
}

.fmone-v2 .bomb-box-form label{
    color: #666;
    font-size: 14px;
    padding-right: 7px;
    float:left;
}

.fmone-v2 .bomb-box-form .bomb-box-value-field{
    width: auto;
    margin-left: 80px;
}

.fmone-v2 .bomb-box-form .bomb-box-input{
    border-top: 1px solid #fff;
    border-left: 1px solid #fff;
    border-right: 1px solid #fff;
    border-bottom: 1px solid #e6e6e6;
    width: 100%;
    text-indent: 22px;
    color: #666;
    font-size: 14px;
    line-height: 34px;
    height:34px;
}

.fmone-v2 .bomb-box-form .bomb-box-label{
    text-indent: 22px;
    line-height: 34px;
    color: #666;
    font-size: 14px;
    border-bottom: 1px solid #e6e6e6;
    display: block;
}

.fmone-v2 .bomb-box-footer{
    border-top:1px solid #e6e6e6;
    color:#999;
    font-size:12px;
    margin-top:20px;
    margin-bottom:10px;
    padding-top:10px;

}


.fmone-v2 .btn,
.fmone-v2 .btn:hover,
#content .fmone-v2 .btn,
#content .fmone-v2 .btn:hover{
    padding: 5px 12px;
    position: initial;
    border:1px solid #e6e6e6;
    background: #f5f5f5;
    color: #666;
    cursor: pointer;
    display: inline-block;
    min-width: 68px;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.fmone-v2 .modal-body .avatar-body .btn-primary-fo{
    color: #ffffff !important;
    background-color: #1ab394 !important;
    border: 1px solid #1ab394 !important;
}

.fmone-v2 .modal-body .avatar-body .btn-primary-fo:hover{
    color: #ffffff !important;
    background-color: #48c2a9 !important;
    border: 1px solid #48c2a9 !important;
}

.fmone-v2 .modal-body .avatar-body .btn-primary-fo:active{
    color: #ffffff !important;
    background-color: #17a185 !important;
    border: 1px solid #17a185 !important;
}

.fmone-v2 .modal-body .avatar-body .btn-default-fo {
    color: #666666 !important;
    background-color: #f5f5f5 !important;
    border: 1px solid #e6e6e6 !important;
}

.fmone-v2 .modal-body .avatar-body .btn-default-fo:hover {
    color: #666!important;
    border-color: #e6e6e6 !important;
    background: #e6e6e6 !important;
}

.fmone-v2 .modal-body .avatar-body .btn-default-fo:active {
    color: #2e2e2e !important;
    border-color: #999 !important;
}

.fmone-v2 .btn-upload,
#content .fmone-v2 .btn-upload{
    border: 1px solid #1ab394;
    color: #1ab394;
    background-color: #fff;
}

.fmone-v2 .btn-upload.with-icon,
#content .fmone-v2 .btn-upload.with-icon{
    border:1px solid #E8E8E8;
    background-color:#f5f5f5;
    color:#666;
    margin-top: 3px;
}

.btn-upload-icon{background: url(../../img/icon_plus.png) no-repeat; width:16px;height:16px; display: inline-block;vertical-align:middle;    padding-right: 26px;}

.fmone-v2 .btn.btn-success:hover,
#content .fmone-v2 .btn.btn-success:hover{
    background-color: #1ab394;
    border-color: #1ab394;
}

.fmone-v2 .btn.btn-info,
#content .fmone-v2 .btn.btn-info{
    background-color: #1c83c6;
    border-color: #1c83c6;
    color:#fff;
}

.fmone-v2 .btn.btn-info:hover,
#content .fmone-v2 .btn.btn-info:hover{
    background-color: #1b7bb9;
    border-color: #1b7bb9;
}
.fmone-v2 .btn.btn-warning,
#content .fmone-v2 .btn.btn-warning{
    background-color: #ff9e36;
    border-color: #ff9e36;
    color:#fff;
}
.fmone-v2 .btn.btn-warning:hover,
#content .fmone-v2 .btn.btn-warning:hover{
    background-color: #f39733;
    border-color: #f39733;
}
.fmone-v2 .btn.btn-danger,
#content .fmone-v2 .btn.btn-danger{
    background-color:#ff5b5b;
    border-color: #ff5b5b;
}
.fmone-v2 .btn.btn-danger:hover,
#content .fmone-v2 .btn.btn-danger:hover{
    background-color:#ec5d62;
    border-color: #ec5d62;
}
.fmone-v2 .btn.btn-info.with-border,
#content .fmone-v2 .btn.btn-info.with-border{
    background-color: #fff;
    border:1px solid #1c83c6;
    color: #1c83c6;
}
.fmone-v2 .btn.btn-info.with-border:hover,
#content .fmone-v2 .btn.btn-info.with-border:hover{
    background-color: #1c83c6;
    color: #fff;
}
.fmone-v2 .btn.btn-danger.with-border,
#content .fmone-v2 .btn.btn-danger.with-border{
    background-color: #fff;
    border:1px solid #ff5b5b;
    color: #ff5b5b;
}
.fmone-v2 .btn.btn-danger.with-border:hover,
#content .fmone-v2 .btn.btn-danger.with-border:hover{
    background-color: #ff5b5b;
    color: #fff;
}
.fmone-v2 .btn.btn-warning.with-border,
#content .fmone-v2 .btn.btn-warning.with-border{
    background-color: #fff;
    border:1px solid #f39733;
    color: #f39733;
}
.fmone-v2 .btn.btn-warning.with-border:hover,
#content .fmone-v2 .btn.btn-warning.with-border:hover{
    background-color: #f39733;
    color: #fff;
}
.fmone-v2 .btn.btn-success.with-border,
#content .fmone-v2 .btn.btn-success.with-border{
    background-color: #fff;
    border:1px solid #1ab394;
    color: #1ab394;
}
.fmone-v2 .btn.btn-success.with-border:hover,
#content .fmone-v2 .btn.btn-success.with-border:hover{
    background-color: #1ab394;
    color: #fff;
}

.fmone-v2 .btn.btn-success.with-border:active, #content .fmone-v2 .btn.btn-success.with-border:active {
    background-color: #fff;
    color: #1ab394;

}

.fmone-v2 .badge{
    background-color:#ff9e36 ;
    font-weight: normal;
}

.fmone-v2 .badge.badge-warning{
    background-color:#f39733;

}

.fmone-v2 .badge.badge-success{
    background-color:#1ab394;
}

.fmone-v2 .badge.badge-danger{
    background-color:#ff5b5b;
}

.fmone-v2 .badge.badge-info{
    background-color:#1c83c6;
}

.fmone-v2 .form-control{
    border:1px solid #e6e6e6;
    border-radius: 2px;
}

.fmone-v2 .form-control::-webkit-input-placeholder{
    color: #b3b3b3;
    font-weight:lighter;
}
.fmone-v2 .form-control::-moz-placeholder{
    color: #b3b3b3;
    font-weight:lighter;
}
.fmone-v2 .form-control:-ms-input-placeholder{
    color: #b3b3b3;
    font-weight:lighter;
}

.fmone-v2 .btn:active{
    box-shadow: none;
}

.bootbox.dialog-border .modal-content .modal-body{
    padding: 40px 30px 40px 30px !important;
    margin: 0px !important;
}

