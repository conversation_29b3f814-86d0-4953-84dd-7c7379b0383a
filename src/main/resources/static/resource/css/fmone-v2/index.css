@import url('common.css');

/* style.css */
@import url('style.css');

/* default.css */
@import url('default.css');

/*cloud-admin.css*/
@import url('cloud-admin.css');

/*magicsuggest-min.css*/
@import url('magicsuggest-min.css');

/*datatables.css*/
@import url('datatables.css');

@import url("print.css");

/*index.css*/
.fmone-v2 .required,
.bootbox.fmone-v2 .required{
    color: #ff3d3d;
    width: 10px;
    display: inline-block;
    padding-right: 13px;
    margin-top: 3px;
    vertical-align: middle;
    position: inherit;
    font-size: 20px;
}

.fmone-v2-margin{
    margin:19px -5px 10px -5px;
}

.fmone-v2 .page{
    background-color: #fff;
}
.fmone-v2 .page-head{
    overflow: auto;
    zoom: 1;
    line-height: 59px;
    border-bottom:1px solid #E7EAEC;
}
.fmone-v2 .page-head-title{padding:0 25px;float:left;color:#666;}
.fmone-v2 .page-body{padding:28px 25px;}
.fmone-v2 .no-padding{margin-left:-25px;margin-right:-25px;}

.fmone-v2 .page-head .xia-tabs{float:left}
.fmone-v2 .page-head .title-buttons{float:right; padding:0 12px;}

.bootbox.fmone-v2 .page-head .title-buttons .btn:last-child,
#content .fmone-v2 .page-head .title-buttons .btn:last-child{margin-right:0}
#content .fmone-v2 .page-head .btn{position: inherit;}
#content .fmone-v2 .page-head .btn.btn-success{ background-color: #fff; color:#1ab394}
#content .fmone-v2 .page-head .btn.btn-success:hover{ background-color: #1ab394; color:#fff}
#content .fmone-v2 .page-head .btn.btn-success:active{ background-color: #fff; color:#1ab394;border:1px solid #1ab394;}
#content .fmone-v2 .page-head .btn.btn-warning{ border:1px solid #ff9e36; color:#ff9e36; background-color: #fff}
#content .fmone-v2 .page-head .btn.btn-warning:hover{ background-color:#ff9e36; color:#fff}
#content .fmone-v2 .page-head .btn.btn-warning:active{ background-color:#fff; color:#ff9e36}

#content .fmone-v2 .page-head .btn.btn-error{ background-color: #fff; color:#FF5353; border:1px solid #FF5353;}
#content .fmone-v2 .page-head .btn.btn-error:hover{ background-color: #FF5353; color:#fff}

#content .fmone-v2 .page-head .btn.btn-info{ background-color: #fff; color:#1c83c6; border:1px solid #1c83c6;}
#content .fmone-v2 .page-head .btn.btn-info:hover{ background-color: #1c83c6; color:#fff}

.fmone-v2 .page-head .xia-tabs.xia-nav-tabs > .nav-tabs > li{margin-right:0;border-bottom:1px solid transparent}
.fmone-v2 .page-head .xia-tabs.xia-nav-tabs > .nav-tabs > li.active{border-bottom: 2px solid #1ab394;}
.fmone-v2 .page-head .xia-nav-tabs > .nav-tabs > li.active > a{
    color:#1ab394;
    border-left:1px solid #E7EAEC;
    border-right:1px solid #E7EAEC;
}
.fmone-v2 .page-head .xia-nav-tabs > .nav-tabs > li:first-of-type.active > a{
    border-left:1px solid transparent;
}
.fmone-v2 .page-head .xia-nav-tabs > .nav-tabs > li > a{ padding:20px 26px 17px 26px; font-size: 14px;border-top:0px solid transparent;}
.fmone-v2 .page-head .xia-tabs.xia-nav-tabs > .nav-tabs > li > a:hover{color:#1ab394;}
.fmone-v2 .page-head .nav-tabs{border-bottom:0}
.fmone-v2 .page-head .nav-tabs > li{margin-bottom:0}

.fmone-v2 .dataTables_wrapper .btn-inline-edit{margin-right:10px}
.fmone-v2 .dataTables_wrapper .btn-inline-edit:last-child{margin-right:0}

.fmone-v2-alert .modal-content{border-radius:5px;}
.fmone-v2-alert .modal-header{background-color: #fff; border-bottom: none;height:82px;    border-radius: 10px 10px 0 0; }
.fmone-v2-alert .modal-header h4{text-align: center; font-size:18px;float: none;font-weight: bold; color:#666; padding-top:17px;}
.fmone-v2-alert .modal-body{ padding:0}
.fmone-v2-alert .modal-footer{background-color: #F0F6FA; border-top:1px solid #ebebeb;border-radius: 0 0 10px 10px;margin:0}
.fmone-v2-alert .modal-footer .btn.btn-default-fo{
    margin:16px;
    background-color: #fff;
    border:1px solid #e6e6e6;
    color:#666;
}
.fmone-v2-alert .modal-footer .btn.btn-default-fo:hover{
    margin:16px;
    background-color: #e6e6e6;
    border:1px solid #e6e6e6;
    color:#666;
}

.fmone-v2-alert .modal-footer .btn.btn-default-fo:active{
    color: #2e2e2e;
    border-color: #999;
}

.fmone-v2-alert .read-summary-category{background-color: #ebebeb; font-weight: bold; padding:8px 10px; color: #666;}
.fmone-v2-alert .read-summary-users{padding:10px 20px 10px 20px; color:#666;overflow: auto;height: 200px;}
.fmone-v2-alert .read-summary-user{float:left; text-align: center;margin-left:11px; margin-bottom:10px; width: 60px;}
.fmone-v2-alert .read-summary-user.new-line{margin-left:0}
.fmone-v2-alert .read-summary-user img{border-radius: 50%;}
.fmone-v2-alert .read-summary-user span{display:block;    margin-top: 4px; overflow: hidden; text-overflow: ellipsis;}

.fmone-v2 .dropzone .dz-preview,
.fmone-v2 .dropzone-previews .dz-preview{
    margin: 3px 17px 14px 0px;
}

.fmone-v2 .section{
    border-bottom: 1px dashed #E7EAEC;
    padding: 18px 0 16px 0;
    margin-bottom: 28px;
}

.fmone-v2 .section-title{ font-weight: bold}
.fmone-v2 .section-icon{ display:inline-block;margin-right: 13px;    vertical-align: middle;    margin-top: -3px;}
.fmone-v2 .section-icon.section-icon-reserve{background: url(../../img/icon_reserve_material.png) no-repeat; width:16px; height:16px;}

.fmone-v2 .xia-tabs.xia-nav-tabs.page-inner > .nav-tabs{background: #f4f4f4;border-bottom-color: #E7EAEC;}
.fmone-v2 .xia-tabs.xia-nav-tabs.page-inner > .nav-tabs > li {border-top:2px solid transparent;; border-bottom:0}
.fmone-v2 .xia-tabs.xia-nav-tabs.page-inner > .nav-tabs > li.active{border-bottom: 0;border-top: 2px solid #1ab394;margin-bottom: -1px;}
.fmone-v2 .xia-tabs.xia-nav-tabs.page-inner > .nav-tabs > li:first-of-type.active > a{color: #1ab394;}
.fmone-v2 .xia-tabs.xia-nav-tabs.page-inner > .nav-tabs > li > a{padding: 12px 15px;border: 0;    font-size: 14px;}
.fmone-v2 .xia-tabs.xia-nav-tabs.page-inner > .nav-tabs > li.active > a,
.fmone-v2 .xia-tabs.xia-nav-tabs.page-inner > .nav-tabs > li > a:hover{color:#1ab394}

.fmone-v2 .simple-table{width:100%;margin-top: 20px;color:#666}

.fmone-v2 .simple-table > thead > tr > th,
.fmone-v2 .simple-table > tbody > tr > th,
.fmone-v2 .simple-table > tbody > tr > td{
    border-color:#e6e6e6;
}

.fmone-v2 a:link{color:#1c83c6}
.fmone-v2 a:hover,.fmone-v2 a:active,.fmone-v2 a:focus{
    outline: none;
    text-decoration: none;
    color: #1a7bb9;
}

.fmone-v2 .form-control:focus{border-color: #1ab394 !important;}

.fmone-v2 .modal-content{box-shadow:none}

.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button{
    transition: .3s all;
}
/* 覆盖全局样式 慎重 */
.ui-datepicker .ui-datepicker-title select{ font-family: inherit}

/*公告样式*/
.fmone-v2 .help-block{
    color: #ff3d3d;
}

.fmone-v2 .form-group.has-error .form-control{
    border-color: #ff3d3d !important;
    box-shadow: none;
}
#content .fmone-v2.fmone-v2-spacing .form-group{
    margin-bottom: 0px;
}

.fmone-v2-spacing label{
    display: table-cell;
}
#content .fmone-v2 .form-group{
    margin-bottom: 12px;
}

.fmone-v2 .input-readonly{
    /*text-indent: 5px;*/
}

.fmone-v2 .form-control{
    /*text-indent: 5px;*/
}

.fmone-v2 .form-group.has-success .form-control{
    border-color: #e5e5e5 !important;
    box-shadow: none;
}

.fmone-v2 .form-group.has-success label{
    color:#666;
}

.fmone-v2 .member-item{
    margin-left: 20px;
    margin-right: 33px;
}

.fmone-v2 .form-group.has-error label{
    color:#ff3d3d;
}

.fmone-v2 .icon-confirm-delete{
    padding-right: 10px;
    vertical-align: text-bottom;
}

.fmone-v2 .control-label{
    white-space: nowrap;
    height: 34px;
    line-height: 34px;
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 0px;
}

.fmone-v2 .parent.member-item label{
    font-weight: 500;
}


.fmone-v2 .control-label.label-width-narrow:lang(en_US){
    width: 120px;
}

.fmone-v2 .control-label.label-width-normal:lang(en_US){
    width: 140px;
}

.fmone-v2 .control-label.label-width-wide:lang(en_US){
    width: 180px;
}

.fmone-v2 .control-label.label-width-broad:lang(en_US){
    width: 220px;
}

.fmone-v2 .control-label.label-width-narrow:lang(zh_CN){
    width: 100px;
}

.fmone-v2 .control-label.label-width-normal:lang(zh_CN){
    width: 120px;
}

.fmone-v2 .control-label.label-width-wide:lang(zh_CN){
    width: 160px;
}

.fmone-v2 .control-label.label-width-broad:lang(zh_CN){
    width: 200px;
}

.fmone-v2 .member-item:hover{
    background: none;
}

.fmone-v2 .leftTitle {
    margin-left: 56px;
    margin-right: 6px;
}

.chose_patrol_people{
    max-height: 200px;
    overflow: auto;
}
