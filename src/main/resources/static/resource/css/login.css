html {
    height: 100%;
    background: #fff;
}
body {
    width: 100%;
    height: 100%;
    min-height: 900px;
    margin: 0;
    padding: 0;
    background: #e9e9e9;
    overflow-x: hidden;
    /* Fix for webkit rendering */
    -webkit-font-smoothing: antialiased;
    -webkit-text-size-adjust: 100%;
    font-size-adjust: 100%;
    font-family:"微软雅黑","Open Sans", Helvetica, Arial, sans-serif;
}
#main {
    position: relative;
    min-height: 100%;
    background: #eeeeee;
}
/* psuedo background */
#main:before {
    content: "";
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #eeeeee;
}
.fadeIn {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
}

/*===============================================
  G. External Pages - login, register,
  screenlock, coming-soon, forgotpw
================================================= */
body.external-page {
    min-height: 0;
    overflow: auto;
}
body.external-page #main {
    overflow: hidden;
    background: url("../img/login_bg.jpg") no-repeat top center #2d494d;
}
body.external-page #main:before {
    display: none;
}
body.external-page #canvas-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
}
body.external-page #content .admin-form {
    position: relative;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    margin-top: 8%;
}
body.external-page #content .panel {
    box-shadow: 0 1px 40px 0 rgba(0, 0, 0, 0.3);
}
body.external-page #content .panel-heading {
    padding: 32px 10px 15px;
}
body.external-page .login-links {
    font-size: 15px;
    color: #DDD;
}
body.external-page .login-links a {
    color: #DDD;
    font-weight: 300;
}
body.external-page .login-links a.active {
    color: #FFF;
    font-weight: 600;
}
body.external-page .login-links a:hover,
body.external-page .login-links a:focus {
    color: #FFF;
    text-decoration: none;
}
body.external-page .coming-soon-title {
    text-align: center;
    color: #FFF;
    font-size: 40px;
    font-weight: 400;
    margin-top: 70px;
    margin-bottom: 20px;
}

/* table-layout when attached to row */
.row.table-layout {
    margin-left: 0;
    margin-right: 0;
}
.table-layout {
    display: table;
    table-layout: fixed;
    width: 100%;
    margin: 0;
}
.mb10 {
    margin-bottom: 10px !important;
}
.mb15 {
    margin-bottom: 15px !important;
}
.p10 {
    padding: 10px !important;
}
.p30 {
    padding: 30px !important;
}
.pr30 {
    padding-right: 30px !important;
}
.fs18 {
    font-size: 18px !important;
}
.ph15 {
    padding-left: 15px !important;
    padding-right: 15px !important;
}
.mr10 {
    margin-right: 10px !important;
}
.mt10 {
    margin-top: 10px !important;
}
.pull-right {
    float: right !important;
}

/*==================================================================
  Panel
===================================================================*/
.admin-form .panel {
    margin-bottom: 20px;
    background-color: #ffffff;
    border: 1px solid #DDD;
}
/*==================================================================
  Panel Header
===================================================================*/
.admin-form .panel-heading {
    overflow: hidden;
    position: relative;
    height: auto;
    padding: 19px 22px 18px;
    color: #999;
    border-radius: 0;
    border-top: 1px solid transparent;
    border-left: 0;
    border-right: 0;
    border-bottom: 1px solid #DDD;
    background-color: #FAFAFA;
}
.admin-form .heading-border:before {
    content: "";
    background-color: #9999A3;
    position: absolute;
    height: 10px;
    z-index: 1;
    top: 0;
    right: 0;
    left: 0;
}
.admin-form .heading-border .panel-heading {
    padding: 30px 22px 17px;
}
.admin-form .heading-border .panel-title {
    color: #999;
}
.admin-form .panel-title {
    text-align: left;
    font-weight: 300;
    font-size: 26px;
    padding: 0;
    margin: 0;
    background: transparent;
}
.admin-form .panel-title i {
    font-size: 26px;
    position: relative;
    margin-right: 15px;
    top: 0;
    border-width: 0;
}

.admin-form .panel-info > .panel-heading {
    border-top-color: #4788dc;
}
/*==================================================
  Panels
==================================================== */
.panel {
    position: relative;
    margin-bottom: 27px;
    background-color: #ffffff;
    border-radius: 3px;
}
.panel.panel-transparent {
    background: none;
    border: 0;
    margin: 0;
    padding: 0;
}
.panel.panel-border {
    border-style: solid;
    border-width: 0;
}
.panel.panel-border.top {
    border-top-width: 5px;
}
.panel.panel-border.right {
    border-right-width: 5px;
}
.panel.panel-border.bottom {
    border-bottom-width: 5px;
}
.panel.panel-border.left {
    border-left-width: 5px;
}
.panel.panel-border > .panel-heading {
    background-color: #fafafa;
    border-color: #e2e2e2;
    border-top: 1px solid transparent;
}
.panel.panel-border > .panel-heading > .panel-title {
    color: #999999;
}
.panel.panel-border.panel-default {
    border-color: #DDD;
}
.panel.panel-border.panel-default > .panel-heading {
    border-top: 1px solid transparent;
}
.panel-menu {
    background-color: #fafafa;
    padding: 12px;
    border: 1px solid #e2e2e2;
}
.panel-menu.dark {
    background-color: #f8f8f8;
}
.panel-body .panel-menu {
    border-left: 0;
    border-right: 0;
}
.panel-heading + .panel-menu,
.panel-menu + .panel-body,
.panel-body + .panel-menu,
.panel-body + .panel-body {
    border-top: 0;
}
.panel-body {
    position: relative;
    padding: 15px;
    border: 1px solid #e2e2e2;
}
.panel-body + .panel-footer {
    border-top: 0;
}
.panel-heading {
    position: relative;
    height: 52px;
    line-height: 49px;
    letter-spacing: 0.2px;
    color: #999999;
    font-size: 15px;
    font-weight: 400;
    padding: 0 8px;
    background: #fafafa;
    border: 1px solid #e2e2e2;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
}
.panel-heading + .panel-body {
    border-top: 0;
}
.panel-heading > .dropdown .dropdown-toggle {
    color: inherit;
}
.panel-heading .widget-menu .btn-group {
    margin-top: -3px;
}
.panel-heading .widget-menu .form-control {
    margin-top: 6px;
    font-size: 11px;
    height: 27px;
    padding: 2px 10px;
    border-radius: 1px;
}
.panel-heading .widget-menu .form-control.input-sm {
    margin-top: 9px;
    height: 22px;
}
.panel-heading .widget-menu .progress {
    margin-top: 11px;
    margin-bottom: 0;
}
.panel-heading .widget-menu .progress-bar-lg {
    margin-top: 10px;
}
.panel-heading .widget-menu .progress-bar-sm {
    margin-top: 15px;
}
.panel-heading .widget-menu .progress-bar-xs {
    margin-top: 17px;
}
.panel-icon {
    padding-left: 5px;
}
.panel-title {
    padding-left: 6px;
    margin-top: 0;
    margin-bottom: 0;
}
.panel-title > .fa,
.panel-title > .glyphicon,
.panel-title > .glyphicons,
.panel-title > .imoon {
    top: 2px;
    min-width: 22px;
    color: inherit;
    font-size: 14px;
}
.panel-title > a {
    color: inherit;
}
.panel-footer {
    padding: 10px 15px;
    background-color: #fafafa;
    border: 1px solid #e2e2e2;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px;
}
.panel > .list-group {
    margin-bottom: 0;
}
.panel > .list-group .list-group-item {
    border-radius: 0;
}
.panel > .list-group:first-child .list-group-item:first-child {
    border-top-right-radius: 2px;
    border-top-left-radius: 2px;
}
.panel > .list-group:last-child .list-group-item:last-child {
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px;
}
.panel-heading + .list-group .list-group-item:first-child {
    border-top-width: 0;
}
.panel-body + .list-group .list-group-item:first-child {
    border-top-width: 0;
}
.list-group + .panel-footer {
    border-top-width: 0;
}
.panel > .table,
.panel > .table-responsive > .table,
.panel > .panel-collapse > .table {
    margin-bottom: 0;
}
.panel > .table:first-child,
.panel > .table-responsive:first-child > .table:first-child {
    border-top-right-radius: 2px;
    border-top-left-radius: 2px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
    border-top-left-radius: 2px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
    border-top-right-radius: 2px;
}
.panel > .table:last-child,
.panel > .table-responsive:last-child > .table:last-child {
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
    border-bottom-left-radius: 2px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
    border-bottom-right-radius: 2px;
}
.panel > .panel-body + .table,
.panel > .panel-body + .table-responsive {
    border-top: 1px solid #eeeeee;
}
.panel > .table > tbody:first-child > tr:first-child th,
.panel > .table > tbody:first-child > tr:first-child td {
    border-top: 0;
}
.panel > .table-bordered,
.panel > .table-responsive > .table-bordered {
    border: 0;
}
.panel > .table-bordered > thead > tr > th:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.panel > .table-bordered > tbody > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.panel > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-bordered > thead > tr > td:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.panel > .table-bordered > tbody > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.panel > .table-bordered > tfoot > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
}
.panel > .table-bordered > thead > tr > th:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.panel > .table-bordered > tbody > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.panel > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-bordered > thead > tr > td:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.panel > .table-bordered > tbody > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.panel > .table-bordered > tfoot > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
}
.panel > .table-bordered > thead > tr:first-child > td,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
.panel > .table-bordered > tbody > tr:first-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
.panel > .table-bordered > thead > tr:first-child > th,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > th,
.panel > .table-bordered > tbody > tr:first-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
    border-bottom: 0;
}
.panel > .table-bordered > tbody > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.panel > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-bordered > tbody > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.panel > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
    border-bottom: 0;
}
.panel > .table-responsive {
    border: 0;
    margin-bottom: 0;
}
.panel-group {
    margin-bottom: 19px;
}
.panel-group .panel-title {
    padding-left: 0;
}
.panel-group .panel-heading,
.panel-group .panel-heading a {
    position: relative;
    display: block;
    width: 100%;
}
.panel-group.accordion-lg .panel + .panel {
    margin-top: 12px;
}
.panel-group.accordion-lg .panel-heading {
    font-size: 14px;
    height: 54px;
    line-height: 52px;
}
.panel-group .accordion-icon {
    padding-left: 35px;
}
.panel-group .accordion-icon:after {
    position: absolute;
    content: "\f068";
    font-family: "FontAwesome";
    font-size: 12px;
    font-style: normal;
    font-weight: normal;
    -webkit-font-smoothing: antialiased;
    color: #555;
    left: 10px;
    top: 0;
}
.panel-group .accordion-icon.collapsed:after {
    content: "\f067";
}
.panel-group .accordion-icon.icon-right {
    padding-left: 10px;
    padding-right: 30px;
}
.panel-group .accordion-icon.icon-right:after {
    left: auto;
    right: 5px;
}
.panel-group .panel {
    margin-bottom: 0;
    border-radius: 3px;
}
.panel-group .panel + .panel {
    margin-top: 5px;
}
.panel-group .panel-heading + .panel-collapse > .panel-body {
    border-top: 0;
}
.panel-group .panel-footer {
    border-top: 0;
}
.panel-group .panel-footer + .panel-collapse .panel-body {
    border-bottom: 1px solid #eeeeee;
}
/*==================================================================
  Panel Body
===================================================================*/
.admin-form .panel-body {
    padding: 25px;
    border: 0;
}
/*==================================================================
  Panel Footer
===================================================================*/
.admin-form .panel-footer {
    padding: 12px 13px;
    border: 0;
    border-top: 1px solid #DDD;
    background: #f2f2f2;
}
/*==================================================================
  Form Wrappers
===================================================================*/
.admin-form,
.admin-form * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.admin-form {
    line-height: 1.231;
    font-weight: 400;
    font-size: 14px;
    color: #626262;
}
.admin-form .section {
    margin-bottom: 22px;
}

.bg-light {
    background-color: #FAFAFA;
    color: #666;
}

/*==================================================================
  General Input Styling
===================================================================*/
.admin-form .radio,
.admin-form .option,
.admin-form .checkbox {
    cursor: pointer;
}
.admin-form .field {
    display: block;
    position: relative;
}
.admin-form .field-icon i {
    color: #BBB;
    position: relative;
}
.admin-form .field-label {
    display: block;
    margin-bottom: 7px;
}
.admin-form .field-label em {
    color: #e74c3c;
    font-size: 14px;
    font-style: normal;
    display: inline-block;
    margin-left: 4px;
    position: relative;
    top: 3px;
}
.admin-form .gui-input,
.admin-form .gui-textarea {
    padding: 10px;
}
.admin-form .select,
.admin-form .gui-input,
.admin-form .gui-textarea,
.admin-form .select > select,
.admin-form .select-multiple select {
    position: relative;
    vertical-align: top;
    border: 1px solid #DDD;
    display: -moz-inline-stack;
    display: inline-block;
    *display: inline;
    color: #626262;
    outline: none;
    height: 42px;
    width: 100%;
    *zoom: 1;
}
.admin-form .select option {
    background: #fff;
}
.admin-form select[disabled],
.admin-form .select > select[disabled] {
    color: #aaa !important;
}

/*==================================================================
  Input Icons
===================================================================*/
.admin-form .append-icon,
.admin-form .prepend-icon {
    top: 0;
    left: 0;
    display: inline-block;
    vertical-align: top;
    position: relative;
    width: 100%;
}
.admin-form .append-icon .field-icon,
.admin-form .prepend-icon .field-icon {
    top: 0;
    z-index: 4;
    width: 42px;
    height: 42px;
    color: inherit;
    line-height: 42px;
    position: absolute;
    text-align: center;
    -webkit-transition: all 0.5s ease-out;
    -moz-transition: all 0.5s ease-out;
    -ms-transition: all 0.5s ease-out;
    -o-transition: all 0.5s ease-out;
    transition: all 0.5s ease-out;
    pointer-events: none;
}
.admin-form .append-icon .field-icon i,
.admin-form .prepend-icon .field-icon i {
    position: relative;
    font-size: 14px;
}
.admin-form .prepend-icon .field-icon {
    left: 0;
}
.admin-form .append-icon .field-icon {
    right: 0;
}
.admin-form .prepend-icon > input,
.admin-form .prepend-icon > textarea {
    padding-left: 36px;
    font-size: 16px;
}
.admin-form .append-icon > input,
.admin-form .append-icon > textarea {
    padding-right: 36px;
    padding-left: 10px;
}
.admin-form .append-icon > textarea {
    padding-right: 36px;
}
/*==================================================
  Grid System
==================================================== */
.container {
    margin-right: auto;
    margin-left: auto;
    padding-left: 11px;
    padding-right: 11px;
}
@media (min-width: 768px) {
    .container {
        width: 742px;
    }
}
@media (min-width: 992px) {
    .container {
        width: 962px;
    }
}
@media (min-width: 1140px) {
    .container {
        width: 1062px;
    }
}
@media (min-width: 1400px) {
    .container {
        width: 1302px;
    }
}
.container-sm {
    max-width: 640px;
}
.container-md {
    max-width: 860px;
}
.container-lg {
    max-width: 1000px;
}
.container-xl {
    max-width: 1200px;
}
.container-fluid {
    margin-right: auto;
    margin-left: auto;
    padding-left: 11px;
    padding-right: 11px;
}
.row {
    margin-left: -11px;
    margin-right: -11px;
}
.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xl-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xl-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xl-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xl-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xl-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xl-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xl-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xl-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xl-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xl-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xl-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12, .col-xl-12 {
    position: relative;
    min-height: 1px;
    padding-left: 11px;
    padding-right: 11px;
}
.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
    float: left;
}
.col-xs-12 {
    width: 100%;
}
.col-xs-11 {
    width: 91.66666667%;
}
.col-xs-10 {
    width: 83.33333333%;
}
.col-xs-9 {
    width: 75%;
}
.col-xs-8 {
    width: 66.66666667%;
}
.col-xs-7 {
    width: 58.33333333%;
}
.col-xs-6 {
    width: 50%;
}
.col-xs-5 {
    width: 41.66666667%;
}
.col-xs-4 {
    width: 33.33333333%;
}
.col-xs-3 {
    width: 25%;
}
.col-xs-2 {
    width: 16.66666667%;
}
.col-xs-1 {
    width: 8.33333333%;
}
.col-xs-pull-12 {
    right: 100%;
}
.col-xs-pull-11 {
    right: 91.66666667%;
}
.col-xs-pull-10 {
    right: 83.33333333%;
}
.col-xs-pull-9 {
    right: 75%;
}
.col-xs-pull-8 {
    right: 66.66666667%;
}
.col-xs-pull-7 {
    right: 58.33333333%;
}
.col-xs-pull-6 {
    right: 50%;
}
.col-xs-pull-5 {
    right: 41.66666667%;
}
.col-xs-pull-4 {
    right: 33.33333333%;
}
.col-xs-pull-3 {
    right: 25%;
}
.col-xs-pull-2 {
    right: 16.66666667%;
}
.col-xs-pull-1 {
    right: 8.33333333%;
}
.col-xs-pull-0 {
    right: auto;
}
.col-xs-push-12 {
    left: 100%;
}
.col-xs-push-11 {
    left: 91.66666667%;
}
.col-xs-push-10 {
    left: 83.33333333%;
}
.col-xs-push-9 {
    left: 75%;
}
.col-xs-push-8 {
    left: 66.66666667%;
}
.col-xs-push-7 {
    left: 58.33333333%;
}
.col-xs-push-6 {
    left: 50%;
}
.col-xs-push-5 {
    left: 41.66666667%;
}
.col-xs-push-4 {
    left: 33.33333333%;
}
.col-xs-push-3 {
    left: 25%;
}
.col-xs-push-2 {
    left: 16.66666667%;
}
.col-xs-push-1 {
    left: 8.33333333%;
}
.col-xs-push-0 {
    left: auto;
}
.col-xs-offset-12 {
    margin-left: 100%;
}
.col-xs-offset-11 {
    margin-left: 91.66666667%;
}
.col-xs-offset-10 {
    margin-left: 83.33333333%;
}
.col-xs-offset-9 {
    margin-left: 75%;
}
.col-xs-offset-8 {
    margin-left: 66.66666667%;
}
.col-xs-offset-7 {
    margin-left: 58.33333333%;
}
.col-xs-offset-6 {
    margin-left: 50%;
}
.col-xs-offset-5 {
    margin-left: 41.66666667%;
}
.col-xs-offset-4 {
    margin-left: 33.33333333%;
}
.col-xs-offset-3 {
    margin-left: 25%;
}
.col-xs-offset-2 {
    margin-left: 16.66666667%;
}
.col-xs-offset-1 {
    margin-left: 8.33333333%;
}
.col-xs-offset-0 {
    margin-left: 0%;
}
@media (min-width: 768px) {
    .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
        float: left;
    }
    .col-sm-12 {
        width: 100%;
    }
    .col-sm-11 {
        width: 91.66666667%;
    }
    .col-sm-10 {
        width: 83.33333333%;
    }
    .col-sm-9 {
        width: 75%;
    }
    .col-sm-8 {
        width: 66.66666667%;
    }
    .col-sm-7 {
        width: 58.33333333%;
    }
    .col-sm-6 {
        width: 50%;
    }
    .col-sm-5 {
        width: 41.66666667%;
    }
    .col-sm-4 {
        width: 33.33333333%;
    }
    .col-sm-3 {
        width: 25%;
    }
    .col-sm-2 {
        width: 16.66666667%;
    }
    .col-sm-1 {
        width: 8.33333333%;
    }
    .col-sm-pull-12 {
        right: 100%;
    }
    .col-sm-pull-11 {
        right: 91.66666667%;
    }
    .col-sm-pull-10 {
        right: 83.33333333%;
    }
    .col-sm-pull-9 {
        right: 75%;
    }
    .col-sm-pull-8 {
        right: 66.66666667%;
    }
    .col-sm-pull-7 {
        right: 58.33333333%;
    }
    .col-sm-pull-6 {
        right: 50%;
    }
    .col-sm-pull-5 {
        right: 41.66666667%;
    }
    .col-sm-pull-4 {
        right: 33.33333333%;
    }
    .col-sm-pull-3 {
        right: 25%;
    }
    .col-sm-pull-2 {
        right: 16.66666667%;
    }
    .col-sm-pull-1 {
        right: 8.33333333%;
    }
    .col-sm-pull-0 {
        right: auto;
    }
    .col-sm-push-12 {
        left: 100%;
    }
    .col-sm-push-11 {
        left: 91.66666667%;
    }
    .col-sm-push-10 {
        left: 83.33333333%;
    }
    .col-sm-push-9 {
        left: 75%;
    }
    .col-sm-push-8 {
        left: 66.66666667%;
    }
    .col-sm-push-7 {
        left: 58.33333333%;
    }
    .col-sm-push-6 {
        left: 50%;
    }
    .col-sm-push-5 {
        left: 41.66666667%;
    }
    .col-sm-push-4 {
        left: 33.33333333%;
    }
    .col-sm-push-3 {
        left: 25%;
    }
    .col-sm-push-2 {
        left: 16.66666667%;
    }
    .col-sm-push-1 {
        left: 8.33333333%;
    }
    .col-sm-push-0 {
        left: auto;
    }
    .col-sm-offset-12 {
        margin-left: 100%;
    }
    .col-sm-offset-11 {
        margin-left: 91.66666667%;
    }
    .col-sm-offset-10 {
        margin-left: 83.33333333%;
    }
    .col-sm-offset-9 {
        margin-left: 75%;
    }
    .col-sm-offset-8 {
        margin-left: 66.66666667%;
    }
    .col-sm-offset-7 {
        margin-left: 58.33333333%;
    }
    .col-sm-offset-6 {
        margin-left: 50%;
    }
    .col-sm-offset-5 {
        margin-left: 41.66666667%;
    }
    .col-sm-offset-4 {
        margin-left: 33.33333333%;
    }
    .col-sm-offset-3 {
        margin-left: 25%;
    }
    .col-sm-offset-2 {
        margin-left: 16.66666667%;
    }
    .col-sm-offset-1 {
        margin-left: 8.33333333%;
    }
    .col-sm-offset-0 {
        margin-left: 0%;
    }
}
@media (min-width: 992px) {
    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
        float: left;
    }
    .col-md-12 {
        width: 100%;
    }
    .col-md-11 {
        width: 91.66666667%;
    }
    .col-md-10 {
        width: 83.33333333%;
    }
    .col-md-9 {
        width: 75%;
    }
    .col-md-8 {
        width: 66.66666667%;
    }
    .col-md-7 {
        width: 58.33333333%;
    }
    .col-md-6 {
        width: 50%;
    }
    .col-md-5 {
        width: 41.66666667%;
    }
    .col-md-4 {
        width: 33.33333333%;
    }
    .col-md-3 {
        width: 25%;
    }
    .col-md-2 {
        width: 16.66666667%;
    }
    .col-md-1 {
        width: 8.33333333%;
    }
    .col-md-pull-12 {
        right: 100%;
    }
    .col-md-pull-11 {
        right: 91.66666667%;
    }
    .col-md-pull-10 {
        right: 83.33333333%;
    }
    .col-md-pull-9 {
        right: 75%;
    }
    .col-md-pull-8 {
        right: 66.66666667%;
    }
    .col-md-pull-7 {
        right: 58.33333333%;
    }
    .col-md-pull-6 {
        right: 50%;
    }
    .col-md-pull-5 {
        right: 41.66666667%;
    }
    .col-md-pull-4 {
        right: 33.33333333%;
    }
    .col-md-pull-3 {
        right: 25%;
    }
    .col-md-pull-2 {
        right: 16.66666667%;
    }
    .col-md-pull-1 {
        right: 8.33333333%;
    }
    .col-md-pull-0 {
        right: auto;
    }
    .col-md-push-12 {
        left: 100%;
    }
    .col-md-push-11 {
        left: 91.66666667%;
    }
    .col-md-push-10 {
        left: 83.33333333%;
    }
    .col-md-push-9 {
        left: 75%;
    }
    .col-md-push-8 {
        left: 66.66666667%;
    }
    .col-md-push-7 {
        left: 58.33333333%;
    }
    .col-md-push-6 {
        left: 50%;
    }
    .col-md-push-5 {
        left: 41.66666667%;
    }
    .col-md-push-4 {
        left: 33.33333333%;
    }
    .col-md-push-3 {
        left: 25%;
    }
    .col-md-push-2 {
        left: 16.66666667%;
    }
    .col-md-push-1 {
        left: 8.33333333%;
    }
    .col-md-push-0 {
        left: auto;
    }
    .col-md-offset-12 {
        margin-left: 100%;
    }
    .col-md-offset-11 {
        margin-left: 91.66666667%;
    }
    .col-md-offset-10 {
        margin-left: 83.33333333%;
    }
    .col-md-offset-9 {
        margin-left: 75%;
    }
    .col-md-offset-8 {
        margin-left: 66.66666667%;
    }
    .col-md-offset-7 {
        margin-left: 58.33333333%;
    }
    .col-md-offset-6 {
        margin-left: 50%;
    }
    .col-md-offset-5 {
        margin-left: 41.66666667%;
    }
    .col-md-offset-4 {
        margin-left: 33.33333333%;
    }
    .col-md-offset-3 {
        margin-left: 25%;
    }
    .col-md-offset-2 {
        margin-left: 16.66666667%;
    }
    .col-md-offset-1 {
        margin-left: 8.33333333%;
    }
    .col-md-offset-0 {
        margin-left: 0%;
    }
}
@media (min-width: 1140px) {
    .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
        float: left;
    }
    .col-lg-12 {
        width: 100%;
    }
    .col-lg-11 {
        width: 91.66666667%;
    }
    .col-lg-10 {
        width: 83.33333333%;
    }
    .col-lg-9 {
        width: 75%;
    }
    .col-lg-8 {
        width: 66.66666667%;
    }
    .col-lg-7 {
        width: 58.33333333%;
    }
    .col-lg-6 {
        width: 50%;
    }
    .col-lg-5 {
        width: 41.66666667%;
    }
    .col-lg-4 {
        width: 33.33333333%;
    }
    .col-lg-3 {
        width: 25%;
    }
    .col-lg-2 {
        width: 16.66666667%;
    }
    .col-lg-1 {
        width: 8.33333333%;
    }
    .col-lg-pull-12 {
        right: 100%;
    }
    .col-lg-pull-11 {
        right: 91.66666667%;
    }
    .col-lg-pull-10 {
        right: 83.33333333%;
    }
    .col-lg-pull-9 {
        right: 75%;
    }
    .col-lg-pull-8 {
        right: 66.66666667%;
    }
    .col-lg-pull-7 {
        right: 58.33333333%;
    }
    .col-lg-pull-6 {
        right: 50%;
    }
    .col-lg-pull-5 {
        right: 41.66666667%;
    }
    .col-lg-pull-4 {
        right: 33.33333333%;
    }
    .col-lg-pull-3 {
        right: 25%;
    }
    .col-lg-pull-2 {
        right: 16.66666667%;
    }
    .col-lg-pull-1 {
        right: 8.33333333%;
    }
    .col-lg-pull-0 {
        right: auto;
    }
    .col-lg-push-12 {
        left: 100%;
    }
    .col-lg-push-11 {
        left: 91.66666667%;
    }
    .col-lg-push-10 {
        left: 83.33333333%;
    }
    .col-lg-push-9 {
        left: 75%;
    }
    .col-lg-push-8 {
        left: 66.66666667%;
    }
    .col-lg-push-7 {
        left: 58.33333333%;
    }
    .col-lg-push-6 {
        left: 50%;
    }
    .col-lg-push-5 {
        left: 41.66666667%;
    }
    .col-lg-push-4 {
        left: 33.33333333%;
    }
    .col-lg-push-3 {
        left: 25%;
    }
    .col-lg-push-2 {
        left: 16.66666667%;
    }
    .col-lg-push-1 {
        left: 8.33333333%;
    }
    .col-lg-push-0 {
        left: auto;
    }
    .col-lg-offset-12 {
        margin-left: 100%;
    }
    .col-lg-offset-11 {
        margin-left: 91.66666667%;
    }
    .col-lg-offset-10 {
        margin-left: 83.33333333%;
    }
    .col-lg-offset-9 {
        margin-left: 75%;
    }
    .col-lg-offset-8 {
        margin-left: 66.66666667%;
    }
    .col-lg-offset-7 {
        margin-left: 58.33333333%;
    }
    .col-lg-offset-6 {
        margin-left: 50%;
    }
    .col-lg-offset-5 {
        margin-left: 41.66666667%;
    }
    .col-lg-offset-4 {
        margin-left: 33.33333333%;
    }
    .col-lg-offset-3 {
        margin-left: 25%;
    }
    .col-lg-offset-2 {
        margin-left: 16.66666667%;
    }
    .col-lg-offset-1 {
        margin-left: 8.33333333%;
    }
    .col-lg-offset-0 {
        margin-left: 0%;
    }
}
@media (min-width: 1400px) {
    .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
        float: left;
    }
    .col-xl-12 {
        width: 100%;
    }
    .col-xl-11 {
        width: 91.66666667%;
    }
    .col-xl-10 {
        width: 83.33333333%;
    }
    .col-xl-9 {
        width: 75%;
    }
    .col-xl-8 {
        width: 66.66666667%;
    }
    .col-xl-7 {
        width: 58.33333333%;
    }
    .col-xl-6 {
        width: 50%;
    }
    .col-xl-5 {
        width: 41.66666667%;
    }
    .col-xl-4 {
        width: 33.33333333%;
    }
    .col-xl-3 {
        width: 25%;
    }
    .col-xl-2 {
        width: 16.66666667%;
    }
    .col-xl-1 {
        width: 8.33333333%;
    }
    .col-xl-pull-12 {
        right: 100%;
    }
    .col-xl-pull-11 {
        right: 91.66666667%;
    }
    .col-xl-pull-10 {
        right: 83.33333333%;
    }
    .col-xl-pull-9 {
        right: 75%;
    }
    .col-xl-pull-8 {
        right: 66.66666667%;
    }
    .col-xl-pull-7 {
        right: 58.33333333%;
    }
    .col-xl-pull-6 {
        right: 50%;
    }
    .col-xl-pull-5 {
        right: 41.66666667%;
    }
    .col-xl-pull-4 {
        right: 33.33333333%;
    }
    .col-xl-pull-3 {
        right: 25%;
    }
    .col-xl-pull-2 {
        right: 16.66666667%;
    }
    .col-xl-pull-1 {
        right: 8.33333333%;
    }
    .col-xl-pull-0 {
        right: auto;
    }
    .col-xl-push-12 {
        left: 100%;
    }
    .col-xl-push-11 {
        left: 91.66666667%;
    }
    .col-xl-push-10 {
        left: 83.33333333%;
    }
    .col-xl-push-9 {
        left: 75%;
    }
    .col-xl-push-8 {
        left: 66.66666667%;
    }
    .col-xl-push-7 {
        left: 58.33333333%;
    }
    .col-xl-push-6 {
        left: 50%;
    }
    .col-xl-push-5 {
        left: 41.66666667%;
    }
    .col-xl-push-4 {
        left: 33.33333333%;
    }
    .col-xl-push-3 {
        left: 25%;
    }
    .col-xl-push-2 {
        left: 16.66666667%;
    }
    .col-xl-push-1 {
        left: 8.33333333%;
    }
    .col-xl-push-0 {
        left: auto;
    }
    .col-xl-offset-12 {
        margin-left: 100%;
    }
    .col-xl-offset-11 {
        margin-left: 91.66666667%;
    }
    .col-xl-offset-10 {
        margin-left: 83.33333333%;
    }
    .col-xl-offset-9 {
        margin-left: 75%;
    }
    .col-xl-offset-8 {
        margin-left: 66.66666667%;
    }
    .col-xl-offset-7 {
        margin-left: 58.33333333%;
    }
    .col-xl-offset-6 {
        margin-left: 50%;
    }
    .col-xl-offset-5 {
        margin-left: 41.66666667%;
    }
    .col-xl-offset-4 {
        margin-left: 33.33333333%;
    }
    .col-xl-offset-3 {
        margin-left: 25%;
    }
    .col-xl-offset-2 {
        margin-left: 16.66666667%;
    }
    .col-xl-offset-1 {
        margin-left: 8.33333333%;
    }
    .col-xl-offset-0 {
        margin-left: 0%;
    }
}

/*==================================================================
  Buttons
-===================================================================*/
.admin-form .button {
    color: #243140;
    border: 0;
    height: 42px;
    line-height: 42px;
    font-size: 15px;
    cursor: pointer;
    padding: 0 18px;
    text-align: center;
    vertical-align: top;
    background: #DBDBDB;
    display: inline-block;
    -webkit-user-drag: none;
    text-shadow: 0 1px rgba(255, 255, 255, 0.2);
    -webkit-border-radius: 3px;
    border-radius: 3px;
}
.admin-form .btn-primary {
    background-color: #4a89dc;
}

.admin-form .btn-primary,
.admin-form .btn-primary:hover,
.admin-form .btn-primary:focus,
.admin-form .btn-primary:active {
    color: #fff;
    text-shadow: 0 1px rgba(0, 0, 0, 0.08);
}
/*==================================================================
  ANDROID + IOS FIXES
===================================================================*/
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .admin-form .option,
    .admin-form .rating,
    .admin-form .switch,
    .admin-form .captcode {
        -webkit-animation: bugfix infinite 1s;
    }
    @-webkit-keyframes bugfix {
        from {
            padding: 0;
        }
        to {
            padding: 0;
        }
    }
    .admin-form .switch {
        margin-right: 10px;
        margin-bottom: 5px;
    }
    .admin-form .option {
        margin-right: 15px;
    }
}
/*==================================================================
  Switches
===================================================================*/
.admin-form .switch {
    cursor: pointer;
    position: relative;
    padding-right: 10px;
    display: inline-block;
    margin-bottom: 5px;
    height: 26px;
}
.admin-form .switch > label {
    cursor: pointer;
    display: inline-block;
    position: relative;
    height: 25px;
    width: 58px;
    color: #fff;
    font-size: 10px;
    font-weight: bold;
    line-height: 20px;
    text-align: center;
    background: #D7D7D7;
    border: 2px solid #D7D7D7;
    text-transform: uppercase;
    font-family: Helvetica, Arial, sans-serif;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    transition: 0.3s ease-out;
    -webkit-border-radius: 3px;
    border-radius: 3px;
}
.admin-form .switch > label + span {
    display: inline-block;
    padding-left: 5px;
    position: relative;
    top: -7px;
}
.admin-form .switch > label:before {
    content: attr(data-off);
    position: absolute;
    top: 1px;
    right: 3px;
    width: 33px;
}
.admin-form .switch > label:after {
    content: "";
    margin: 1px;
    width: 19px;
    height: 19px;
    display: block;
    background: #fff;
    -webkit-border-radius: 2px;
    border-radius: 2px;
}
.admin-form .switch > input {
    -webkit-appearance: none;
    position: absolute;
    width: inherit;
    height: inherit;
    opacity: 0;
    left: 0;
    top: 0;
}

/* Input Labels */
.admin-form .switch,
.admin-form .option,
.admin-form .field-label {
    font-size: 14px;
}


/*==================================================================
  Switch:checked state
===================================================================*/
.admin-form .switch > input:checked + label {
    border-color: #999;
    background: #999;
    padding-left: 33px;
    color: white;
}
.admin-form .switch > input:checked + label:before {
    content: attr(data-on);
    left: 1px;
    top: 1px;
}
.admin-form .switch > input:checked + label:after {
    margin: 1px;
    width: 19px;
    height: 19px;
    background: white;
}

.admin-form .switch-primary > input:checked + label {
    background: #4a89dc;
    border-color: #4a89dc;
}
.admin-form .switch-primary > input:checked + label:after {
    color: #4a89dc;
}
.admin-form .switch-primary > input:checked:focus + label {
    background: #2e76d6;
    border-color: #2e76d6;
}

/*==================================================
  Utility Classes
==================================================== */
.clearfix:before,
.clearfix:after,
.dl-horizontal dd:before,
.dl-horizontal dd:after,
.container:before,
.container:after,
.container-fluid:before,
.container-fluid:after,
.row:before,
.row:after,
.form-horizontal .form-group:before,
.form-horizontal .form-group:after,
.btn-toolbar:before,
.btn-toolbar:after,
.btn-group-vertical > .btn-group:before,
.btn-group-vertical > .btn-group:after,
.nav:before,
.nav:after,
.navbar:before,
.navbar:after,
.navbar-header:before,
.navbar-header:after,
.navbar-collapse:before,
.navbar-collapse:after,
.pager:before,
.pager:after,
.panel-body:before,
.panel-body:after,
.modal-footer:before,
.modal-footer:after,
#topbar:before,
#topbar:after {
    content: " ";
    display: table;
}
.clearfix:after,
.dl-horizontal dd:after,
.container:after,
.container-fluid:after,
.row:after,
.form-horizontal .form-group:after,
.btn-toolbar:after,
.btn-group-vertical > .btn-group:after,
.nav:after,
.navbar:after,
.navbar-header:after,
.navbar-collapse:after,
.pager:after,
.panel-body:after,
.modal-footer:after,
#topbar:after {
    clear: both;
}

.admin-form .panel-info > .panel-heading:before {
    background-color: #4788dc;
}
.admin-form .panel-info.heading-border:before,
.admin-form .panel-info .heading-border:before {
    background-color: #4788dc;
}
