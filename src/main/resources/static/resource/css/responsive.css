/* Colors */
/* Primary colors */
@media only screen and (max-width: 767px) {
  body {
    overflow-x: hidden;
  }
  /* NAVBAR */
  .navbar .navbar-nav.pull-right {
    display: block;
    float: none !important;
    background-color: #404040;
    text-align: center;
  }
  .navbar .nav > li > a:hover,
  .navbar .nav > li > a:focus,
  .navbar .nav .open > a,
  .navbar .nav .open > a:hover,
  .navbar .nav .open > a :focus {
    background-color: #5a5a5a !important;
  }
  .navbar .navbar-brand {
    float: none;
    height: 45px;
    width: 100%;
  }
  .navbar .navbar-brand img {
    left: 10px;
  }
  .navbar .navbar-brand a.switcher {
    position: absolute;
    left: auto;
    right: 70px;
  }
  .navbar .navbar-brand a.switcher i {
    font-size: 14px;
  }
  .navbar .nav li.dropdown {
    display: inline-block;
    vertical-align: middle;
  }
  .navbar .dropdown-menu {
    position: absolute !important;
    box-shadow: 2px 1px 2px 0 rgba(0, 0, 0, 0.2) !important;
    text-align: left;
  }
  .navbar #header-notification .dropdown-toggle i,
  .navbar #header-message .dropdown-toggle i,
  .navbar #header-tasks .dropdown-toggle i {
    color: #FFFFFF;
    text-shadow: none;
  }
  .navbar-nav {
    margin: 5px -15px 0px;
  }
  .navbar-nav .open .dropdown-menu.notification {
    min-width: 270px;
    right: auto !important;
    left: 0 !important;
  }
  .navbar-nav .open .dropdown-menu.inbox {
    min-width: 280px;
    left: -50px !important;
  }
  .navbar-nav .open .dropdown-menu.tasks {
    min-width: 250px;
    left: -100px !important;
  }
  .navbar-nav .open .dropdown-menu > li > a {
    line-height: 40px;
    padding: 0 10px;
  }
  .navbar-nav .open .dropdown-menu > li.footer > a {
    line-height: 30px;
    padding: 0 10px;
  }
  .navbar-nav > li > a {
    padding: 15px;
  }
  .dropdown.user a.dropdown-toggle {
    padding: 10.5px;
  }
  /* SIDEBAR */
  .sidebar {
    box-shadow: 2px 1px 2px 0 rgba(0, 0, 0, 0.2);
    right: -250px;
    position: absolute;
    top: 0 !important;
    z-index: 1001;
    transition: right 0.3s ease 0s;
    display: none;
    -webkit-transform: scale3d(1, 1, 1);
  }
  .sidebar.mini-menu {
    box-shadow: none;
    right: auto;
    top: auto !important;
    z-index: 1;
  }
  body {
    right: 0;
    transition: right 0.3s ease 0s;
  }
  .slidebar {
    position: absolute;
    right: 250px;
  }
  .slidebar .sidebar {
    right: 0;
    width: 250px !important;
    display: block;
    height: 100%;
  }
  .slidebar #content {
    border-right: 1px solid #cdd2d2;
  }
  .sidebar-collapse {
    right: 20px;
    left: auto;
  }
  /* DATE RANGE */
  .date-range.pull-right {
    display: block;
    float: none !important;
    padding-bottom: 5px;
  }
  /* CONTENT TITLE */
  .content-title.pull-left {
    display: block;
    float: none !important;
  }
  /* CALENDAR */
  .fc-header-left,
  .fc-header-center,
  .fc-header-right {
    display: block;
    float: none !important;
  }
  /* TABS AND ACCORDIONS */
  .box .box-tabs .nav-tabs {
    top: 0;
  }
  .box .box-tabs .nav-tabs > li {
    display: block;
    float: none;
  }
  .box.border.orange .box-tabs .nav-tabs,
  .box.border.blue .box-tabs .nav-tabs,
  .box.border.red .box-tabs .nav-tabs,
  .box.border.green .box-tabs .nav-tabs,
  .box.border.primary .box-tabs .nav-tabs,
  .box.border.pink .box-tabs .nav-tabs,
  .box.border.purple .box-tabs .nav-tabs,
  .box.border.inverse .box-tabs .nav-tabs {
    top: 0;
  }
  .box.border.orange .box-tabs .nav-tabs > li,
  .box.border.blue .box-tabs .nav-tabs > li,
  .box.border.red .box-tabs .nav-tabs > li,
  .box.border.green .box-tabs .nav-tabs > li,
  .box.border.primary .box-tabs .nav-tabs > li,
  .box.border.pink .box-tabs .nav-tabs > li,
  .box.border.purple .box-tabs .nav-tabs > li,
  .box.border.inverse .box-tabs .nav-tabs > li {
    display: block;
    float: none;
  }
  .box.border.orange .box-tabs .nav-tabs > li > a,
  .box.border.blue .box-tabs .nav-tabs > li > a,
  .box.border.red .box-tabs .nav-tabs > li > a,
  .box.border.green .box-tabs .nav-tabs > li > a,
  .box.border.primary .box-tabs .nav-tabs > li > a,
  .box.border.pink .box-tabs .nav-tabs > li > a,
  .box.border.purple .box-tabs .nav-tabs > li > a,
  .box.border.inverse .box-tabs .nav-tabs > li > a {
    color: #666666;
  }
  .box.border.orange .box-tabs .nav-tabs > li.active a,
  .box.border.blue .box-tabs .nav-tabs > li.active a,
  .box.border.red .box-tabs .nav-tabs > li.active a,
  .box.border.green .box-tabs .nav-tabs > li.active a,
  .box.border.primary .box-tabs .nav-tabs > li.active a,
  .box.border.pink .box-tabs .nav-tabs > li.active a,
  .box.border.purple .box-tabs .nav-tabs > li.active a,
  .box.border.inverse .box-tabs .nav-tabs > li.active a {
    font-weight: 600;
    color: #555555;
  }
  .box.border.orange .box-tabs .nav-tabs > li.active a:before,
  .box.border.blue .box-tabs .nav-tabs > li.active a:before,
  .box.border.red .box-tabs .nav-tabs > li.active a:before,
  .box.border.green .box-tabs .nav-tabs > li.active a:before,
  .box.border.primary .box-tabs .nav-tabs > li.active a:before,
  .box.border.pink .box-tabs .nav-tabs > li.active a:before,
  .box.border.purple .box-tabs .nav-tabs > li.active a:before,
  .box.border.inverse .box-tabs .nav-tabs > li.active a:before {
    color: #333;
    display: inline;
    font-size: 16px;
    font-family: FontAwesome;
    height: auto;
    content: "\f00c";
    font-weight: 300;
    text-shadow: none;
    padding: 0 5px;
  }
  /* HUBSPOT MESSENGER */
  ul.messenger.messenger-fixed.messenger-on-right,
  ul.messenger.messenger-fixed.messenger-on-left {
    width: 50% !important;
  }
  /* WIZARDS AND VALIDATION */
  .form-wizard .nav-justified > li > a {
    text-align: left;
  }
  /* CHAT WIDGET */
  .chat-widget {
    right: 5px;
    width: 85%;
  }
  /* PRICING */
  .pricing_table {
    margin: 0 -15px 10px -15px;
  }
  /* INVOICE */
  .invoice-header {
    margin-bottom: 20px;
  }
  .invoice-header .pull-left,
  .invoice-header .pull-right {
    display: block;
    float: none !important;
  }
  .amount {
    margin-right: 0;
  }
  /* ERROR 404 & 500 */
  .not-found .error {
    right: 30%;
  }
  .not-found .error-500 {
    right: 30%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 800px) {
  body {
    overflow-x: hidden;
  }
  #navbar-left .name {
    display: none;
  }
}
