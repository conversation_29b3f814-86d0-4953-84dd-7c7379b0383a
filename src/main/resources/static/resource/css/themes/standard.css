html {
  overflow-y: auto;
}
#content table .btn {
  margin-right: 0;
}
textarea {
  resize: none;
}
.modal {
  position: absolute;
  /*overflow-y: auto;*/
}
.s-button-tools:last-child {
  margin-right: 0px;
}
.card-panel-autoScroll {
  overflow-y: auto;
}
.container .twolineText {
  margin-top: -10px;
}
.btnbar-top {
  height: 32px;
}
.btnbar-top button {
  float: right;
}
.col-outer {
  padding-top: 10px;
}
.row-normal {
  padding: 0;
  margin: 0;
}
.container-border {
  border: solid 1px #eeeeee;
  background-color: #FFF;
}
.container-padding {
  padding: 10px;
}
.card-panel {
  overflow-y: auto;
  overflow-x: hidden;
}
.card-panel .card-item {
  background-color: #FFF;
  padding: 10px 15px;
  float: left;
  width: 100%;
  border-bottom: solid 1px #eeeeee;
}
.card-panel .card-item .card-title {
  font-size: 16px;
  float: left;
}
.card-panel .card-item .card-item.select {
  padding-left: 8px;
}
.card-panel .card-item .card-secinfo-right {
  float: right;
  font-size: 12px;
  color: #8FA0A4;
}
.card-panel .card-item .card-secinfo-left {
  float: left;
  font-size: 12px;
  color: #8FA0A4;
}
.nav.nav-tabs .nav-hide {
  display: none;
}
.bottom-seperate {
  margin-bottom: 5px;
}
.font-strong {
  font-weight: 800;
}
.dialog-butn-bar {
  float: right;
}
.dialog-butn-bar .btn-right {
  float: right;
}
.form-horizontal > .form-group:last-child {
  /*margin-bottom: 0;*/
}
.button-tab {
  position: absolute;
  right: 0;
  top: -50px;
}
.tab-pane {
  position: relative;
}
.tabbable.tabs-left .nav > li > a {
  padding: 10px 15px;
}
.container .panel .panel-body {
  padding-top: 20px;
}
.panel {
  border: solid 1px #EEE;
}
.panel-body section:last-child {
  margin-bottom: 0;
}
.panel-table {
  border: 0;
}
.panel-heading-lg {
  height: 52px;
}
.panel-heading-title-lg {
  font-size: 18px;
  font-weight: normal;
  padding-top: 2px;
}
.panel-heading-title {
  float: left;
}
.panel-heading-buttonbar {
  float: right;
}
.panel-heading-buttonbar button {
  float: right;
  margin-left: 10px;
}
.xia-tabs.xia-nav-tabs > .nav-tabs > li > a:hover {
  color: #1ab394;
}
.xia-tabs.xia-nav-tabs > .nav-tabs > li .page-head-title{
  color: #666 !important;
}
.legend {
  float: right;
}
.legend-icon {
  width: 20px;
  height: 10px;
  border: solid 1px #fff;
  display: inline-block;
  margin-right: 5px;
  margin-left: 10px;
}
#materialsmgr .stock-activenum {
  color: #008d4c;
  font-size: 14px;
  margin-right: 10px;
  font-weight: 500;
}
#materialsmgr .stock-reservenum {
  color: #000;
  font-size: 14px;
  margin-right: 10px;
  font-weight: 500;
}
#materialsmgr .tabbable.tabs-left .nav > li > a {
  padding: 10px 15px;
}
#materialsmgr .tabs-left > .nav-tabs .active > a,
#materialsmgr .tabs-left > .nav-tabs .active > a:hover,
#materialsmgr .tabs-left > .nav-tabs .active > a:focus {
  border-radius: 0;
  border-left: solid 1px #3C8DBC;
}
#materialsmgr .panel-left {
  float: left;
  width: 18%;
  height: 120px;
  border: solid 1px #EEE;
}
#materialsmgr .panel-right {
  float: left;
  height: 120px;
  border: solid 1px #EEE;
  padding-top: 0px;
  width: 80%;
}
#materialsmgr .dashbox {
  margin-bottom: 0px;
}
#materialsmgr .searchbar {
  padding: 10px;
}
#materialsmgr .searchPanel {
  margin-bottom: 10px;
}
#inventoryMgr .tabs-left > .nav-tabs .active {
  border-left: solid 3px #3C8DBC;
}
#inventoryMgr .tabs-left > .nav-tabs .active > a {
  padding-left: 12px;
}
#inventoryMgr .tabs-left > .nav-tabs > li > a {
  border-radius: 0;
  border-left: none;
}
#inventoryMgr .tabs-left > .nav-tabs {
  margin-right: 10px;
}
#inventoryMgr .form-hint {
  height: 34px;
  padding: 6px 6px;
  vertical-align: middle;
  padding-left: 15px;
  font-size: 15px;
}
.input-num {
  width: 100px;
  margin-left: 10px;
  padding: 0 10px;
}
.m-t-10 {
  margin-top: 10px;
}
.m-t-20 {
  margin-top: 20px;
}
.m-b-20 {
  margin-bottom: 20px;
}
.m-b-0 {
  margin-bottom: 0px;
}
.p-l-0 {
  padding-left: 0;
}
.p-r-0 {
  padding-right: 0;
}
.tab-content {
  min-height: 670px;
}
.list-group-item {
  margin-bottom: 0;
  border-bottom: 0;
}
.list-group-item:hover {
  background-color: #f7f3f0;
}
.list-group-item.selected {
  background-color: #D2E1F4;
}
.list-group-item.selected {
  border-left: 3px solid #1ab394;
  padding-left: 13px;
}
.list-group-item .describe {
  width: 80%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.list-group {
  margin-bottom: 20px;
  padding-left: 0;
}
.list-group-item {
  cursor: pointer;
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #ffffff;
  border: 1px solid #dddddd;
}
.list-group-item:first-child {
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
}
.list-group-item:last-child {
  margin-bottom: 0;
}
.list-group-item > .badge {
  float: right;
}
.list-group-item > .badge + .badge {
  margin-right: 5px;
}
.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}
.radio-format {
  margin-top: 7px;
  margin-right: 10px;
  display: block;
  float: left;
}
.radio-format input {
  margin-right: 0px;
}
.ms-ctn {
  padding-left: 6px;
}
.calendar.second.left {
  display: block;
}
.calendar-date {
  margin-top: 40px;
}
.range_inputs {
  position: absolute;
  top: 0;
  left: 10px;
  padding-top: 10px;
}
.range_inputs label,
.range_inputs input {
  float: left;
}
.daterangepicker_start_input,
.daterangepicker_end_input {
  width: 230px;
}
.daterangepicker .daterangepicker_start_input label,
.daterangepicker .daterangepicker_end_input label {
  width: 20%;
  line-height: 30px;
  /*margin-right: 5%;*/
}
.daterangepicker .ranges .input-mini {
  width: 80%;
}
.daterangepicker .applyBtn {
  margin-left: 10px;
}
.daterangepicker.opensright .ranges {
  margin-top: 45px;
  margin-left: 244px;
}
.daterangepicker.opensright .ranges ul li:last-child {
  display: none;
}
.daterangepicker.opensright .ranges,
.daterangepicker.opensright .calendar,
.daterangepicker.openscenter .ranges,
.daterangepicker.openscenter .calendar {
  float: left;
}
.daterangepicker .ranges .range_inputs > div:nth-child(2) {
  padding-left: 0;
  margin-left: 11px;
}
.daterangepicker.dropdown-menu {
  width: 662px;
}
.daterangepicker .right {
  position: absolute;
  left: 244px;
}
.carousel {
  position: absolute;
  top: 0;
}
.videoThumnail,
.videoThumnail video,
.audioThumnail {
  width: 50px;
  height: 50px;
  position: absolute;
  margin-right: 10px;
  cursor: pointer;
  float: left;
}
.videoThumnail video {
  background-color: #000;
}
.videoThumnail img {
  margin-top: 5px;
  margin-left: 5px;
  width: 40px;
  height: 40px;
  position: absolute;
  z-index: 1;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.4);
}
.videoThumnail img:hover {
  background-color: transparent;
}
.bootbox-body video {
  width: 100%;
  max-height: 600px;
}
.audioThumnail img {
  width: 50px;
  height: 50px;
}
.cboxLoadedContent img {
  max-height: 600px;
}
.imgDialog .modal-content,
.videoDialog .modal-content {
  background-color: transparent;
  border: 0;
  box-shadow: none;
  border: none;
}
.imgDialog .turnLeft,
.videoDialog .turnLeft {
  width: 100px;
  height: 100%;
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  background: url("/resource/img/left.png") center center no-repeat;
  opacity: 0;
}
.imgDialog .turnLeft:hover,
.videoDialog .turnLeft:hover {
  opacity: 1;
}
.imgDialog .turnRight,
.videoDialog .turnRight {
  width: 100px;
  height: 100%;
  position: absolute;
  cursor: pointer;
  top: 0;
  right: 0;
  background: url("/resource/img/right.png") center center no-repeat;
  opacity: 0;
}
.imgDialog .turnRight:hover,
.videoDialog .turnRight:hover {
  opacity: 1;
}
.imgDialog .cboxPhoto,
.videoDialog .cboxPhoto {
  cursor: pointer;
}
.imgDialog .digcloseBtn,
.videoDialog .digcloseBtn {
  position: absolute;
  right: 0;
  top: 0;
  color: #fff;
  cursor: pointer;
  z-index: 100;
}
.dz-download {
  display: none;
}
.container .dropzone .dz-image-preview {
  padding: 0;
  box-shadow: 0;
  border: 0;
}
.container .dropzone .dz-image-preview .dz-remove {
  margin-top: -2px;
  width: 100%;
  height: 20px;
  display: inline-block;
  background-color: transparent;
}
.container .dropzone .dz-image-preview .dz-remove i {
  display: none;
}
.container .dropzone .dz-image-preview .dz-remove:after {
  content: attr(title);
  font-size: 10px;
}
.container .dropzone .dz-image-preview .dz-remove:hover {
  border-radius: 0 0 5px 5px;
}
.container .dropzone .dz-image-preview img:hover {
  display: block;
}
