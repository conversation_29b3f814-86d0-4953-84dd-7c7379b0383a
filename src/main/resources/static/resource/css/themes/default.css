/* Colors */
/* Primary colors */
.logo_facilityone{
    /*background: url(../../img/logo.png) no-repeat;*/
    height: 50px;
    display: block;
    background-position: 30% 50%;
    /*border-right: 1px solid #e7eaec;*/
    height: 100%;
    width: 220px;
    float: left;

}
.header-collapse .logo_facilityone {
    /*background: url(../../img/logo-collapse.png) no-repeat;*/
    background-position: 50%;
    margin-left:1px;

}
.logo_cloud{
    background: url("../../img/common/logo_cloud.png");
    margin-top: 7px;
    margin-left: 2px;
    width: 42px;
    height: 28px;
    float: left;
}

#page {
    background: none repeat scroll 0 0 #39435C;
}

.breadcrumb li .upper {
    color: #444;
}

.breadcrumb li .current {
    color: #999;
}

#content .padding-left-0 {
    padding-left: 0px;
}

#content .padding-right-0 {
    padding-right: 0px;
}

#content .btn {
    border: none;
    font-family: inherit;
    font-size: 14px;
    color: inherit;
    background: none;
    cursor: pointer;
    padding: 5px 18px;
    display: inline-block;
    margin-right: 10px;
    outline: none;
    position: relative;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

/*#content .btn:after {*/
    /*content: '';*/
    /*position: absolute;*/
    /*z-index: -1;*/
    /*-webkit-transition: all 0.3s;*/
    /*-moz-transition: all 0.3s;*/
    /*transition: all 0.3s;*/
/*}*/

/* Pseudo elements for icons */
#content .btn:before {
    font-family: 'icomoon';
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    position: relative;
    -webkit-font-smoothing: antialiased;
}

/*#content .btn:active {*/
    /*top: 2px;*/
/*}*/

#content .btn-toolbar .btn {
    padding: 0 3px;
}

/* Button Color */

/*i.fa.fa-minus {*/
/*margin-right: 12px;*/
/*margin-left: -8px;*/
/*}

i.fa.fa-plus {
    margin-right: 12px;
    margin-left: -8px;
}*/

#content .btn-1, #content .btn-4 {
    color: #1ab394;
    background-color: #fff;
    border: 1px solid #1ab394;
    -webkit-transition: none;
    -moz-transition: none;
    transition: none;
}

#content .btn-add {
    color: #1ab394;
    background-color: #1ab394;
    border: 1px solid #1ab394;
    -webkit-transition: none;
    -moz-transition: none;
    transition: none;
    padding: 5px 0px 7px;
    width:100%;
    max-width:52px;
    min-width: 40px;
    text-align:center;
}

#content .btn-build {
    color: #1c83c6;
    background-color: #fff;
    border: 1px solid #1c83c6;
    margin-top: 2px;
    -webkit-transition: none;
    -moz-transition: none;
    transition: none;
}

#content .btn-build:hover {
    background-color: #1c83c6;
    color: #fff;
    border: 1px solid #1c83c6;
}

#content .btn-build:active {
    background-color: #fff;
    color: #1c83c6;
    border: 1px solid #1c83c6;
}

#content .btn-delete {
    color: #ff5353;
    background-color: #fff;
    border: 1px solid #ff5353;
    margin-top: 2px;
    border-radius: 3px;
    -webkit-transition: none;
    -moz-transition: none;
    transition: none;
}

#content .btn-delete:hover {
    background-color: #ff5353;
    color: #fff;
    border: 1px solid #ff5353;
}

#content .btn-delete:active{
    background-color: #fff;
    color: #ff5353;
    border: 1px solid #ff5353;
}

#content .btn-3 {
    border: 1px solid #ccc;
    border-radius: 0;
    padding: 4px 17px;
}

#content .btn-3:hover {
    background: #efefef;
}

/*#content .btn-1:active, #content .btn-4:active {*/
    /*top: 2px;*/
/*}*/

#content .btn-1, #content .btn-2 {
    border-radius: 3px;
    /*float: left;*/
    margin-top: 2px;
}
#content .form-group-clear{
    padding: 0;
    margin: 0;
    margin-bottom: 15px;
}

#content .btn-4 {
    border-radius: 2px;
    float: left;
    height: 34px;
}

#content .btn-1:hover, #content .btn-4:hover {
    background-color: #1ab394;
    color: #fff;
}

#content .btn-1:active, #content .btn-4:active {
    background-color: #fff;
    color: #1ab394;
    border: 1px solid #1ab394;
}

#content .btn.btn-danger {
    background-color: #f05050;
    color: #ffffff;
    border: none;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
}
#content .btn.btn-danger:hover,
#content .btn.btn-danger:active,
#content .btn.btn-danger.hover {
    background-color: #ee3939;
    color: #ffffff;
    border: none;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
}
#content .btn.btn-danger i.fa{
    padding-right: 5px;
}

.button-bar,
.s-button-tools {
    overflow: hidden;
    padding: 18px 0 11px;
    background-color: transparent;
    z-index: 1000;
    width: 100%;
    /*min-height: 50px;*/
}

.content-body {
    /*margin-top: 5px;*/
}

/*logo*/
.navbar-brand .logo {
    background: url("../../img/logo.png");
    margin-top: 13px;
    margin-left: 82px;
    width: 117px;
    height: 23px;
}

/* Start Navbar */
.navbar {
    background: none repeat scroll 0 0 #375471;
    border: none;
    border-radius: 0;
    margin: 0;
}

.navbar .navbar-brand {
    float: left;
    font-size: 18px;
    padding: 0;
    width: 292px;
    height: 50px;
}

.navbar .dropdown-toggle {
    color: #FFFFFF;
    padding: 0 20px;
    line-height: 60px;
}

.navbar .dropdown-toggle i {
    color: #FFFFFF;
    font-size: 17px;
}

.navbar .dropdown-toggle .badge {
    background-color: #ff9e36;
    border-radius: 99px !important;
    font-size: 12px !important;
    font-weight: 300;
    height: 16px;
    padding: 2px 6px;
    position: absolute;
    left: 27px;
    text-align: center;
    text-shadow: none !important;
    top: 9px;
}

.navbar .dropdown-toggle .username {
}

.navbar #header-notification .dropdown-toggle i,
.navbar #header-message .dropdown-toggle i,
.navbar #header-tasks .dropdown-toggle i {
    color: #486d93;
    text-shadow: 1px 1px #b4c7da, -1px -1px #375471;
}

.navbar #navbar-left li.dropdown {
    background-color: #6f94b8;
    border-right: 1px solid #507aa4;
    font-weight: 400;
}

.navbar #navbar-left li.dropdown:first-child {
    border-left: 1px solid #507aa4;
}

.navbar #navbar-left i {
    margin-right: 5px;
    color: #FFFFFF;
}

.navbar .nav.navbar-nav.pull-right {
    margin-right: 0;
}

/* Navbar Dropdown */
.dropdown-menu {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #C7CBD5;
    display: none;
    float: left;
    left: 0;
    font-size: 14px;
    list-style: none outside none;
    margin: 0;
    max-width: 345px;
    min-width: 166px;
    padding: 0;
    position: absolute;
    text-shadow: none;
    top: 100%;
    z-index: 1000;
    font-family: "微软雅黑", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.dropdown-menu .dropdown-title {
    background: none repeat scroll 0 0 #486d93;
    color: #FFFFFF;
    display: block;
    font-weight: 600;
    line-height: 30px;
    padding: 5px 10px;
}

.dropdown-menu .dropdown-title i {
    margin-right: 5px;
}

.dropdown-menu .footer {
    display: block;
}

.dropdown-menu .footer a {
    background-color: #fff !important;
    font-weight: 600;
    height: 58px;
    line-height: 58px;
    text-align: center;
    border-bottom: none !important;
    border-top: none !important;
}

.dropdown-menu .footer a:hover{
    color: #1ab394;
}

.dropdown-menu .footer a i {
    margin-left: 5px;
    font-size: 16px;
}

.dropdown-menu ul {
    margin: 0 !important;
    padding: 0 !important;
}

/* Navbar Settings Menu */
.dropdown-menu.skins {
    border-top: 1px solid #486d93;
}

/* Navbar Right Menu */
.dropdown-menu {
    background-color: #fff ;
}

.dropdown-menu li a {
    float: left;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 6px;
}

.dropdown-menu .messageTip li a {
    padding: 11px 30px 14px 20px;
    border-bottom: 1px solid #e5e5e5;
}

.dropdown-menu .messageTip li a:hover{
    background-color: #f3f3f3;
    cursor: pointer;
}

.dropdown-menu dd li a {
    height: 30px;
    line-height: 28px;
    width: 120px;
    float: left;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 6px;
}

.dropdown-menu li a:hover {
    color: #333333;
    background-color: #ffffff;
}

.dropdown-menu.context {
    /* For smaller menus */

}

.dropdown-menu.context li a {
    line-height: 30px;
}

.dropdown-menu .divider {
    height: 1px;
    border-bottom: 0 !important;
    border-top: 1px solid #cecece !important;
    color: #555555 !important;
    margin: 0 !important;
}

/* Notification */
.dropdown-menu.notification {
    border-top: 1px solid #486d93;
    width: 270px;
}

.dropdown-menu.notification .label {
    color: #FFFFFF;
    display: inline-block;
    margin: -1px 10px -5px -10px !important;
    padding: 9px;
    text-align: center;
    width: 40px;
    height: 42px;
    border-radius: 0;
    position: absolute;
}

.dropdown-menu.notification .label i {
    font-size: 18px;
}

.dropdown-menu.notification .body {
    position: relative;
    display: inline-block;
    line-height: 20px;
    vertical-align: middle;
    white-space: normal;
    margin: 5px 5px 5px 40px;
    width: 100%;
}

.dropdown-menu.notification .message {
    line-height: 16px;
    display: inline-block;
    margin: 0 5px 5px 0;
}

.dropdown-menu.notification .time {
    color: #777777;
    display: inline-block;
    font-size: 12px;
}

/* Messages */
.dropdown-menu.inbox {
    border-top: 1px solid #d3dce6;
    width: 345px;
}

.dropdown-menu.inbox img {
    border-radius: 50em 50em 50em 50em;
    height: 36px;
    margin-right: 10px;
    margin-top: 5px;
    width: 36px;
    border: 1px solid #ccc;
}

.dropdown-menu.inbox .body {
    position: relative;
    display: inline-block;
    line-height: 20px;
    max-width: 245px;
    vertical-align: middle;
    white-space: normal;
    margin: 5px 0;
}

.dropdown-menu.inbox .from {
    display: inline-block;
    line-height: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.dropdown-menu.inbox .message {
    line-height: 18px;
    display: block;
    margin-bottom: 5px;
    color: #666;
    overflow: hidden;
    height: 19px;
}

.dropdown-menu.inbox .message p{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dropdown-menu.inbox .time {
    color: #999;
    font-size: 12px;
    float: right;
    /*margin-right: 20px;*/
}

.dropdown-menu.inbox .compose {
    display: block;
}

.dropdown-menu.inbox .compose :hover {
    cursor: pointer;
}

/* Tasks */
.dropdown-menu.tasks {
    border-top: 1px solid #486d93;
    width: 250px;
}

.dropdown-menu.tasks .progress {
    margin-top: 0 !important;
    margin-bottom: 10px !important;
    height: 9px !important;
}

/* User Menu */
.dropdown.user a.dropdown-toggle {
    padding: 0 20px;
}

.dropdown.user .dropdown-menu {
    border-top: none;
}

.dropdown.user img {
    border-radius: 99px;
    height: 32px;
    width: 32px;
    /* filter: url("data:image/svg+xml;filter: gray;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale"); */
    margin-right: 9px;
    margin-top: -2px;
}

.dropdown.user a:hover img,
.dropdown.user a:focus img {
    filter: none;
}

.s-angle-down {
    background: url("../../img/icon.png") no-repeat;
    width: 17px;
    height: 10px;
    display: inline-block;
    background-position: -311px -26px;
    margin-left: 10px;
}

/* Navbar Right tool */
.navbar-nav .h-tools {

}

.navbar-nav .h-tools .tclose {
    background: url("../../img/icon.png") repeat scroll 0 0 rgba(0, 0, 0, 0);
    width: 40px;
    height: 25px;
    background-position: -138px -19px;
    background-color: #ffb940;
    cursor: pointer;
}

.navbar-nav .h-tools .tset {
    background: url("../../img/icon.png") repeat scroll 0 0 rgba(0, 0, 0, 0);
    width: 40px;
    height: 25px;
    background-position: -218px -19px;
    background-color: #f7f7f7;
    cursor: pointer;
}

/* End Navbar */
/**s-menu*/
.sidebar-menu .mymenu .open i.s-menu-visitor {
    background-image: url('../../img/common/icon_visitor.png');
}

.sidebar-menu .mymenu .open i.s-menu-service {
    /*background-position: -309px -332px;*/
    background-image: url('../../img/common/icon_service.png');
}

.sidebar-menu .mymenu .open i.s-menu-bulletin {
    /*background-position: -309px -462px;*/
    background-image: url('../../img/common/icon_bulletin.png');
}

.sidebar-menu .mymenu .open i.s-menu-workorder {
    /*background-position: -568px -332px;*/
    background-image: url('../../img/common/icon_wo.png');
}

.sidebar-menu .mymenu .open i.s-menu-epayment {
    /*background-position: -959px -332px;*/
    background-image: url('../../img/common/icon_payment.png');
}

.sidebar-menu .mymenu .open i.s-menu-patrol {
    /*background-position: -830px -332px;*/
    background-image: url('../../img/common/icon_patrol.png');
}

.sidebar-menu .mymenu .open i.s-menu-sign {
    background-image: url('../../img/common/icon_sign.png');
}

.sidebar-menu .mymenu .open i.s-menu-preventive {
    /*background-position: -701px -332px;*/
    background-image: url('../../img/common/icon_pm.png');
}

.sidebar-menu .mymenu .open i.s-menu-undertake {
    background-image: url('../../img/common/icon_under.png');
}

.sidebar-menu .mymenu .open i.s-menu-seedling {
    background-image: url('../../img/common/icon_seedling.png');
}

.sidebar-menu .mymenu .open i.s-menu-seedlingIssues {
    background-image: url('../../img/common/icon_seedlingIssues.png');
}

.sidebar-menu .mymenu .open i.s-menu-asset {
    /*background-position: -439px -332px;*/
    background-image: url('../../img/common/icon_asset.png');
}

.sidebar-menu .mymenu .open i.s-menu-inventory {
    /*background-position: -959px -332px;*/
    background-image: url('../../img/common/icon_inventory.png');
}

.sidebar-menu .mymenu .open i.s-menu-contract {
    background-image: url('../../img/common/icon_contract.png');
}

.sidebar-menu .mymenu .open i.s-menu-vendor {
    background-image: url('../../img/common/icon_vendor.png');
    margin-right: 4px;
    width: 17px;
}

.sidebar-menu .mymenu .open i.s-menu-energy {
    /*background-position: -959px -332px;*/
    background-image: url('../../img/common/icon_energy.png');
}

.sidebar-menu .mymenu .open i.s-menu-report {
    /*background-position: -180px -462px;*/
    background-image: url('../../img/common/icon_report.png');
}

.sidebar-menu .mymenu .open i.s-menu-knowledge {
    /*background-position: -309px -462px;*/
    background-image: url('../../img/common/icon_kb.png');
}

.sidebar-menu .mymenu .open i.s-menu-knowledge {
    /*background-position: -309px -462px;*/
    background-image: url('../../img/common/icon_kb.png');
}

.sidebar-menu .mymenu .open i.s-menu-organize {
    /*background-position: -180px -332px;*/
    background-image: url('../../img/common/icon_org.png');
}

.sidebar-menu .mymenu .open i.s-menu-system {
    /*background-position: -51px -332px;*/
    background-image: url('../../img/common/icon_sys.png');
}

.sidebar-menu .mymenu .open i.s-menu-user {
    /*background-position: -830px -332px;*/
    background-image: url('../../img/common/icon_user.png');
}

.sidebar-menu .mymenu .open i.fa-th-large{
    background-image: url('../../img/common/icon_projectm.png');
}

.sidebar-menu .mymenu .open i.s-menu-ezviz {
    background-image: url('../../img/common/icon_ezviz.png');
}

.sidebar-menu .mymenu .open i.s-menu-initialize {
    background-image: url('../../img/common/icon_initialize.png');
}


.sidebar-menu .mymenu i.s-menu-system {
    /*background-position: -51px -332px;*/
    background-image: url('../../img/common-img/icon_sys.png');
}

.sidebar-menu .mymenu .current i.s-menu-system {
    /*background-position: -51px -332px;*/
    background-image: url('../../img/common/icon_sys.png');
}

.sidebar-menu .mymenu i.s-menu-organize {
    /*background-position: -180px -332px;*/
    width: 17px;
    background-image: url('../../img/common-img/icon_org.png');
}

.sidebar-menu .mymenu i.s-menu-service {
    /*background-position: -309px -332px;*/
    background-image: url('../../img/common-img/icon_service.png');
}

.sidebar-menu .mymenu i.s-menu-asset {
    /*background-position: -439px -332px;*/
    background-image: url('../../img/common-img/icon_asset.png');
}

.sidebar-menu .mymenu i.s-menu-workorder {
    /*background-position: -568px -332px;*/
    background-image: url('../../img/common-img/icon_wo.png');
}

.sidebar-menu .mymenu i.s-menu-preventive {
    /*background-position: -701px -332px;*/
    background-image: url('../../img/common-img/icon_pm.png');
}

.sidebar-menu .mymenu i.s-menu-patrol {
    /*background-position: -830px -332px;*/

    background-image: url('../../img/common-img/icon_patrol.png');
}

.sidebar-menu .mymenu i.s-menu-user {
    /*background-position: -830px -332px;*/
    background-image: url('../../img/common-img/icon_user.png');
}

.sidebar-menu .mymenu i.s-menu-sign {
    background-image: url('../../img/common-img/icon_sign.png');
}

.sidebar-menu .mymenu i.s-menu-monitoring {
    background-image: url('../../img/common/icon_monitoring.png');
}

.sidebar-menu .mymenu i.s-menu-contract {
    background-image: url('../../img/common-img/icon_contract.png');
}

.sidebar-menu .mymenu i.s-menu-initialize {
    background-image: url('../../img/common-img/icon_initialize.png');
}

.sidebar-menu .mymenu i.s-menu-vendor {
    background-image: url('../../img/common-img/icon_vendor.png');
    margin-right: 4px;
    width: 17px;
}

.sidebar-menu .mymenu i.s-menu-inventory {
    /*background-position: -959px -332px;*/
    background-image: url('../../img/common-img/icon_inventory.png');
}

.sidebar-menu .mymenu i.s-menu-mobile {
    background-position: -51px -462px;
}

.sidebar-menu .mymenu i.s-menu-energy {
    /*background-position: -959px -332px;*/
    background-image: url('../../img/common-img/icon_energy.png');
}

.sidebar-menu .mymenu i.s-menu-epayment {
    /*background-position: -959px -332px;*/
    background-image: url('../../img/common-img/icon_payment.png');
}

.sidebar-menu .mymenu i.s-menu-report {
    /*background-position: -180px -462px;*/
    background-image: url('../../img/common-img/icon_report.png');
}

.sidebar-menu .mymenu i.s-menu-knowledge {
    /*background-position: -309px -462px;*/
    background-image: url('../../img/common-img/icon_kb.png');
}
.sidebar-menu .mymenu i.s-menu-home {
    /*background-position: -309px -462px;*/
    background-image: url('../../img/common-img/icon_home.png');
}

.sidebar-menu .mymenu .current i.s-menu-home {
    background-image: url('../../img/common/icon_home.png');
}

.sidebar-menu .mymenu i.fa-th-large{
    background-image: url('../../img/common-img/icon_projectm.png');
}
.sidebar-menu .mymenu i.fa-th-large:before{content:""}

.sidebar-menu .mymenu i.s-menu-bulletin {
    /*background-position: -309px -462px;*/
    background-image: url('../../img/common-img/icon_bulletin.png');
}

.sidebar-menu .mymenu i.s-menu-visitor {
    width: 17px;
    background-image: url('../../img/common-img/icon_visitor.png');
}

.sidebar-menu .mymenu i.s-menu-monitoring, .menu .is-opened i.s-menu-contract {
    background-image: url('../../img/common-img/icon_monitoring.png');
}

.menu i.s-menu-contract {
    background-image: url('../../img/common-img/icon_monitoring.png');
}

.sidebar-menu .mymenu i.s-menu-undertake {
    background-image: url('../../img/common-img/icon_under.png');
}

.sidebar-menu .mymenu i.s-menu-seedling {
    background-image: url('../../img/common-img/icon_seedling.png');
}

.sidebar-menu .mymenu i.s-menu-seedlingIssues {
    background-image: url('../../img/common-img/icon_seedlingIssues.png');
}

.sidebar-menu .mymenu i.s-menu-ezviz {
    background-image: url('../../img/common-img/icon_ezviz02.png');
}

.sidebar-menu .mymenu i.s-menu-decision {
    /*background-position: -180px -462px;*/
    background-image: url('../../img/common-img/icon-decision.png');
}
.sidebar-menu .mymenu .open i.s-menu-decision {
    background-image: url('../../img/common/icon-decision.png');
}
.sidebar-menu .mymenu i.s-menu-parking {
    width: 17px;
    background-image: url('../../img/common-img/cl_icon1.png');
}
.sidebar-menu .mymenu .current i.s-menu-parking {
    background-image: url('../../img/common/cl_icon2.png');
}


/* Start Sidebar */
#page #main-content {
    margin-left: 220px;
    margin-top: 0;
    padding-top:50px;
}

#sidebar {
    background: none repeat scroll 0 0 #ffffff;
    padding: 0 !important;
    width: 220px;
    position: absolute;
    top: 50px;
    /* border-bottom: 1px solid #39435C;*/
}

.sidebar-fixed {
    /*position: fixed !important;*/
}

#sidebar * {
    text-overflow: ellipsis;
}

.sidebar-menu > ul {
    list-style: none;
    margin: 0;
    padding: 0;
    /*margin-top: 15px;*/
    /*border-bottom: 1px solid #383e4c !important;*/
    /*border-top: 1px solid #383e4c !important;*/
}

.sidebar-menu > ul > li {
    display: block;
    margin: 0;
    padding: 0;
    border: 0px;
}
.mini-menu .slimScrollDiv .sidebar-menu > ul > li > a{
    padding: 13px 15px 10px 10px;
}
.slimScrollDiv .sidebar-menu > ul > li > a{
    padding: 13px 15px 13px 15px;
}

.mini-menu .slimScrollDiv .sidebar-menu > ul > li > ul.sub > li > a {
    padding: 14px !important;
}

.sidebar-menu > ul > li > a {
    display: block;
    position: relative;
    margin: 0;
    border: 0 none;
    padding: 12px 15px 10px 10px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
}

.sidebar-menu > ul > li.current > a{
    background: #FFFFFF;
}

.sidebar-menu > ul > li a i {
    color: #cdd0d8;
    font-size: 16px;
    margin-right: 5px;
}

.sidebar-menu > ul > li.has-sub {
    position: relative;
}

.sidebar-menu > ul > li.has-sub .sub {
    position: relative;
    background: #ffffff;
    /*border-top: 1px solid #2f4050;*/
    /*box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) inset;*/
}

.sidebar-menu > ul > li.has-sub .sub .has-sub-sub .sub-sub {
    position: relative;
    background: #ffffff;
    padding-left: 0px;
    /*border-top: 1px solid #2f4050;*/
    /*box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) inset;*/
}

.sidebar-menu > ul > li.active > a {
    border: none;
    text-shadow: none;
}

.mini-menu .sidebar-menu ul > li .clearfix .arrow {
    display: none;
}
.sidebar-menu ul > li .clearfix .arrow{
    /*float: right;*/
    /*margin-top: 1px;*/
    /*margin-right: 5px;*/
    /*display: inline;*/
    /*font-size: 16px;*/
    /*font-family: FontAwesome;*/
    /*height: auto;*/
    /*content: "\f0d9";*/
    /*font-weight: 300;*/
    /*text-shadow: none;*/
    width: 6px;
    height: 10px;
    position: absolute;
    position: absolute;
    top: 50%;
    right: 24px;
    margin-top: -4px;
    transition: transform .3s;
    font-size: 12px;
    background: url("../../img/arrow-left.png") no-repeat center;
}

.sidebar-menu .mymenu .has-sub .sub .has-sub-sub .sublevel .arrow {
    width: 16px;
    height: 16px;
    position: absolute;
    bottom: 28%;
    right: 20px;
    margin-top: -7px;
    transition: transform .3s;
    font-size: 12px;
    background: url(../../img/arrow-left.png) no-repeat center;
}

.sidebar-menu .mymenu .has-sub .sub .has-sub-sub .sublevel{
    position: relative;
}
.sidebar-menu .mymenu .has-sub .sub .has-sub-sub .sublevel .arrow.open {
    transform: rotateZ(-90deg);
    transition: transform .3s;
    background: url(../../img/arrow-left.png) no-repeat center;
}

.sidebar-menu ul > li > a .arrow.open:before {
    /*content: "\f0d7";*/
}

.sidebar-menu > ul > li > a .arrow.open {
    /*float: right;*/
    /*margin-top: 1px;*/
    /*margin-right: 5px;*/
    /*display: inline;*/
    /*font-family: FontAwesome;*/
    /*height: auto;*/
    /*font-size: 16px;*/
    /*content: "\f0d7";*/
    /*font-weight: 300;*/
    /*text-shadow: none;*/
    transform: rotateZ(-90deg);
    transition: transform .3s;
    background: url(../../img/arrow-left.png) no-repeat center;
}

/*.sidebar-menu > ul > li > ul.sub:before {*/
    /*-moz-border-bottom-colors: none;*/
    /*-moz-border-left-colors: none;*/
    /*-moz-border-right-colors: none;*/
    /*-moz-border-top-colors: none;*/
    /*border-color: #b0b5c2;*/
    /*border-image: none;*/
    /*border-style: dotted;*/
    /*border-width: 0 0 0 1px;*/
    /*bottom: 0;*/
    /*content: "";*/
    /*display: block;*/
    /*position: absolute;*/
    /*top: 0;*/
    /*z-index: 1;*/
/*}*/

/*.sidebar-menu > ul > li > ul.sub > li:before {*/
    /*border-top: 1px dotted #b0b5c2;*/
    /*content: "";*/
    /*display: inline-block;*/
    /*position: absolute;*/
    /*width: 17px;*/
    /*margin: 5% 0 0;*/
/*}*/

.sidebar-menu > ul > li > ul.sub {
    display: none;
    list-style: none;
    clear: both;
    padding-left: 0px;
}

.sidebar-menu > ul > li > ul.sub .has-sub-sub ul.sub-sub {
    display: none;
    list-style: none;
    clear: both;
}

.sidebar-menu > ul > li.active > ul.sub {
    display: block;
}

.sidebar-menu > ul > li.active > ul.sub .has-sub-sub ul.sub-sub {
    display: block;
}

.sidebar-menu > ul > li > ul.sub > li,
.sidebar-menu > ul > li > ul.sub .has-sub-sub ul.sub-sub > li {
    background: none;
    margin: 0px;
    padding: 0px;
    /*margin-top: 1px !important;*/
}

.sidebar-menu > ul > li > ul.sub > li > a {
    display: block;
    margin: 0px 0px 0px 0px;
    padding: 12px 0px;
    padding-left: 50px !important;
    color: #333333;
    text-decoration: none;
    font-size: 13px;
    /*font-weight: 400;*/
    background: none;
}
.sidebar-menu > ul > li > ul.sub > li > a span{
    margin-left:0!important;
}
.sidebar-menu > ul > li   ul.sub-sub > li > a span{
    margin-left:0!important;
}
.mymenu >li>a>i,
.mymenu >li>a>span,
.has-sub >a >*{
    display:inline-block;
    float:left
}
.has-sub >a>.arrow{
    float:right;
}
.has-sub>a> .menu-text{
    width:123px;
}

.sidebar-menu > ul > li > ul.sub .has-sub-sub ul.sub-sub > li > a {
    display: block;
    margin: 0px 0px 0px 0px;
    padding: 12px 0px;
    padding-left: 64px !important;
    color: #333333;
    text-decoration: none;
    font-size: 14px;
    /*font-weight: 300;*/
    background: none;
}

.sidebar-menu > ul > li > ul.sub .has-sub-sub ul.sub-sub > li :hover,
.sidebar-menu > ul > li > ul.sub .has-sub-sub ul.sub-sub > li :focus {
    text-decoration: none;
}

.sidebar-menu > ul > li > ul.sub > li > a > i,
.sidebar-menu > ul > li > ul.sub .has-sub-sub ul.sub-sub > li > a > i {
    font-size: 13px;
}

.sidebar-menu > ul > li > a {
    /*border-bottom: 1px solid #39435C !important;*/
    /*border-top: 1px solid #39435C !important;*/
    color: #333333 !important;
}

.sidebar-menu > ul > li:last-child > a {
    border-bottom: 1px solid transparent !important;
}

.sidebar-menu > ul > li:first-child > a {
    /* border-top-color: transparent !important; */

}

.sidebar-menu > ul > li a i {
    color: #cdd0d8;
}

.sidebar-menu > ul > li > a:hover{
    background: #E1FAFF;
}

.sidebar-menu > ul > li.has-sub.open > a{
    color: #333333 !important;
}

.sidebar-menu > ul > li.has-sub.open > ul > li.has-sub-sub.open > a {
    background: #ffffff;
    color: #333333;
}

.sidebar-menu > ul > li.active > a {
    background: #3c4352 !important;
    border-top-color: transparent !important;
    color: #fff;
}

.sidebar-menu > ul > li > a.open {
    background: #313131;
}

.sidebar-menu ul > li > a .arrow:before,
.sidebar-menu > ul > li > a .arrow.open:before {
    color: #cdd0d8 !important;
}

/*.sidebar-menu ul > li.active > a .arrow:before,
.sidebar-menu > ul > li.active > a .arrow.open:before {
   color: #fff !important;
} */
.sidebar-menu > ul > li > ul.sub > li > a {
    color: #333333;
    margin-left: 0px;
    padding-left: 5px;
    font-size: 14px;
    font-weight: 400;
}

.sidebar-menu > ul > li > ul.sub > li:first-child > a {
    border-top: 0px !important;
}

.sidebar-menu > ul > li > ul.sub > li.active > a,
.sidebar-menu > ul > li > ul.sub > li > a:hover{
    background: #293846 !important;
}

.sidebar-menu > ul > li > ul.sub > li.current{
    background: #E1FAFF !important;
    border-right: 2px solid #00ACB7;
    text-indent: -3px;
    color: #00ACB7!important;
}

.sidebar-menu > ul > li > ul.sub > li.current a {
    color: #00ACB7;
}

.sidebar-menu > ul > li > ul.sub > li > a:hover {
    background: #E1FAFF !important;
    color: #00ACB7;
}

/* Search */
.sidebar-menu #search-bar {
    display: block;
    text-align: center;
}

.sidebar-menu #search-bar .search {
    background: none repeat scroll 0 0 #4d5669;
    border: 0 none;
    color: #cdd0d8;
    padding: 5px 0px 5px 8px;
    width: 90%;
    margin: 0 auto 10px;
    padding-right: 30px !important;
}

.sidebar-menu #search-bar .search-icon {
    display: block;
    font-size: 14px;
    line-height: 1;
    position: absolute;
    right: 18px;
    top: 18px;
    color: #ffffff;
}

/***
* menu icon
*/

.sidebar-menu > ul.mymenu > li > ul.sub li:hover {
    background: #E1FAFF !important;
}

.sidebar-menu a > span {
    vertical-align: middle;
    margin-left: 5px;
}

.sidebar-menu i.fa {
    background: url("../../img/icon.png") no-repeat;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-left: 5px;
    line-height: 25px;
    text-align: center;
    margin-top: 2px;
    filter: brightness(0) saturate(100%) invert(14%) sepia(9%) saturate(1041%) hue-rotate(353deg) brightness(93%) contrast(93%);
}

/**组织管理*/
.sidebar-menu i.fa-organization {
    background-position: -392px -216px;
}

.sidebar-menu i.fa-location {
    background-position: -457px -216px;
}

.sidebar-menu i.fa-employee {
    background-position: -525px -216px;
}

.sidebar-menu i.fa-workteam {
    background-position: -591px -216px;
}

.sidebar-menu i.fa-position {
    background-position: -656px -216px;
}

.sidebar-menu i.fa-customer {
    background-position: -723px -216px;
}

/**资产管理*/
.sidebar-menu i.fa-asset-class {
    background-position: -709px -277px;
}

.sidebar-menu i.fa-asset-list {
    background-position: -762px -277px;
}

.sidebar-menu i.fa-asset-notice {
    background-position: -817px -277px;
}

/**系统管理*/
.sidebar-menu i.fa-system-user {
    background-position: -441px -277px;
}

.sidebar-menu i.fa-system-role {
    background-position: -494px -277px;
}

.sidebar-menu i.fa-system-permission {
    background-position: -548px -277px;
}

.sidebar-menu i.fa-system-mtemplate {
    background-position: -602px -277px;
}

.sidebar-menu i.fa-system-message {
    background-position: -655px -277px;
}

/* Mini Menu Search */
.mini-menu .sidebar-menu #search-bar {
    display: block;
    text-align: center;
}

.mini-menu .sidebar-menu #search-bar .search {
    background: none repeat scroll 0 0 #4d5669;
    border: 0 none;
    border-radius: 4px;
    color: #cdd0d8;
    padding: 8px 10px;
    width: 90%;
    margin: 0 auto 10px;
}

.mini-menu .sidebar-menu #search-bar .search-icon {
    display: block;
    font-size: 14px;
    line-height: 1;
    position: absolute;
    right: 22px;
    top: 29px;
    color: #ffffff;
}

/* Quick launch */
.sidebar-menu .quicklaunch-lg {
    line-height: 39px;
    margin-bottom: 0;
    max-height: 41px;
    text-align: center;
}

.sidebar-menu .quicklaunch-lg .btn {
    margin-right: 1rem;
}

.sidebar-menu .quicklaunch-mini {
    display: none;
    font-size: 0;
    line-height: 18px;
    padding-bottom: 2px;
    padding-top: 2px;
    text-align: center;
    width: 50px;
}

/* Sidebar collapse */
.switcher {
    padding: 10px 10px 0 0;
    position: absolute;
    top: 10px;
    left: 10px;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
    color: #FFFFFF;
    padding: 6px 8px;
    border-radius: 0px;
}

.sidebar-collapse-icon {
    background: url("../../img/icon.png") repeat scroll 0 0 rgba(0, 0, 0, 0);
    background-position: -53px -274px;
    width: 35px;
    height: 35px;
    display: block;
}

.sidebar-collapse > [class*="fa-"],
.switcher > [class*="fa-"] {
    color: #FFFFFF;
    cursor: pointer;
    display: inline-block;
    padding: 0 5px;
    position: relative;
    float: right;
}

.menu-text .badge {
    position: absolute;
    right: 35px;
    line-height: 15px;
    padding-bottom: 3px;
    padding-top: 2px;
    text-shadow: none;
    background-color: #dd5a43 !important;
}

/* Min Menu */
.mini-menu#sidebar {
    width: 50px;
}

.mini-menu#sidebar:before {
    width: 50px;
}

.mini-menu#sidebar .sidebar-menu > ul > li > a > .menu-text .badge {
    top: 15px;
    right: 10px;
}

.mini-menu#sidebar .sidebar-menu > ul > li > a > .menu-text .label {
    position: absolute;
    top: 15px;
    right: 10px;
}

.mini-menu#sidebar .sidebar-menu > ul > li > a > .menu-text {
    background-color: #ffffff;
    display: none;
    height: 50px;
    left: 44px;
    line-height: 38px;
    padding-left: 12px;
    position: absolute;
    top: 0;
    width: 174px;
    padding: 5px 0 0 15px;
    z-index: 121;
    /*border: 1px solid #0d0e11;*/
}

.mini-menu#sidebar .sidebar-menu > ul > li.has-sub > a > .menu-text {
    border-right: none;
    border-top: none;
}

.mini-menu#sidebar .sidebar-menu > ul > li:hover > a > .menu-text {
    display: block;
}

.mini-menu#sidebar .sidebar-menu > ul > li > ul.sub {
    /*background-color: #22262e;*/
    /*border: 1px solid #0d0e11;*/
    display: none !important;
    left: 50px;
    padding-bottom: 2px;
    padding-top: 50px;
    position: absolute;
    top: 0;
    width: 176px;
    z-index: 120;
    /*border-top: 1px solid #22262e;*/
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) inset;
}
.mini-menu#sidebar .sidebar-menu > ul.menu-en > li  span.menu-text{
    line-height: normal;
}

.mini-menu#sidebar .sidebar-menu > ul > li:hover > ul.sub {
    display: block !important;
}

.mini-menu#sidebar .sidebar-menu ul > li > a .arrow:before {
    display: none;
}

.mini-menu#sidebar .sidebar-menu > ul > li > a .arrow.open:before {
    display: none;
}

.mini-menu#sidebar .quicklaunch-mini {
    display: block;
}

.mini-menu#sidebar #quicklaunch {
    position: relative;
}

.mini-menu#sidebar .quicklaunch-lg {
    background-color: #2d323d;
    display: none;
    left: 50px;
    position: absolute;
    top: -1px;
    width: 190px;
    z-index: 20;
    padding: 0 0 0 1px;
    max-height: 50px;
    height: 115%;
    border: 1px solid #0d0e11;
}

.mini-menu#sidebar .quicklaunch-lg .btn {
    margin-right: 0.25rem;
}

.mini-menu#sidebar #quicklaunch:hover .quicklaunch-lg {
    display: block;
}

.mini-menu#sidebar .slimScrollDiv,.mini-menu#sidebar .sidebar-menu{
    overflow: visible!important;
}

/* End Sidebar */
/* shang button */
/*.s-button-group button {*/
/*float: left;*/
/*}*/

/* Button delete */

#content .btn-2 {
    background: #fff;
    color: #ff9e36;
    padding: 5px 18px;
    overflow: hidden;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    backface-visibility: hidden;
    border: 1px solid #ff9e36;
    overflow: hidden;
}

#content .btn-2:hover {
    background: #ff9e36;
    color: #fff;
}

#content .btn-2:active {
    background: #fff;
    color: #ff9e36;
    border: 1px solid #ff9e36;
}
/*#content .btn-2:active {*/
    /*background: #e08e0b;*/
    /*top: 2px;*/
/*}*/

#content .btn-2 span {
    display: inline-block;
    width: 100%;
    height: 100%;
    /*-webkit-transition: all 0.3s;*/
    /*-webkit-backface-visibility: hidden;*/
    /*-moz-transition: all 0.3s;*/
    /*-moz-backface-visibility: hidden;*/
    /*transition: all 0.3s;*/
    /*backface-visibility: hidden;*/
}

#content .btn-2:before {
    position: absolute;
    height: 100%;
    line-height: 1.3;
    font-size: 180%;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
    top: -100%;
    padding-left: 4px;
}

#content .btn-2:active:before {
    color: #b6c68d;
}

/*#content .btn-2:hover span {*/
    /*-webkit-transform: translateY(300%);*/
    /*-moz-transform: translateY(300%);*/
    /*-ms-transform: translateY(300%);*/
    /*transform: translateY(300%);*/
/*}*/

#content .btn-2:hover:before {
    top: 0;
}

#content .btn-save {
    float: right;
    margin-right: 0px;
}

/*#content .btn-save:before {*/
    /*content: "\f0c7";*/
    /*font-style: normal;*/
    /*font-weight: normal;*/
    /*font-family: FontAwesome;*/
/*}*/

#content .btn-save:hover {
    float: right;
}

#content .btn-del {
    float: right;
    margin-right: 10px;
}

/*#content .btn-del:before {*/
    /*content: "\f014";*/
    /*font-style: normal;*/
    /*font-weight: normal;*/
    /*font-family: FontAwesome;*/
/*}*/

#content .btn-del:hover {
    float: right;
}

.s-button-search-multi {
    background: url("../../img/icon.png") no-repeat;
    width: 48px;
    height: 35px;
    background-position: -1021px -79px;
    border: 0;
}

.s-button-phone {
    background: url("../../img/icon.png") no-repeat;
    width: 26px;
    height: 26px;
    background-position: -959px -80px;
    display: inline-block;
    vertical-align: middle;
}

/*服务台*/
.container table.dataTable.no-footer {
    border: 1px solid #dfdfdf;
}
/*datatable*/
.container .dataTables_wrapper {
    background-color: #FFFFFF;
}
.container table.dataTable.no-footer {
    border: 0;
    border-bottom: 1px solid #dfdfdf;
    padding-bottom: 1px;
    table-layout: fixed;
}

.container table.dataTable thead tr:first-child {
    background-color: #fff;
    color: #f9f9f9;
    height: 40px;
}

.container table.dataTable thead tr:first-child th {
    background-color: transparent;
    font-size: 14px;
    font-weight: 400;
    color: #666;
    border-bottom: 2px solid #e0e6ed;
    padding-left: 18px;
    padding-right: 18px;
}

@-moz-document url-prefix() {
    .container table.dataTable thead tr:first-child th {
        background-color: transparent;
        font-size: 14px;
        font-weight: 400;
        color: #666;
        border-bottom: 2px solid #e0e6ed;
        padding-left: 6px;
        padding-right: 6px;
    }
}

.container table.dataTable thead tr:first-child th:last-child {
    border-right: 0px;
}

table.dataTable thead th:last-child,
table.dataTable thead td:last-child {
    border-right: 0px;
}

.container table.dataTable thead th, .container table.dataTable thead td {
    border: 0;
    padding: 0px 5px;
}

.container table.dataTable.row-border tbody th,
.container table.dataTable.row-border tbody td,
.container table.dataTable.display tbody th,
.container table.dataTable.display tbody td {
    border: 0;
    /*border-right: 1px solid #dfdfdf;*/
    padding-left: 18px;
    padding-right: 18px;
    /*text-align: center;*/
}

@-moz-document url-prefix() {
    .container table.dataTable.row-border tbody th,
    .container table.dataTable.row-border tbody td,
    .container table.dataTable.display tbody th,
    .container table.dataTable.display tbody td {
        border: 0;
        /*border-right: 1px solid #dfdfdf;*/
        padding-left: 6px;
        padding-right: 6px;
        /*text-align: center;*/
    }
}

/*.container table.dataTable.display tbody td input {*/
    /*text-align: center;*/
/*}*/

.container table.dataTable.display tbody tr.odd {
    background-color: #F3F3F4;
}

.container table.dataTable.display tbody tr{
    height: 40px;
}

/*.container table.dataTable.display tbody tr.even.selected,*/
/*.container table.dataTable.display tbody tr.odd.selected,*/
/*.container table.dataTable.display tbody tr.even.selected:hover,*/
/*.container table.dataTable.display tbody tr.odd.selected:hover {*/
    /*background-color: #D8E4F2;*/
/*}*/

.container table.dataTable.display tbody tr.even.selected > .sorting_1,
.container table.dataTable.display tbody tr.even:hover.selected > .sorting_1 {
    background-color: inherit !important;
}

.container table.dataTable.display tbody tr.odd.selected > .sorting_1,
.container table.dataTable.display tbody tr.odd:hover.selected > .sorting_1{
    background-color: inherit !important;
}
.bootbox-body table.dataTable.display tbody tr> td.sorting_1, .bootbox-body table.dataTable.order-column.stripe tbody tr > td.sorting_1{
    background-color: inherit !important;
    text-align: center !important;
}
table.dataTable.display tbody tr.odd > .sorting_1, table.dataTable.order-column.stripe tbody tr.odd > .sorting_1{
    background-color: inherit !important
}

table.dataTable.display tbody tr.even > .sorting_1, table.dataTable.order-column.stripe tbody tr.even > .sorting_1{
    background-color: inherit !important;
}

/*.container table.dataTable.display tbody tr.even:hover {*/
    /*background-color: inherit;*/
/*}*/

.container table.dataTable.display tbody tr.even:hover,
.container table.dataTable.display tbody tr.odd:hover{
    background-color: #f1f1f3 !important;
}

/*.container table.dataTable.display tbody tr.odd:hover {*/
    /*background-color: #F3F3F4;*/
/*}*/

.container table.dataTable.display tbody tr.even:hover > .sorting_1 {
    background-color: inherit !important;
}

.container table.dataTable.display tbody tr.odd:hover > .sorting_1 {
    background-color: inherit !important;
}
.container .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.container .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
.container .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
    background-color: #efefef;
}

.container table.dataTable.display thead td:last-child {
    border-right: 0;
}

.container table.dataTable.display tbody {
    font-family: "微软雅黑", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: #666;
}

.container table.dataTable.display tbody tr:last-child {
    border-bottom: 1px solid #dfdfdf;
}

.container .dataTables_wrapper .dataTables_paginate .paginate_button {
    margin: 0 10px 0 0;
    border-radius: 2px;
}

.container .dataTables_wrapper .dataTables_info {
    padding-left: 12px;
    color: #475669;

}

.container table tr.table-search td {
    position: relative;
}

.container table tr.table-search i {
    background: url("../../img/icon.png") no-repeat;
    width: 35px;
    height: 100%;
    /*z-index: 10;*/
    position: absolute;
    right: 0px;
    cursor: pointer;
    text-align: center;
    transform: scale(0.9);
}

.container table tr.table-search i.t-search {
    background-position: -593px -80px;
}

.container table tr.table-search i.t-remove {
    background-position: -520px -80px;
}

.container table.dataTable thead input {
    font-size: inherit;
    padding: 3px 7px;
    padding-right: 35px;
    height: 34px;
}

.container table.dataTable thead input::-webkit-input-placeholder{
    color: #999999;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled, .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover, .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active{
    border:none !important;
    background-color: #F3F3F4 !important;
}

/* fone-detail-table datatable */
.container .fone-detail-table table.dataTable thead tr:first-child th{
    background-color: #e1e4e6;
    height: 34px;
    border-right: 1px solid #cccccc;
    font-weight: bold;
}

.container .fone-detail-table table.dataTable.display tbody tr{
    background-color: transparent !important;
}

.container .fone-detail-table table.dataTable.row-border tbody th, .container.fone-detail-table table.dataTable.row-border tbody td, .container .fone-detail-table table.dataTable.display tbody th, .container .fone-detail-table table.dataTable.display tbody td{
    border-right: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;
}

.fone-detail-table table.dataTable.row-border tbody th:last-child, .fone-detail-table table.dataTable.row-border tbody td:last-child, .fone-detail-table table.dataTable.display tbody th:last-child, .fone-detail-table table.dataTable.display tbody td:last-child{
    border-right: 0;
}

.container .fone-detail-table table.dataTable thead tr:first-child th:last-child{
    border-right: 0;
}

.container .fone-detail-table table.dataTable.no-footer{
    border: 1px solid #cccccc;
    padding-bottom: 0;
    border-bottom:none;
}

.container .fone-detail-table table.dataTable.display tbody tr{
    height: 35px;
}

.container .fone-detail-table table.dataTable thead tr:first-child th{
    border-bottom: 0;
    padding: 0 18px;
}

@-moz-document url-prefix() {
    .container .fone-detail-table table.dataTable thead tr:first-child th{
        border-bottom: 0;
        padding: 0 6px;
    }
}
/* bootbox datatable */
.bootbox .dataTables_wrapper {
    background-color: #FFFFFF;
}

.bootbox table.dataTable.no-footer {
    border: 1px solid #e0e0e0;
    border-bottom: none;
    padding-bottom: 1px;
    table-layout: fixed;
}

.bootbox table.dataTable thead tr:first-child {
    background-color: #E1E4E6;
    color: #666;
}

.bootbox table.dataTable thead tr:first-child th {
    background-color: transparent;
    font-size: 14px;
    color: #666;
    border-right: 1px solid #ccc;
    text-align: center;
    font-weight: bold;
}

.bootbox table.dataTable thead tr:first-child th:last-child {
    border-right: 0px;
}

table.dataTable thead th:last-child,
table.dataTable thead td:last-child {
    border-right: 0px;
}

.bootbox table.dataTable thead th, .container table.dataTable thead td {
    border: 0;
    padding: 6px 10px;
}

@-moz-document url-prefix() {
    .bootbox table.dataTable thead th, .container table.dataTable thead td {
        border: 0;
        padding: 6px 5px;
    }
}

.bootbox table.dataTable.row-border tbody th,
.bootbox table.dataTable.row-border tbody td,
.bootbox table.dataTable.display tbody th,
.bootbox table.dataTable.display tbody td {
    padding-left: 18px;
    padding-right: 18px;
}

@-moz-document url-prefix() {
    .bootbox table.dataTable.row-border tbody th,
    .bootbox table.dataTable.row-border tbody td,
    .bootbox table.dataTable.display tbody th,
    .bootbox table.dataTable.display tbody td {
        padding-left: 6px;
        padding-right: 6px;
    }
}
.bootbox table.dataTable.display tbody tr.odd {
    background-color: #fff;
}

.bootbox table.dataTable.display tbody tr {
    height: 40px;
}

.bootbox table.dataTable.display tbody tr.even.selected,
.bootbox table.dataTable.display tbody tr.odd.selected,
.bootbox table.dataTable.display tbody tr.even.selected:hover,
.bootbox table.dataTable.display tbody tr.odd.selected:hover {
    background-color: #D8E4F2;
}

.bootbox table.dataTable.display tbody tr.even.selected > .sorting_1,
.bootbox table.dataTable.display tbody tr.odd.selected > .sorting_1,
.bootbox table.dataTable.display tbody tr.odd:hover.selected > .sorting_1,
.bootbox table.dataTable.display tbody tr.even:hover.selected > .sorting_1 {
    background-color: #f3f3f4;
}

.bootbox table.dataTable.display tbody tr.even:hover,
.bootbox table.dataTable.display tbody tr.odd:hover {
    background-color: #f3f3f4;
}

.bootbox table.dataTable.display tbody tr.even:hover > .sorting_1,
.bootbox table.dataTable.display tbody tr.odd:hover > .sorting_1 {
    background-color: #f3f3f4;
}

.bootbox .dataTables_wrapper .dataTables_paginate .paginate_button.current,
.bootbox .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    border: 1px solid #1ab394;
}

.bootbox .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.bootbox .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
.bootbox .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
    background-color: #f3f3f4;
}

.bootbox table.dataTable.display thead td {
    border-right: 1px solid #dfdfdf;
}

.bootbox table.dataTable.display thead td:last-child {
    border-right: 0;
}

.bootbox table.dataTable.display tbody {
    font-family: "微软雅黑", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 13px;
    font-weight: 400;
    color: #333333;
}

.bootbox table.dataTable.display tbody tr:last-child {
    border-bottom: 1px solid #dfdfdf;
}

.bootbox .dataTables_wrapper .dataTables_paginate .paginate_button {
    margin: 0 10px 0 0;
    border-radius: 2px;
}

.bootbox .dataTables_wrapper .dataTables_info {
    padding-left: 12px;
    color: #475669;
}

.bootbox table tr.table-search td {
    position: relative;
}

.bootbox table tr.table-search i {
    background: url("../../img/icon.png") no-repeat;
    width: 22px;
    height: 25px;
    z-index: 10;
    position: absolute;
    right: 0px;
    cursor: pointer;
    text-align: center;
    transform: scale(0.9);
}

.bootbox table tr.table-search i.t-search {
    background-position: -593px -83px;
}

.bootbox table tr.table-search i.t-remove {
    background-position: -520px -83px;
}

.bootbox table.dataTable thead input {
    font-size: inherit;
    padding: 0px 12px 0px 0;
}

/*/!**/
/*nav nav-tabs*/
/**!/*/
/*.xia-nav-tabs-1 .box .xia-nav-tabs-2 {*/
/*margin-top: 45px;*/
/*}*/

/*.xia-nav-tabs-1 .box .header-tabs > .nav-tabs {*/
/*border: 1px solid #eee;*/
/*border-bottom: 0;*/
/*}*/

/*.xia-nav-tabs-1 .box .header-tabs > .nav-tabs > li > a:before, .container .box .header-tabs > .nav-tabs > li > a:after {*/
/*width: 0;*/
/*border: 0;*/
/*content: none;*/
/*}*/

/*.xia-nav-tabs-1 .box .header-tabs > .nav-tabs > li > a {*/
/*margin-right: 0;*/
/*border-radius: 0;*/
/*min-width: 150px;*/
/*text-align: center;*/
/*float: none;*/
/*border-top: 0;*/
/*padding-bottom: 9px;*/
/*}*/

/*.xia-nav-tabs-1 .box .header-tabs > .nav-tabs > li {*/
/*margin-left: 0;*/
/*padding: 0;*/
/*cursor: pointer;*/
/*border-right: 1px solid #ccc;*/
/*border-left: 0;*/
/*margin-right: 0;*/
/*float: left;*/
/*}*/

/*.xia-nav-tabs-1 .box .header-tabs > .nav-tabs > li:last-child {*/
/*border-right: 0;*/
/*}*/

/*.xia-nav-tabs-1 .box .header-tabs > .nav-tabs > li:hover > a {*/
/*border-bottom: 0;*/
/*padding-bottom: 13px;*/
/*background-color: #fff;*/
/*}*/

/*.xia-nav-tabs-1 .box .header-tabs > .nav-tabs > li.active > a,*/
/*.xia-nav-tabs-1 .box .header-tabs > .nav-tabs > li.active:hover > a {*/
/*border-bottom: 0;*/
/*padding-bottom: 13px;*/
/*background-color: #fff;*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs {*/
/*border-bottom: 0;*/
/*margin-bottom: 2px;*/
/*}*/

/*.xia-nav-tabs-1 .tab-content {*/
/*padding-top: 5px;*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs > li {*/
/*margin: 0 2px;*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs > li > a:before {*/
/*content: '';*/
/*position: absolute;*/
/*z-index: 1;*/
/*top: 12px;*/
/*left: 4px;*/
/*bottom: 8px;*/
/*background-image: linear-gradient(to bottom, #fff, #ddd);*/
/*border-left: 1px solid #cdcdcd;*/
/*transform: skew(170deg);*/
/*-webkit-transform: skew(170deg);*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs > li.active > a:before {*/
/*border-color: #2A5B7E;*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs > li:first-child > a:before {*/
/*border: 0;*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs > li.active > a,*/
/*.xia-nav-tabs-1 .nav-tabs > li.active > a:hover,*/
/*.xia-nav-tabs-1 .nav-tabs > li.active > a:focus {*/
/*border: 0;*/
/*border-radius: 0;*/
/*color: #333;*/
/*border-bottom: 3px solid #2A5B7E;*/
/*font-weight: bold;*/
/*background-color: transparent;*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs > li > a {*/
/*border: 0;*/
/*border-radius: 0;*/
/*color: #cdcdcd;*/
/*border-bottom: 3px solid transparent;*/
/*margin-right: 0;*/
/*font-size: 18px;*/
/*}*/

/*.xia-nav-tabs-1 .tab-content .nav-tabs > li > a {*/
/*font-size: 16px;*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs > li > a:hover,*/
/*.xia-nav-tabs-1 .nav-tabs > li > a:focus {*/
/*/!*border: 0;*!/*/
/*/!*border-radius: 0;*!/*/
/*/!*color: #686868;*!/*/
/*/!*border-bottom: 3px solid #2A5B7E;*!/*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs > li:first-child {*/
/*border-left: 0;*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs li.s-nav-button-tools {*/
/*padding: 0;*/
/*float: right;*/
/*position: relative;*/
/*margin-top: 10px;*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs li.s-nav-button-tools .s-button-tools {*/
/*padding: 0;*/
/*}*/

/*.xia-nav-tabs-1 .nav-tabs li.s-nav-button-tools .s-button-tools .s-button {*/
/*margin-top: 0;*/
/*float: right;*/
/*}*/

/* NAV TABS */
.xia-tabs.xia-nav-tabs > .nav-tabs > li > a{
    font-size: 14px;
}
.xia-tabs.xia-nav-tabs > .nav-tabs > li{
    border-top: 0;
    border-bottom: 2px solid transparent;
}
.xia-tabs.xia-nav-tabs > .nav-tabs > li.active{
    border-bottom-color: #1ab394;
    display: block;
}

.xia-nav-tabs {
    margin-bottom: 20px;
    background: #fff;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}
.xia-nav-tabs > .nav-tabs {
    margin: 0;
    border-bottom-color: #E7EAEC;
    text-align: center;
}
.xia-nav-tabs > .nav-tabs > li {
    border-top: 2px solid transparent;
    margin-bottom: -2px;
    margin-right: 5px;
    padding: 0;
}
.xia-nav-tabs > .nav-tabs > li > a {
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    border-radius: 0 !important;
    padding: 19px 26px 16px 26px;
    color: #555;
    font-weight: 400;
    font-size: 14px;
}
.xia-nav-tabs > .nav-tabs > li > a,
.xia-nav-tabs > .nav-tabs > li > a:hover {
    background: transparent;
    margin: 0;
}
.xia-nav-tabs > .nav-tabs > li:not(.active) > a:hover,
.xia-nav-tabs > .nav-tabs > li:not(.active) > a:focus,
.xia-nav-tabs > .nav-tabs > li:not(.active) > a:active {
    border-color: transparent;
}
.xia-nav-tabs > .nav-tabs > li.active {
    border-top-color: #1ab394;
}
.xia-nav-tabs > .nav-tabs > li.active > a,
.xia-nav-tabs > .nav-tabs > li.active:hover > a {
    background-color: #fff;
}
.xia-nav-tabs > .nav-tabs > li.active > a {
    border-top: 0;
    border-left-color: #f4f4f4;
    border-right-color: #f4f4f4;
    color: #1ab394;
    border-top:solid 0px transparent;
}
.xia-nav-tabs > .nav-tabs > li:first-of-type {
    margin-left: 0px;
}
.xia-nav-tabs > .nav-tabs > li:first-of-type.active > a {
    border-left-width: 0;
}
.xia-nav-tabs > .nav-tabs.pull-right {
    float: none!important;
}
.xia-nav-tabs > .nav-tabs.pull-right > li {
    float: right;
}
.xia-nav-tabs > .nav-tabs.pull-right > li:first-of-type {
    margin-right: 0px;
}
.xia-nav-tabs > .nav-tabs.pull-right > li:first-of-type.active > a {
    border-left-width: 1px;
    border-right-width: 0px;
}
.xia-nav-tabs > .nav-tabs > li.header {
    font-weight: 400;
    line-height: 35px;
    padding: 0 10px;
    font-size: 20px;
    color: #444;
    cursor: default;
}
.xia-nav-tabs > .nav-tabs > li.header > .fa,
.xia-nav-tabs > .nav-tabs > li.header > .glyphicon,
.xia-nav-tabs > .nav-tabs > li.header > .ion {
    margin-right: 10px;
}
.xia-nav-tabs > .tab-content {
    background: #fff;
    padding: 10px;
}
/** button in li */
.xia-nav-tabs li.s-nav-button-tools{
    float: right;
}
.xia-nav-tabs .s-button-tools{
    padding: 0px;
}

.xia-nav-tabs .nav .page-head-title {
    padding: 0px 25px;
    float: left;
    color: #666;
    line-height: 59px;
}

/**from
*/
.container .box-form {
    clear: both;
    margin-top: 0px;
    margin-bottom: 15px;
    padding: 15px 10px 0 10px;/*bottom由formgroup提供*/
    /*padding: 20px 20px 0px 20px;*/
    /*border: 1px solid #EEE;*/
    border-radius: 2px;
    background: #FFFFFF;
    /* overflow: hidden;*/
}

.container .form-control {
    box-shadow: none;
    -webkit-box-shadow: none;
    min-height: 34px;
    font-size: 14px;
}

.container .form-group {
    /*margin-bottom: 15px;*/
}

.container textarea.form-control {
    height: auto;
}

.container .box-form .box-title h4 {
    width: 100%;
    padding: 10px;
    margin-top: -10px;
    margin-bottom: 15px;
    /*border-bottom: 1px dashed #ddd;*/
    color: #333;
    font-family: "微软雅黑", "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.container .box-form .box-title h4 i {
    float: right;
    border-left: 1px solid #efefef;
    padding-left: 10px;
    cursor: pointer;
    color: #000;
}

.container .box-form .box-title {
    border-bottom: 0;
    background-color: #FAFAFA;
    font-size: 14px;
}

.container .box-form .box-body {
    padding: 10px;
}
.container .box-form .box-body-2{
    margin-top: -14px;
}

.container .box-form .box-title.box-title-red h4 {
    background-color: #e87769;
    border-bottom: 0;
    color: #efefef;
}

.container .box-form .box-title.box-title-simple h4 {
    border-width: 0px 0px 1px 0px;
    border-style: solid;
}

.container .box-form .box-title.box-title-green h4 {
    background-color: #7caa78;
    border-bottom: 0;
    color: #efefef;
}

.container .box-form .box-title.box-title-red h4 i,
.container .box-form .box-title.box-title-green h4 i {
    display: inline-block;
}

/** favorite
*/
.s-favorite {
    position: fixed;
    bottom: 0;
    width: 250px;
    padding: 10px;
    /*border-top: 1px solid #383e4c !important;*/
}

.sidebar-menu i.fa-favorite-add {
    background-position: -318px -209px;
    width: 40px;
    height: 40px;
    margin-left: 1px;
}

.sidebar-menu .s-favorite > span {
    width: 40px;
    height: 40px;
    background: #4d5669;
    display: inline-block;
    border-radius: 15px;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
}

.mini-menu .sidebar-menu .s-favorite {
    display: none;
}

/**
 panel
*/
.container .panel {
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: transparent;
}

.container .panel .panel-body {
    background-color: #FFF;
    padding: 10px;
}
.container .panel .panel-body.panel-body-table{
    padding: 0;
}

/**
* autocomplete
*/
.container .ms-ctn.input-sm .ms-sel-ctn, .container .ms-ctn .ms-sel-item {
    /*line-height: 32px;*/
}

.container .ms-ctn.input-sm .ms-sel-ctn .ms-sel-item .ms-close-btn, .container .ms-ctn .ms-sel-ctn .ms-sel-item .ms-close-btn {
    /*margin-top: 13px;*/
}

.container .ms-ctn.input-sm .ms-trigger .ms-trigger-ico, .container .ms-ctn .ms-trigger .ms-trigger-ico {
    margin-top: 15px;
    margin-left: 9px;
}

/**
* fullcalendar
*/
.container .fc-unthemed .fc-head {
    background-color: #2F4050;
    color: #fff;
}

.container .fc-unthemed .fc-day:hover {
    background-color: #f7f3f0;
}

/**
* bootbox
*/
.bootbox .modal-header {
    background-color:#FAFAFA;
    color: #333;
    height: 52px;
    font-size: 15px;
    padding: 10px 15px;
    text-align: right;
}
.bootbox .modal-title{
    float: left;
    font-size: 20px;
    font-weight: 500;
}

.bootbox .modal-footer {
    padding: 0;
}

.bootbox .modal-header button {
    margin-left: 10px;
    margin-right: 0;
    padding: 5px 19px;
    border-radius: 3px;
    border: none;
    outline: none;
}

.bootbox .modal-header button.btn-default-fo {
    /*background-color: #ED9C28;*/
    background-color: #f5f5f5;
    border: 1px solid #e6e6e6;
    color: #666;
}

.bootbox .modal-header button.btn-default-fo:hover {
    /*background-color: #ED9C28;*/
    background-color: #e6e6e6;
    border: 1px solid #e6e6e6;
    color: #666;
}

.bootbox .modal-header button.btn-default-fo:active {
    color: #2e2e2e;
    border-color: #999;
}

.bootbox .modal-header button.btn-primary-fo , .bootbox .modal-header button.btn-success{
    /*background-color: #D9534F;*/
    background-color: #1ab394;
    color: #fff;
    border: 1px solid #1ab394;
    margin-right: 0;
}
.bootbox .modal-header button.btn-primary-fo:hover, .bootbox .modal-header button.btn-success:hover{
    /*background-color: #D2322D;*/
    background-color: #48c2a9;
    color: #fff;
    border: 1px solid #48c2a9;
}

.bootbox .modal-header .success {

}

.container #content {
    width: 100%!important;
    /*border-left: 0;*/
}
.modal-header button[data-bb-handler='cancel'] {
    color: #666666 !important;
    background-color: #f5f5f5 !important;
    border: 1px solid #e6e6e6 !important;
}

.modal-header button[data-bb-handler='cancel']:hover {
    color: #666 !important;
    border-color: #e6e6e6 !important;
    background: #e6e6e6 !important;
}

.modal-header button[data-bb-handler='cancel']:active {
    color: #2e2e2e !important;
    border-color: #999 !important;
}

/**
* magicsuggest
*/
.container .ms-ctn input,
.modal-open .ms-ctn input {
    line-height: 2.12857143;
    height: 30px;
}

.container .ms-ctn .ms-sel-ctn,
.modal-open .ms-ctn .ms-sel-ctn {
    height: 32px;
}

/**dropzone*/
.container .dropzone.dz-clickable {
    border: 1px dashed silver;
    background: rgba(181, 205, 207, 0.3);
}

.container  a.dz-download{
    font-family: "Segoe UI","Helvetica Neue",Helvetica,Arial,sans-serif;
    background-color: #E25856;
    border-color: #DE4240;
    color: #FFF;
    -moz-user-select: none;
    border: 1px solid rgba(0,0,0,0);
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857;
    margin-bottom: 0;
    padding: 6px 17px;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    text-decoration: none;
    margin-left: 2px;
}
.container .dropzone a.dz-remove,
.container .dropzone a.dz-download,
.container .dropzone-previews a.dz-remove,
.container .dropzone-previews a.dz-download{
    line-height: 0.4;
    border-radius: 0;
    margin-top: 1px;
    padding: 1px 15px;
    background: #fff;
    color: #e25856;
    font-size: 20px;
}
.container .dropzone a.dz-remove:hover,
.container .dropzone a.dz-download:hover{
    background: #e25856;
    color:#fff;
}

.container .dropzone .dz-preview .dz-details,
.container .dropzone-previews .dz-preview .dz-details {
    margin-bottom: 0;
}

.container .dropzone .dz-preview,
.container .dropzone-previews .dz-preview {
    border-radius: 5px;
}

.container .dropzone .dz-preview .dz-details .dz-size,
.container .dropzone-previews .dz-preview .dz-details .dz-size {
    position: inherit;
}

.container .dropzone .progress{
    margin-bottom: 2px;
    margin-top: 7px;
    display: none;
}
.container .dropzone .dz-size{
    margin-left: 10px;
}
.container .dropzone .dz-name{
    cursor:pointer;
}

.container .dropzone .dz-file-preview .dz-remove{
    background-color: transparent;
    color: #e25856;
    font-size: 20px;
    padding: 0;
    margin-left: 10px;
}

.container .dropzone .dz-file-preview .dz-remove i{
    cursor:pointer;
}
.container .dropzone .dz-file-preview .dz-file-notice{
    position: absolute;
    top: -8px;
    right: -20px;
}
.container .dropzone .dz-preview .dz-error-message{
    min-width: 150px;
}


@media only screen and (min-width: 1635px) and (max-width: 1680px) {
    .s-button-tools .s-button-org {
        margin-right: 15px;
    }
}

#content .button-right-row-div {
    float: right;
    padding-top: 2px;
    padding-left: 20px;
}

#content .multi-select .location {
    padding-right: 0px;
    width: 25%;
    margin-left: 0;
}

#content .readonly {
    cursor: default;
    background-color: #eeeeee;
}

#content .readonly:hover {
    cursor: default;
    background-color: #eeeeee;
}

.modal-dialog .box.border > .box-title, .box.solid > .box-title {
    padding: 4px 10px 0px;
}

.modal-dialog .form-control {
    -webkit-box-shadow: none;
    min-height: 34px;
    font-size: 14px;
}

.modal-dialog .ms-ctn input {
    line-height: 2.12857143;
}

form .form-group:first-child{
    /*margin-top: 15px;*/
}

.mgt5{
    margin-top: 5px;
}

.bgw{
    background: #FFF;
}
.greyborder{
    border: solid 1px #EEE;
}
/*.btn i{*/
/*margin-right: 5px;*/
/*}*/
/*搜索面板*/
.searchbar{
    background-color: #FFF;
    border: 1px solid #cccccc;
    border-top: 0;
    padding-top: 10px;
    /*margin-top: 5px;*/
}
/*.searchbar:before{*/
/*position: absolute;*/
/*content: " ";*/
/*top: 40px;*/
/*left: 20px;*/
/*width: 0px;*/
/*height: 0px;*/
/*border-right: 10px solid transparent;*/
/*border-bottom: 10px solid #fff;*/
/*border-left: 10px solid transparent;*/
/*z-index: 2;*/
/*}*/
/*.searchbar:after{*/
/*position: absolute;*/
/*content: " ";*/
/*top: 39px;*/
/*left: 19px;*/
/*width: 0px;*/
/*height: 0px;*/
/*border-right: 11px solid transparent;*/
/*border-bottom: 11px solid #cccccc;*/
/*border-left: 11px solid transparent;*/
/*}*/
.input-group-addon.searchbtn:hover{
    background-color: #1c83c6;
    color: #FFF;
}
.searchbar i{
    float: right;
    cursor: pointer;
}
.searchbar .cleardatebtn{
    position: absolute;
    margin-top: -27px;
    right: 20px;
    color: #CCC;
    font-size: 20px;
    display: none;
}
.searchbar .daterangeDiv{
    position: relative;
}
.daterangeDiv:hover .cleardatebtn{
    display: block;
}
.dateContainer{
    background: #fff;
    cursor: pointer;
    padding: 5px 10px;
    border: 1px solid #ccc;
    height: 30px;
    max-width: 250px;
}



.searchbar .condition{
    max-width: 250px;
}
.searchbarbtn{
    top:-2px;
    height: 34px;
    border-radius: 0;
    width: 100%;
}
.searchbar .cleardate{
    position: relative;
    top:2px;
    left: 65px;
}
.searchPanel .input-group i{
    cursor: pointer;
}
.searchPanel .input-group-addon{
    cursor: pointer;
}
/*收缩面板*/
.panel-slide .condition-sign{
    /*float: right;*/
    position: absolute;
    top: 14px;
    right: 14px;
}

.panel-slide .panel-heading{
    cursor: pointer;
    position: relative;
}
.panel-slide .panel-body{
    background-color: #FFF;
    padding: 10px;
}

/*按钮*/
.button{
    font-weight: 500;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    border: 1px solid transparent;
    padding: 5px 18px;
}
.button-bar .button{
    float: right;
    margin-left: 10px;
}
.button.button-save{
    background-color: #F0AD4E;
    color: #FFF;
}
.button.button-save:hover{
    background-color: #ED9C28;
}

.button.button-delete{
    background-color: #D9534F;
    color: #FFF;
}
.button.button-delete:hover{
    background-color:#D2322D ;
}

.button.button-search{
    background-color: #5E87B0;
    color: #FFF;
}
.button.button-search:hover{
    background-color: #4B739A;
}

.button.button-modify{
    background-color: #A8BC7B;
    color: #FFF;
}
.button.button-modify:hover{
    background-color: #96AE60;
}

.button.button-create{
    background-color: #A8BC7B;
    color: #FFF;
}
.button.button-create:hover{
    background-color: #96AE60;
}
/*按钮�?*/
.button-bar-2{
    height: 50px;
    padding: 10px;
}
.button-bar-2 button{
    float: right;
    /*margin-left: 10px;*/
}
/*tab*/
.tabs{
    background-color: #FFF;
    border: solid 1px #EEE;
    position: relative;
}
.tabs .tabs-nav{
    width: 100%;
    height: 50px;
    margin: 0;
    padding: 0;
    line-height: 50px;
    vertical-align: middle;
    box-sizing: border-box;
    margin: -1px;/*抵消父容器上边框*/
    border-bottom: 1px solid #EEE;
}
.tabs .tabs-nav > li{
    text-decoration: none;
    list-style: none;
    float: left;
    font-size: 16px;
    color: #3C8DBC;
    margin-right: 10px;
    height: 47px;
    line-height: 47px;
    vertical-align: middle;
    padding: 0 15px;
    cursor: pointer;
    box-sizing: content-box;
}

.tabs-content > div{
    padding: 10px;
    display: none;
}

.tabs-content > div.open{
    display: block;
}

.tabs .tabs-nav > li.open{
    border: solid 1px #EEE;
    border-bottom:solid 3px #3C8DBC;
    color: #555;
}

.tabs .tabs-nav > li.disable{
    cursor: default;
    color: #999;
}

.tabs-content .tab-button-bar{
    float: right;
    margin-top: -50px;
}

.tabs-content .tab-button-bar button{
    margin-left: 5px;
}

.woRelate a{
    cursor: pointer;
}

.ztree, .ztree ul{
    /*border: solid 1px #EEE;*/
}

.panel-body .col-sm-2,.panel-body .col-sm-3{
    padding-left: 0px;
}

.card-panel{
    overflow-y: auto;
    overflow-x: hidden;
}

.s-button-tools>button{
    float: right;
}


.timeContainer{
    min-width: 133px;
}

.s-button-tools .btn-left{
    float: left;
}

/*.box{*/
    /*border: solid 1px #EEE;*/
/*}*/




.container_complete{
    padding-top: 20px;
    border-top:solid 1px #EEE;
}

.modal-dialog .ztree{
    border: none;
}
/*.modal-body{*/
/*max-height: 610px;*/
/*overflow-y: scroll;*/
/*}*/

#personalPicture .dropzone{
    background: none;
    border: none;
}
#personalPicture .dropzone .dz-preview, .dropzone-previews .dz-preview{
    border: none;
    background: none;
    box-shadow: none;
}

#personalPicture .dz-remove{
    position: absolute;
    bottom: 5px;
    right: -35px;
    padding: 0;
}
#personalPicture .dz-success-mark{
    display: none;
}
#personalPicture .progress.progress-sm.progress-striped.active{
    display: none;
}
#personalPicture .dz-preview.dz-image-preview.dz-processing.dz-success{
    padding: 0;
    margin: 0;
}
#personalPicture .dropzone.dz-clickable.dz-started.dz-max-files-reached{
    width: 100px;
}
/*图片上传控件 图片名称隐藏*/
#cboxTitle{
    display: none;}

#main-content .dataTables_wrapper.no-footer .dataTables_scrollBody{
    border-bottom: none;
}

#content .input-readonly{/*不能输入但可以�?�择�?*/
    background-color: #FFF;
}

#content .workorder-status{
    display: block;
    float: right;
}
#content .workorder-describe-container{
    white-space: normal;
    text-overflow: ellipsis;

}
#arrive_date_container .input-group input{
    width: 35%;
    border-right: 0;
}
#arrive_date_container .input-group select{
    width: 65%;
}

.fc-content{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#content .custom_table tbody > tr > td:first-child{
    /*border-left: 1px solid #ddd;*/
}

#content .custom_table tbody > tr > td{
    width: 100%;
    /*border-bottom: solid 1px #eee;*/
}

#content .custom_table tbody>tr>td>input{
    border: 0;
    width: 100%;
    height: 100%;
    padding-left: 6px;
}

#content .dataTables_wrapper .dataTables_processing{
    top:31px;
    z-index: 100;
    margin-top: 0;
}

.bootbox .dataTables_wrapper .dataTables_processing i,
#content .dataTables_wrapper .dataTables_processing i{
    display:none;
}

.border-0{
    border: 0;
}

.padding-left-10{
    padding-left: 10px;
}

.padding-right-10{
    padding-right: 10px;
}

#content header.panel-heading button{
    float: right;
    margin-top: -4px;
}

/*气泡*/
.popover{
    word-break: break-all;
}

.notification-setting-list .panel-slide .panel-heading{
    cursor: default;
    font-weight: normal;
    background: #fff;
}

