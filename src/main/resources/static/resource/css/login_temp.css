* {
    margin: 0;
    padding: 0;
}

body {
    font-family: "Microsoft Yahei", verdana;
}

.bg1 {
    width: 100%;
    height: 100%;
    position: absolute;
    background: url(../img/loginbg.png) no-repeat 0 0;
    background-position: center center;
    background-size: cover;
    z-index: -1;
    min-width: 1024px;
    min-height: 768px;
}

.bg {
    width: 100%;
    height: 100%;
    position: relative;
}

.bg .head {
    position: relative;
    height: 70px;
    line-height: 70px;
    width: 100%;
    background: rgba(0, 0, 0, .2);
}

.bg .head div {
    display: inline-block;
}

.bg .head .logo {
    position: absolute;
    /*top: 35%;*/
    left: 100px;
    width: 148px;
    height: 70px;
    /*background: url(../img/logo2.png);*/
}

.bg .head .logo-content {
    position: absolute;
    right: 100px;
}

.bg .head .logo-content .head-font {
    font-size: 16px;
    color: #FFFFFF;
    margin-left: 25px;
    text-decoration: none;
}

.bg .head .logo-content .head-font:hover {
    cursor: pointer;
}

@media screen and (min-height: 701px) {
    .bg .body {
        margin-top: 100px;
        width: 100%;
        position: relative;
        height: 100%;
    }
}

@media screen and (max-height: 700px) {
    .bg .body {
        margin-top: 25px;
        width: 100%;
        position: relative;
        height: 100%;
    }
}

@media screen and (max-height: 670px) {
    .bg .body {
        margin-top: 15px;
        width: 100%;
        position: relative;
        height: 100%;
    }
}

.bg .body .body-con {
    text-align: center;
    margin: auto;
    width: 360px;
    height: auto;
    background-color: #FFFFFF;
}

.bg .body .body-con .con-margin {
    padding: 50px 30px 48px 30px;
}

.bg .body .body-con .con-margin .account_text {
    font-size: 14px;
    margin-top: 48px;
    text-align: left;
}

.bg .body .body-con .con-margin .account_input {
    margin-top: 10px;
}

.bg .body .body-con .con-margin .account_input .username {
    width: 300px;
    height: 34px;
    border-radius: 0px;
    box-shadow: none;
}

.bg .body .body-con .con-margin .account_input .username:focus {
    border: 1px solid #0a87cc;
}

.bg .body .body-con .con-margin .password_text {
    font-size: 14px;
    margin-top: 22px;
    text-align: left;
}

.bg .body .body-con .con-margin .password_input {
    margin-top: 10px;
}

.bg .body .body-con .con-margin .password_input .password {
    width: 300px;
    height: 34px;
    border-radius: 0px;
    box-shadow: none;
}

.bg .body .body-con .con-margin .password_input .password:focus {
    border: 1px solid #0a87cc;
}

.bg .body .body-con .con-margin .login_btn {
    margin-top: 40px;
    width: 300px;
    height: 34px;
    line-height: 34px;
    font-size: 14px;
    color: #FFFFFF;
    text-align: center;
    background-color: #0a87cc;
}

.bg .body .body-con .con-margin .login_language {
    margin-top: 25px;
    font-size: 14px;
    color: #999999;
}

.bg .body .body-con .con-margin .login_language div {
    display: inline-block;
}

/*语言两边横线*/
.bg .body .body-con .con-margin .login_language .l_line {
    border-top: 1px solid #D9D9D9;
    width: 121px;
    vertical-align: middle;
    display: inline-block;
    /*border-top: 1px solid #D9D9D9;*/
    /*width: 123px;*/
    /*vertical-align: middle;*/
}

/*语言*/
.bg .body .body-con .con-margin .login_language .l_text {
    /*margin: 0 10px;*/
    font-size: 14px;
    font-family: 微软雅黑;
    display: inline-block;
    margin: 0 10px;
}

.bg .body .body-con .con-margin .login_choose_lan {
    /*text-align: left;*/
    width: 116px;
    height: 36px;
    margin: auto;
    margin-top: 20px;
    text-align: left;
}

.bg .body .body-con .con-margin .login_choose_lan div {
    display: inline-block;
}

.bg .body .body-con .con-margin .login_choose_lan .login_choose_zh {
    /*text-align: center;*/
    /*margin-left: 70px;*/
    float: left;
    font-family: 微软雅黑;
    /*margin-top: 20px;*/
    text-align: center;
}

.bg .body .body-con .con-margin .login_choose_lan .login_choose_en {
    /*text-align: center;*/
    /*margin-left: 100px;*/
    float: left;
    text-align: center;
    font-family: Arial;
    margin-left: 44px;
    /*margin-top: 20px;*/

}

.bg .body .body-con .con-margin .login_choose_lan .login_choose_active {
    border: 1px solid #0a87cc;
    color: #0a87cc;
}

.bg .body .body-con .con-margin .login_choose_lan .login_choose_inactive {
    color: #E0E0E0;
    border: 1px solid #E0E0E0;
}

.bg .body .body-con .con-margin .login_choose_lan .login_choose_l {
    font-size: 16px;
    width: 36px;
    height: 36px;
    line-height: 36px;
    border-radius: 100%;
}

.bg .body .body-con .con-margin .login_choose_lan .login_choose_l:hover {
    cursor: pointer;
}

.bg .body .body-con .con-margin .login_btn:hover {
    background-color: #0072b1;
    cursor: pointer;
}

/*.bg .body .body-con .con-margin .login-title > span:first-child {
    margin-right: 20px;
}*/

.bg .body .body-con .con-margin .login-title span {
    font-size: 24px;
    font-weight: lighter;
    color: #4b4b4b;
}

.bg .body .body-con .con-margin .hr-title {
    border-top: 1px solid #EDEDED;
    margin-top: 16px;
}

@media screen and (max-height: 700px) {
    .bg .foot {
        background: rgba(0, 0, 0, .3);
        line-height: 50px;
        width: 100%;
        bottom: 0px;
        position: fixed;
    }
}

@media screen and (min-height: 701px) {
    .bg .foot {
        background: rgba(0, 0, 0, .3);
        line-height: 70px;
        width: 100%;
        bottom: 0px;
        position: fixed;
    }
}

.bg .foot div {
    display: inline-block;
}

.bg .foot .foot-size {
    font-size: 14px;
    color: #FFFFFF;
}

.bg .foot .foot-content {
    position: absolute;
    right: 100px;
}

.bg .foot .foot-size-sp {
    margin-left: 100px;
    font-family: arial;
}

