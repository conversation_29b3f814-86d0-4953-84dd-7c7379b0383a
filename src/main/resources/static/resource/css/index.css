.xia-border-right{border-right:solid 1px #cdd2d2;}
.xia-border-top{border-top:solid 1px #cdd2d2;}
.xia-text-right{text-align:right;margin-top: 3px;}
.xia-box-bg-write{background-color:#fff;}
.xia-cursor-pointer{cursor:pointer;}
.xia-font-limit{overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
span.required{color:red;  width: 10px;display: inline-block;  margin-left: 4px;  vertical-align: middle;  position: absolute;  font-size: 20px;}
div.required{color:red;position: absolute;text-align: right;right: 5px;top: 8px;}
.box.border > .box-title .tools > a{color:#fff;}
#content .box.border > .box-title, #content .box.solid > .box-title{padding: 4px 10px 6px;}
#content .box.border > .box-title h4,#content .box.solid > .box-title h4{margin-top: 7px;margin-bottom: 2px;}
.tabs-left .tab-content{float: left;width: 90%;}
.line-title{border-bottom: 1px dashed #ddd;width: 100%;display: block;font-size: 18px;margin: 20px 0 10px 0;color: #999;}
.nav-tabs>.tab-i-20{width: 20%;text-align: center;}
.nav-tabs>.tab-i-30{width: 30%;text-align: center;}
.tab-content .tab-pane{/*overflow: hidden;*/}
.bootbox-body .tab-content .tab-pane{overflow: inherit;}
/*位置组件*/
.multi-select{width:100%;display: -webkit-box;}
.multi-select  .form-control{margin-left: 0px;border-right: 0;border-left: 0;float: left;}
.multi-select  .form-control:first-child{margin-left: 0;border-left: 1px solid #cccccc;display:none;}
.multi-select  .form-control:nth-child(2){margin-left: 0;border-left: 1px solid #cccccc;}
.multi-select  .form-control:last-child{border-right: 1px solid #cccccc;}
/**box head tab*/
.x-item-disabled {color: gray;cursor: not-allowed;opacity: .6;-moz-opacity: .6;filter: alpha(opacity=60);}
/**notification*/
.gritter-light{border-radius: 8px;box-shadow: 0 4px 5px rgba(0,0,0,.3), 0 0 6px rgba(0,0,0,.04);}
.gritter-light:hover{border-color:transparent;}
/*左边栏*/
/*.sidebar-menu > ul > li   ul.sub-sub:before{*/
	/*-moz-border-bottom-colors: none;*/
	/*-moz-border-left-colors: none;*/
	/*-moz-border-right-colors: none;*/
	/*-moz-border-top-colors: none;*/
	/*border-color: #656973;!*三级菜单虚线框*!*/
	/*border-image: none;*/
	/*border-style: dotted;*/
	/*border-width: 0 0 0 1px;*/
	/*bottom: 0;*/
	/*content: "";*/
	/*display: block;*/
	/*position: absolute;*/
	/*top: 0;*/
	/*z-index: 1;*/
/*}*/
/*.sidebar-menu > ul > li ul.sub-sub > li:before{*/
	/*border-top: 1px dotted #656973;!*三级菜单虚线框*!*/
	/*content: "";*/
	/*display: inline-block;*/
	/*position: absolute;*/
	/*width: 17px;*/
	/*margin: 5% 0 0;*/
/*}*/
.sidebar-menu > ul > li   ul.sub-sub > li.active > a, .sidebar-menu > ul > li   ul.sub-sub > li > a:hover, .sidebar-menu > ul > li  ul.sub-sub > li.current{
	background: #E1FAFF !important;
}

.sidebar-menu > ul > li  ul.sub-sub > li.current {
    background: #E1FAFF !important;
    border-right: 2px solid #00ACB7;
    text-indent: -3px;
    color: #00ACB7!important;
}

.sidebar-menu > ul > li  ul.sub-sub > li.current span {
    color: #333333;
}

.sidebar-menu .mymenu .current span {
    /*color: #fff;*/
}


input.form-control.datepicker{cursor: pointer;}
input.form-control.pointer{cursor: pointer;}

/**输入框删除图标*/
.xia-input-clear{position: relative;}
.xia-input-clear i.fa{position: absolute;right: 22px;top: 8px;font-size: 20px;color: #999;cursor: pointer;}
.xia-input-clear i.fa:hover{color: #666;}

/**文字两端对齐*/
.justify{
	 text-align:justify;
     text-justify:distribute-all-lines;/*ie6-8*/
     text-align-last:justify;/* ie9*/
     -moz-text-align-last:justify;/*ff*/
     -webkit-text-align-last:justify;/*chrome 20+*/
}
@media screen and (-webkit-min-device-pixel-ratio:0){/* chrome*/
      .justify:after{
          content:".";
          display: inline-block;
          width:100%;
          overflow:hidden;
          height:0;
      }
}
  
.sub-menu-text:before{
	content:"";
}

/**
组织管理页面
**/
.s-button-org{
  float: right;
  margin-right: 5px;
}

.text-limit{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}


/**
 IE 
*/
/** 去除ie的input框的X*/
::-ms-clear, ::-ms-reveal{display: none;}
.ui-widget input{font-family: inherit}

.ui-datepicker-hourselect{
	right: -23px;
	left: auto;
	min-width: 44px;
	height: 200px;
	overflow: auto;
}

.ui-datepicker-minuteselect{
	right: -24px;
	left: auto;
	min-width: 44px;
	height: 200px;
	overflow: auto;
}

.ui-datepicker-hourselect a,.ui-datepicker-minuteselect a{font-size:13px; }
.ui-datepicker td .ui-datepicker-hourselect a,
.ui-datepicker td .ui-datepicker-minuteselect a {text-align: left}

.icon-confirm-delete{
	padding-right: 10px;
	vertical-align: text-bottom;
}