.strTr{
    background-color: #EEE;
}
.wrapper {
    width: 800px;
    margin: 0 auto;
}

td {
    padding-left: 5px
}

.in-table {
    display: table-cell;
    padding: 0;
}

/* .in-table table tr {   border-bottom: 1px solid #999; }*/

.in-table table tr:last-child {
    border: 0;
}

/* .in-table table tr td {  border-right: 1px solid #999; }*/

/* .in-table table tr td:last-child { border: 0; }*/

.b-table {
    border: 1px solid #333;
}

.td-width {
    width: 80px;
    text-align: justify;
}

.td-width span {
    display: inline-block;
    padding-left: 100%;
}

* {
    font-family: "微软雅黑", Arial;
}

.tit {
    text-align: right;
    padding-right: 10px;
    width: 96px;
}

.innerTable {
    border-collapse: collapse;
    border-spacing: 0;
    margin: 0;
    padding: 0;
}

.innerTable td {
    outline: none;
    border: solid 1px #999;
}

.innerTable tr:first-child td {
    border-top: none;
    text-align: center;
    font-size: 15px;
}

.innerTable tr:last-child td {
    border-bottom: none;
}

.innerTable td:first-child {
    border-left: none;
}

.innerTable td:last-child {
    border-right: none;
}

.strTr {
    height: 35px;
}

#print_content {
    /*width: 800px;*/
    /*margin: 0 auto;*/
    background-color: #fff;
    padding-bottom: 50px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

body {
    /*background-color: #797A7A;*/
    padding-bottom: 50px;
}

#print_button {
    border: none;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    cursor: pointer;
    padding: 5px 18px;
    text-transform: uppercase;
    outline: none;
    background-color: #00a65a;
    border-color: #008d4c;
    color: #fff;
    border-radius: 5px;
    font-weight: 500;
    font-size: 14px;
    position: relative;
    right: -698px;
    top: 40px;
}

#print_button:hover {
    background-color: #008D4C;
}
.tit{
    text-align: center;
    padding:9px;
    font-size: 15px;
    font-weight: bold;
}