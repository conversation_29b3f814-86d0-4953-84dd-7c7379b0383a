.main,
.container > header {
	width: 95%;
	max-width: 69em;
	margin: 0 auto;
	padding: 0 1.875em 3.125em;
}

.container > header {
	padding: 2.875em 1.875em 1.875em;
}

.container > header h1 {
	font-size: 2.125em;
	line-height: 1.3;
	margin: 0;
	float: left;
	font-weight: 400;
}

.container > header span {
	display: block;
	font-weight: 700;
	text-transform: uppercase;
	letter-spacing: 0.5em;
	padding: 0 0 0.6em 0.1em;
}

.container > header nav {
	float: right;
}

.container > header nav a {
	display: block;
	float: left;
	position: relative;
	width: 2.5em;
	height: 2.5em;
	background: #fff;
	border-radius: 50%;
	color: transparent;
	margin: 0 0.1em;
	border: 4px solid #47a3da;
	text-indent: -8000px;
}

.container > header nav a:after {
	content: attr(data-info);
	color: #47a3da;
	position: absolute;
	width: 600%;
	top: 120%;
	text-align: right;
	right: 0;
	opacity: 0;
	pointer-events: none;
}

.container > header nav a:hover:after {
	opacity: 1;
}

.container > header nav a:hover {
	background: #47a3da;
}

.icon-drop:before, 
.icon-arrow-left:before {
	font-family: 'fontawesome';
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	speak: none;
	font-style: normal;
	font-weight: normal;
	line-height: 2;
	text-align: center;
	color: #47a3da;
	-webkit-font-smoothing: antialiased;
	text-indent: 8000px;
	padding-left: 8px;
}
/* 覆盖日期选择左箭头不出现的问题 -- 原因是这条注释上面这段代码导致的，也不知道用来干啥的 -.- */
.daterangepicker .prev .icon-arrow-left:before {
	position: relative;
	color: #000;
	text-indent: 0;
	line-height: 14px;
	padding-left: 4px;
}

.container > header nav a:hover:before {
	color: #fff;
}

.icon-drop:before {
	content: "\e000";
}

.icon-arrow-left:before {
	content: "\f060";
}

.cbp_tmtimeline {
	margin: 30px 0 0 0;
	padding: 0;
	list-style: none;
	position: relative;
} 

/* The line */
.cbp_tmtimeline:before {
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	width: 3px;
	background: #afdcf8;
	left: 106px;
	margin-left: -10px;
}

/* The date/time */
.cbp_tmtimeline > li .cbp_tmtime {
	display: block;
	width: 100px;
	padding-right: 100px;
	position: absolute;
}

.cbp_tmtimeline > li .cbp_tmtime span {
	display: block;
	text-align: right;
}

.cbp_tmtimeline > li .cbp_tmtime span:first-child {
	font-size: 0.9em;
	color: #b2b2b2;
}

.cbp_tmtimeline > li .cbp_tmtime span:last-child {
	font-size: 1.5em;
	color: #666;
}

.cbp_tmtimeline > li:nth-child(odd) .cbp_tmtime span:last-child {
	color: #666;
}

/* Right content */
.cbp_tmtimeline > li .cbp_tmlabel {
	margin: 0 0 15px 120px;
	background: #fff;
	color: #666;
	padding: 1em;
	font-size: 1em;
	font-weight: 300;
	line-height: 1.4;
	position: relative;
	border-radius: 5px;
	border: 1px solid #cccccc;
}
.cbp_tmtimeline > li .cbp_tmlabel .description{
	word-break: break-all;
}
.cbp_tmtimeline > li .cbp_tmlabel .createdBy{
	float: right;
	font-size: 12px;
	font-weight: normal;
	min-width: 3em;
	text-align: left;
}

.cbp_tmtimeline > li:nth-child(odd) .cbp_tmlabel {
	background: #fff;
	border: 1px solid #cccccc;
}

.cbp_tmtimeline > li .cbp_tmlabel h2 { 
	margin-top: 0px;
	padding: 0 0 10px 0;
	border-bottom: 1px solid #cccccc;
	font-size: 16px;
	font-weight: bold;
}

/* The triangle */
.cbp_tmtimeline > li .cbp_tmlabel:after {
	right: 100%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-right-color: #cccccc;
	border-width: 10px;
	top: 10px;
}
.cbp_tmtimeline > li .cbp_tmlabel:before {
	right: 100%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-right-color: white;
	border-width: 9px;
	top: 11px;
	z-index: 1;
}

.cbp_tmtimeline > li:nth-child(odd) .cbp_tmlabel:after {
	border-right-color: #cccccc;
}

/* The icons */
.cbp_tmtimeline > li .cbp_tmicon {
	width: 4px;
	height: 4px;
	font-family: 'ecoico';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	font-size: 1.4em;
	line-height: 40px;
	-webkit-font-smoothing: antialiased;
	position: absolute;
	color: #fff;
	background: #46a4da;
	border-radius: 50%;
	box-shadow: 0 0 0 8px #afdcf8;
	text-align: center;
	left: 120px;
	top: 0;
	margin: 0 0 0 -25px;
}

.cbp_tmicon-phone:before {
	content: "\e000";
}

.cbp_tmicon-screen:before {
	content: "\e001";
}

.cbp_tmicon-mail:before {
	content: "\e002";
}

.cbp_tmicon-earth:before {
	content: "\e003";
}
