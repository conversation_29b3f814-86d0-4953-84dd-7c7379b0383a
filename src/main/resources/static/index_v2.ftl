<!DOCTYPE html>
<#assign  _STATIC_COMMON_="${baseStaticPath}/resource">
<#assign  _STATIC_BUSINESS_="${baseStaticPath}/business">
<#assign  _STATIC_VERSION_="?_v="+.now?string("yyyyMMdd")>
<#assign  _NEW_STYLE_="fmone-v2">
<html>
<#include "template/head_v2.ftl" encoding="utf8">
<body>
<script src="${_STATIC_COMMON_}/js/js-plugin/ezuiplayer/ezuiplayer.js"></script>
<script src="${_STATIC_COMMON_}/js/js-plugin/ezuiplayer/hls.js"></script>
<#include "template/header_v2.ftl" encoding="utf8">
<div class="main-content">
<#include "template/left_v2.ftl" encoding="utf8">
    <div id="VueElement_MainContent" class="container">
        <div class="row" style="background-color: #f5f5f5;">
            <!-- notice-->
            <div id="index_notice" style="position: fixed;  z-index: 9999;"></div>
            <!--header-->
            <div id="page-header" style="display: none;" data-home="${i18n("page.index.content.home")}">
                <div class="page-header" style="min-height: 0;"><h3
                        style="margin: 0px;">${i18n("page.index.content.home")}</h3></div>
            </div>
            <!--content-->
            <div id="content" class="page-content">
                <el-scrollbar :wrap-style='wrapStyle' :style="{height:contentHeight+'px'}" ref="contentScroller">
                <#--<div class="page-content-loading">加载中...</div>-->
                    <div class="page-content-container"></div>
                </el-scrollbar>
            </div>
        </div>
        <div class="footer-tools" style="display:none;">
        <span class="go-top">
            <i class="fa fa-chevron-up"></i> Top
        </span>
        </div>
    </div>
</div>


<script>document.documentElement.lang = "${i18nLocale}";</script>
<#-- xss -->
<script src="${_STATIC_COMMON_}/js/js-plugin/xss/xss.min.js"></script>
<#include "template/footer_v2.ftl" encoding="utf8">

<script>
    //消息服务配置参数
    var _messagesite_hostname = "${messagesite.hostname}";
    var _messagesite_port = "${messagesite.port?c}";
    var _messagesite_channel = "${messagesite.chanel}";
</script>
<script src="${_STATIC_COMMON_}/js/js-plugin/ezuiplayer/ezuiplayer.js"></script>

<script>
    var menu = document.querySelector("#VueElement_LeftMenu");
    menu.style.height = document.documentElement.clientHeight - 60 +'px';

    var pageContent = document.querySelector(".page-content");
    pageContent.style.height = document.documentElement.clientHeight - 60 +'px';

    window.addEventListener('resize', function(){
        var menu = document.querySelector("#VueElement_LeftMenu");
        menu.style.height = document.documentElement.clientHeight - 60 +'px';

        var pageContent = document.querySelector(".page-content");
        pageContent.style.height = document.documentElement.clientHeight - 60 +'px';
    });

    require(['vue','init'],function(Vue, init){
        //$(document).ready(function () {
        init.header.$on('narrow-changed',function(isNarrow){
            if(isNarrow){
                $('.main-content').addClass('main-content-narrow');
            }
            else{
                $('.main-content').removeClass('main-content-narrow');
            }
        });

        new Vue({
            el:"#VueElement_MainContent",
            data:{
                contentHeight:document.documentElement.clientHeight - 60,
                isNarrow:true
            },
            computed:{
                wrapStyle:function () {
                    return [{
                        height:this.contentHeight + 'px',
                        'overflow-x':'hidden'
                    }]
                },
            },
            mounted:function(){
                init.menu.$emit('home-load');
                var me = this;
                window.addEventListener('resize', function(){
                    me.contentHeight=document.documentElement.clientHeight - 60
                });
            }
        });

        //headpic
        if ("${headPic!}" != "") {
            var path = PUBLIC_PATH+"/common/files/id/${headPic!}/img?access_token=" + getAccessToken();
            $("#header-user .dropdown-toggle").find("img").attr("src", path);
        }

        var openProjectHome = function (pid) {
            var accesstoken = localStorage.access_token;
            if (!accesstoken) {
                accesstoken = FO.get_uri_param("access_token");
            }
            localStorage.current_project = pid;

            //百度追踪
            _hmt && _hmt.push(['_trackPageview', "/main/home/" + pid + "?current_project=" + pid]);

            location.href = location.origin + "/main/home/" + pid + "?current_project=" + pid + "&access_token=" + accesstoken;
        }

        //tohome
        var _pone = "${isem?c}";
        if (_pone == "true") {
        <#if pcurrent??>
            openProjectHome("${pcurrent.id?c}");
        </#if>
        }

        //click to home
        $(".projects .project").click(function () {
            var _pid = $(this).data("pid");

            openProjectHome(_pid);
        });
        $(".projects .project img").each(function () {
            var _pid = $(this).data("pic");
            if (_pid) {
                var _src = "/common/files/id/" + _pid + "/img?access_token=" + getAccessToken();
                $(this).attr("src", _src);
            }
        });
        //});
        window.showMeaageSite = function () {
            openMenuPage("/uc002", {});
        }
        window.showPerson = function (e) {
            openMenuPage("/uc001", {});
        }
        window.exit = function () {
            var href = location.href.split("#");
            href = href[0].split("?")[0];

            //百度追踪
            _hmt && _hmt.push(['_trackPageview', href]);

            window.location.href = href;
        }

    })

</script>

<audio id="noticeMp3" src="${_STATIC_COMMON_}/img/notice.mp3" hidden="true"></audio>
</body>
</html>
