<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="utf-8">
    <title>${title}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="shortcut icon" href="${baseStaticPath}/favicon.ico"/>
    <link rel="bookmark" href="${baseStaticPath}/favicon.ico"/>
    <!-- STYLESHEETS --><!--[if lt IE 9]>
    <script src="http://html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <script src="http://css3-mediaqueries-js.googlecode.com/svn/trunk/css3-mediaqueries.js"></script><![endif]-->
<#--<link rel="stylesheet" type="text/css" href="/resource/css/cloud-admin.css">-->
    <link rel="stylesheet" type="text/css" href="${baseStaticPath}/resource/js/js-plugin/bootstrap/css/bootstrap.min.css">
    <#--<link rel="stylesheet" type="text/css" href="${baseStaticPath}/resource/css/login_temp.css">-->
    <link rel="stylesheet" type="text/css" href="${baseStaticPath}/resource/css/login_new.css">
    <link rel="stylesheet" type="text/css" href="${baseStaticPath}/resource/css/plugin/font-awesome/css/font-awesome.min.css">
    <script>var PUBLIC_PATH = '${baseStaticPath}';</script>
</head>
<body>
<style>
.el-message-box__headerdd {
    position: relative;
    padding: 15px 15px 22px;
}
.el-message-box__titledd {
    padding-left: 0;
    margin-bottom: 0;
    font-size: 18px;
    line-height: 1;
    color: #303133;
}
#buttonclock {
    position: absolute;
    right: 15px;
    padding: 0;
    border: none;
    outline: none;
    background: transparent;
    font-size: 16px;
    cursor: pointer;
}
</style>
<div class="login">
    <div class="login__background"></div>

    <!--右侧边栏-->
    <div class="login__form">

        <!--切换语言-->
        <div class="login__form-lan">
                <span class="login__form-lan-span" id="loginLanguage">
                    <span id="login_language">简体中文</span><img style="margin: 0 0 2px 6px;" src="${baseStaticPath}/resource/img/common/icon_arrow_more.png">
                </span>
                <ul class="nav-dropdown" id="nav-dropdown">
                    <li id="login_choose_zh" class="nav-dropdown__item">简体中文</li>
                    <li id="login_choose_en" class="nav-dropdown__item">English</li>
                    <div class="popper__arrow"></div>
                </ul>
        </div>

        <!--form表格-->
        <div class="login__form-info">
            <div class="login__form-logo">
                <img src="${baseStaticPath}/resource/img/facilityone.png">
            </div>
            <div id="login-form">
                <!--用户名-->
                <div class="login__form-info-group">
                        <span class="login__form-info-icon">
                            <img src="${baseStaticPath}/resource/img/common/icon_account.png">
                        </span>
                    <input type="text" name="username" id="username" placeholder="账号"
                           class="login__form-info-input">
                </div>
                <!--密码-->
                <div class="login__form-info-group">
                        <span class="login__form-info-icon">
                            <img src="${baseStaticPath}/resource/img/common/icon_password.png">
                        </span>
                    <input type="password" id="password" name="password" placeholder="密码"
                           class="login__form-info-input">
                    <input type="hidden" name="client_id" value="${client_id}">
                    <input type="hidden" name="response_type" value="${response_type}">
                    <input type="hidden" name="redirect_uri" value="${redirect_uri}">
                </div>
                <input type="hidden" name="ucperson_language" id="ucperson_language" value="zh_CN">

                <div class="login__form-info-group" style="display: flex; padding-left: 4px; align-items: center;">
                    <span class="login__form-info-icon">
                        <img src="${baseStaticPath}/resource/img/code.png">
                    </span>
                    <div>
                        <input type="text" name="verifiedCode" autocomplete="off" id="verifiedCode" maxlength="6"  class="login__form-info-input" style="width: 100%" placeholder="验证码" />
                    </div>
                    <div class="v-image-box" style="display: inline-block;">
                        <img id="verifiedCode-image" style="width: 100%;" />
                    </div>
                </div>

                <div class="login__form-group">
                    <button class="login__form-info-submit" id="login_btn">
                        <span id="login_btn_span">登录</span>
                        <span class="login__form-group-arrow">
                            <img src="${baseStaticPath}/resource/img/common/icon_arrow_right.png">
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
        <div id='boxshow' style='position: absolute;width: 100%; height: 100%;z-index: 9999;background: rgba(0, 0, 0, 0.6);display:none'>
            <div  id="reset_password_htmlnew" style='position: absolute; top: 35vh;left: 24vw; display: inline-block; padding: 20px;width: 50vw; min-width: 500px;background: #fff;border-radius: 5px;max-width: 600px;'>
                <div class="el-message-box__headerdd" style='padding-top: 0;'>
                    <div class="el-message-box__titledd" style='display: inline-block;'><span>密码强度不够，请修改密码</span></div>
                    <button type="button"  id="buttonclock">
                    <svg t="1720145637263" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1521" width="14" height="14"><path d="M617.92 516.096l272 272-101.824 101.824-272-272-272 272-101.856-101.824 272-272-275.008-275.04L241.056 139.2l275.04 275.04 275.04-275.04 101.824 101.824-275.04 275.04z" fill="#000000" p-id="1522"></path></svg>
                    </button>
                </div>
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">账号</label>

                            <div class="col-sm-6">
                                    <span id='spanword'></span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">原密码</label>

                            <div class="col-sm-6">
                                <input type="text" class="form-control input-sm" 
                                    placeholder="原密码" id="old_password"   value=''>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">${i18n('page.sys001.newpassword')}</label>

                            <div class="col-sm-6">
                                <input type="text" class="form-control input-sm" 
                                    placeholder="${i18n('page.sys001.newpassword')}" id="new_password"  value=''>
                            </div>
                            <div class="col-sm-3"><input type="button" class="btn btn-primary" id="auto_generate_passwordnew"
                                                        value="${i18n('page.sys001.autogenerated')}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">二次确认</label>

                            <div class="col-sm-6">
                                <input type="text" class="form-control input-sm"   value=''
                                    placeholder="${i18n('page.sys001.newpassword')}" id="new_passwordpe">
                            </div>
                        </div>
    
                    </form>

             <div style='display: inline-block;float: right;'>
                    <button type="button" class="btn btn-primary" id='savenewpassword'>
                    <span> 
                        保存
                    </span>
                    </button>
                        <button type="button" class="btn btn-default" style='margin-left:20px'  id='rumberpassword'>
                        <span>
                        取消
                        </span></button>
                    </div>


            </div>

        </div>

</div>
<#--背景-->
<#--<div class="bg1"></div>-->
<#--内容-->
<#--<div class="bg">-->
    <#--<div class="head">-->
        <#--<div id="logo" class="logo">-->
        <#--</div>-->
        <#--<div class="logo-content">-->
            <#--<a class="head-font" href="https://help.fmone.cn/" target="_blank">产品使用</a>-->
            <#--<a class="head-font" href="http://www.facilityone.cn/" target="_blank">公司官网</a>-->
        <#--</div>-->
    <#--</div>-->
    <#--<div class="body">-->
        <#--<div class="body-con">-->
            <#--<div class="con-margin">-->
                <#--<div class="login-title"><span>登录</span><span>F-ONE系统</span></div>-->
                <#--<div class="hr-title"></div>-->
                <#--<div class="account_text">账号：</div>-->
                <#--<div class="account_input">-->
                    <#--<input type="text" class="username form-control" name="username" id="username"/>-->
                <#--</div>-->
                <#--<div class="password_text">密码：</div>-->
                <#--<div class="password_input">-->
                    <#--<input type="password" id="password" name="password" class="password form-control"/>-->
                    <#--<input type="hidden" name="client_id" value="${client_id}">-->
                    <#--<input type="hidden" name="response_type" value="${response_type}">-->
                    <#--<input type="hidden" name="redirect_uri" value="${redirect_uri}"></div>-->
                <#--<input type="hidden" name="ucperson_language" id="ucperson_language" value="zh_CN">-->

                <#--<div class="login_btn" id="login_btn">登录</div>-->
                <#--<div class="login_language">-->
                    <#--<div class="l_line"></div>-->
                    <#--<div class="l_text">语言</div>-->
                    <#--<div class="l_line"></div>-->
                <#--</div>-->
                <#--<div class="login_choose_lan">-->
                    <#--<div id="login_choose_zh" class="login_choose_zh login_choose_active login_choose_l">汉</div>-->
                    <#--<div id="login_choose_en" class="login_choose_en login_choose_inactive login_choose_l">EN</div>-->
                <#--</div>-->
            <#--</div>-->
        <#--</div>-->
    <#--</div>-->
    <#--<div class="foot">-->
        <#--<div class="foot-size foot-size-sp">Powered by FacilityONE</div>-->
        <#--<div class="foot-content foot-size">费哲软件提供技术支持</div>-->
    <#--</div>-->
<#--</div>-->
<!--/PAGE -->
<!-- JAVASCRIPTS -->
<!-- Placed at the end of the document so the pages load faster -->
<!-- JQUERY -->
<script src="${baseStaticPath}/resource/js/js-plugin/jquery/jquery-3.6.0.min.js"></script>
<script src="${baseStaticPath}/resource/js/js-plugin/jquery-ui-1.12.1.custom/js/jquery-ui-1.12.1.custom.min.js"></script>

<!-- CanvasBG Plugin(creates mousehover effect) -->
<script src="${baseStaticPath}/resource/js/login/canvasbg.js"></script>
<script src="${baseStaticPath}/resource/js/js-plugin/jQuery-Cookie/jquery.cookie.min.js"></script>

<script type="text/javascript" charset="utf-8">
    jQuery(document).ready(function () {

        $.ajax({
            url:PUBLIC_PATH+'/logo/CustomSettings',
            type: 'get',
            success: function (data) {
                var  data=data.data[0];
                if(data && data.loginPageLogo){
                       $("#logo").css("background","url("+PUBLIC_PATH+"/logo/" +data.loginPageLogo+"/img) 30%");
                  }else {
                       $("#logo").css("background","url("+PUBLIC_PATH+"/resource/img/logo2.png) 30%");
                   }

                }
            });

        refreshVCode();

        /*"use strict";

        // Init CanvasBG and pass target starting location
        CanvasBG.init({
            Loc: {
                x: window.innerWidth / 2,
                y: window.innerHeight / 3.3
            },
        });*/
        $('#login_btn').on('click', function () {
            login();
        });
        $('#buttonclock').on('click', function () {
                $("#boxshow").hide();
        });
        $('#auto_generate_passwordnew').on('click', function () {
               autoGeneratePassword()
        });
        $('#verifiedCode-image').click(function () {
            refreshVCode();
        })

        $('#loginLanguage').on('click', function (e) {
            $('#nav-dropdown').slideToggle('fast');
            e.stopPropagation();
        });
        $('html').click(function() {
            $('.nav-dropdown').hide();
        });
         $('#rumberpassword').click(function() {
            $('#new_passwordpe').val('');
            $('#new_password').val('');
            $('#old_password').val('');
            $('#spanword').html('');
             $("#boxshow").hide();

        });

        $('#savenewpassword').click(function() {
            changepassword()
        });
        $('#login_choose_zh').on('click', function () {
            // $('.login-title span:nth-child(1)').html('登录');
            // $('.login-title span:nth-child(2)').html('F-ONE系统');
            // $('.account_text').html('账号：');
            // $('.password_text').html('密码：');
            $('#login_btn_span').html('登录');
            // $('.l_text').html('语言');
            // $('.l_line').css("width", "121px");
            $('#ucperson_language').val('zh_CN');
            $('#login_language').html('简体中文');
            $('#login_choose_zh').html('简体中文');
            $('#login_choose_en').html('English');
            $("#username").attr("placeholder","账号");
            $("#password").attr("placeholder","密码");
            $("#verifiedCode").attr("placeholder","验证码");
            // $('#login_choose_zh').removeClass('login_choose_inactive').addClass('login_choose_active');
            // $('#login_choose_en').removeClass('login_choose_active').addClass('login_choose_inactive');
        });
        $('#login_choose_en').on('click', function () {
            // $('.login-title span:nth-child(1)').html('');
            // $('.login-title span:nth-child(2)').html('F-ONE System');
            // $('.account_text').html('Account:');
            // $('.password_text').html('Password:');
            $('#login_btn_span').html('Login');
            // $('.l_text').html('Language');
            // $('.l_line').css("width", "95px");
            $('#ucperson_language').val('en_US');
            $('#login_language').html('English');
            $('#login_choose_zh').html('简体中文');
            $('#login_choose_en').html('English');
            $("#username").attr("placeholder","Account");
            $("#password").attr("placeholder","Password");
            $("#verifiedCode").attr("placeholder","verified code");
            // $('#login_choose_en').removeClass('login_choose_inactive').addClass('login_choose_active');
            // $('#login_choose_zh').removeClass('login_choose_active').addClass('login_choose_inactive');
        });

    });

    $(function () {
        document.onkeydown = function (e) {
            var ev = document.all ? window.event : e;
            if (ev.keyCode == 13) {
                login();
            }
        }
    });
    function showRestPasswordDialog (data) {
        $("#boxshow").show();
        $('#new_passwordpe').val('');
        $('#new_password').val('');
        $('#old_password').val('');
        $('#spanword').html('');
        $("#spanword").html(data.username);
        
    }
    function changepassword () {
            let newpasswdpost = {
                    password:$('#new_password').val()
                }
            let old_password=$('#old_password').val()
            let new_password=$('#new_password').val()
            let new_passwordpe=$('#new_passwordpe').val()
            console.log(old_password)
            console.log(new_password)
            console.log(new_passwordpe)
            if(!old_password) return  alert('请输入原密码')
            if(!new_password) return  alert('请输入新密码')
            if(new_password!=new_passwordpe) return  alert('请确认两次输入的密码是否一致')

            $.ajax({
                url: PUBLIC_PATH+"/common/license/password/verify",
                data: JSON.stringify(newpasswdpost),
                type: "post",
                contentType: "application/json",
                processData: false,
                success: function (res) {
                        if(typeof res.data =='object'){
                            let changepass = {
                                    "newpass": $('#new_password').val(),
                                    "oldpass": $('#old_password').val(), 
                                    "userName": $('#spanword').html() 
                                }

                             $.ajax({
                                url: PUBLIC_PATH+"/common/license/password/change",
                                data: JSON.stringify(changepass),
                                type: "post",
                                contentType: "application/json",
                                processData: false,
                                success: function (res) {
                                    if (res.status == "success"&&res.message==null) {
                                            $('#new_passwordpe').val();
                                            $('#new_password').val();
                                            $('#old_password').val();
                                            $('#spanword').html();
                                            $("#boxshow").hide();
                                    }else{
                                         alert(res.message)
                                    }
                                }
                                });
                        }else{
                            alert(res.data)
                        }
                    }
                });
        
    }
    function generateCode (len) {
        if (isNaN(len)) {
            throw new Error('value is not valid')
        }
        var characters = '1234567890qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM_!@#$%^&*`~()-+=';
        var pwd = '';
        for (var i = 0; i < len; i++) {
            var count = Math.floor(Math.random() * characters.length)
            pwd += characters.slice(count, count + 1);
        }
        // var reg = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![a-z0-9]+$)[a-zA-Z0-9]{8,12}$/;
        var reg = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{10,64}$/
        if (reg.test(pwd)) {
            return pwd;
        } else {
            return generateCode(len)
        }
    }

    function login() {
        var username = $("input[name='username']").val();
        var password = $("input[name='password']").val();
        var vCode = $("input[name='verifiedCode']").val();

        var org_url = window.location.href.toString();
        localStorage.removeItem("from");
        if (org_url.indexOf("?from=") != -1 && org_url.indexOf("access_token") != -1) {
            localStorage.from = org_url.substring(org_url.indexOf("?from=") + 6);
        }

        var client_id = $("input[name='client_id']").val();
        var response_type = $("input[name='response_type']").val();
        var lang = $("#ucperson_language").val();
        if (null == lang) {
            lang = 'zh_CN';
        }
        var redirect_uri = $("input[name='redirect_uri']").val();

        var user = {"username": username, "password": password, "captcha": vCode, "source": "web"};
        $.ajax({
            url: PUBLIC_PATH+'/login/session',
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(user),
            success: function (response) {

                if (response.status == "success") {
                    window.location = PUBLIC_PATH+"/oauth2/auth?client_id=" + client_id + "&redirect_uri=" + redirect_uri + "&response_type=" + response_type + "&i18n=" + lang;
                }

            },
            "error": function (XMLHttpRequest, textStatus, errorThrown) {
                if(XMLHttpRequest.responseJSON.code==911){
                    console.log(123123)
                    console.log(123123)
                    showRestPasswordDialog(user);
                }else{
                var Result = $.parseJSON(XMLHttpRequest.responseText);
                if (Result.code >= 500 && Result.code <= 599 && (Result.status == "fail" ||  Result.status == "error")) {//fail
                    Result.message && alert(Result.message);
                }
                $("input[name='verifiedCode']").val("");
                refreshVCode();

                }

            }
        });
    }

    function refreshVCode() {
        $('#verifiedCode-image').attr('src', PUBLIC_PATH+'/login/captcha?reload='+(new Date()).getTime())
    }
   function autoGeneratePassword(){
        let ssr = generateCode(10)
        $("#new_password").val(ssr)
        $("#new_passwordpe").val(ssr)
    }
</script>
<script>document.documentElement.lang = "${i18nLocale}";</script>
<script type="text/javascript" charset="utf-8">
    jQuery(document).ready(function () {
        $("#ucperson_language").change(function () {
            var date = new Date();
            date.setTime(date.getTime() + (30 * 24 * 60 * 60 * 1000)); //7天后的这个时候过期
            $.cookie('lang', $(this).val(), {path: '/', expires: date});
            window.location.href = getRefreshUri($(this).val());
        });

        var query_object = getUriParams();
        if (!query_object["i18n"] && $.cookie('lang')) {
            window.location.href = getRefreshUri($.cookie('lang'));
        }
        //$("#ucperson_language").val(query_object["i18n"]);
    });
    var getRefreshUri = function (i18nv) {
        var query_object = getUriParams();
        query_object["i18n"] = i18nv;
        var query_array = [];
        for (var u in query_object) {
            query_array.push(u + "=" + query_object[u]);
        }
        return window.location.protocol + "//" + window.location.host + PUBLIC_PATH + "/login?" + query_array.join("&") + window.location.hash;
    }
    var getUriParams = function () {
        var query_object = {};
        var query = window.location.search.substring(1);
        if (query) {
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair.length == 2) {
                    query_object[pair[0]] = pair[1];
                } else if (pair.length == 3) {
                    query_object[pair[0]] = pair[1] + "=" + pair[2];
                }
            }
        }
        return query_object;
    }
    
</script>
<!-- /JAVASCRIPTS -->
</body>
</html>