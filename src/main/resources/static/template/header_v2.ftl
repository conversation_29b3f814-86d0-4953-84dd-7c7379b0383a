<#assign  _project_index_=0>
<style>
    .el-popover[x-placement^=bottom] {
        margin-top: -3px;
    }
    .header-menu{overflow: hidden}

    .header-icon-project{
        background-image: url(${_STATIC_COMMON_}/img/location.png);
        background-repeat: no-repeat;
        width: 12px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 4px;
    }

    .header-icon-notice{
        background-image: url(${_STATIC_COMMON_}/img/notice.png);
        background-repeat: no-repeat;
        width: 17px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 12px;
    }

    .header-icon-bar{
        background-image: url(${_STATIC_COMMON_}/img/bar.png);
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
        display: inline-block;
        margin-left: 20px;
        margin-top: 23px;
    }

    .header-icon-down{
        background-image: url(${_STATIC_COMMON_}/img/down.png);
        background-repeat: no-repeat;
        width: 10px;
        height: 6px;
        display: inline-block;
        margin-left: 5px;
    }

    .el-notification {
        padding-right: 40px;
    }

    .header-icon-amis{
        background-image: url(${_STATIC_COMMON_}/img/return_amis.png);
        background-repeat: no-repeat;
        width: 17px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 12px;
    }

    .header-icon-screen {
        background-image: url(${_STATIC_COMMON_}/img/screen.png);
        background-repeat: no-repeat;
        width: 17px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 12px;
    }
</style>
<header id="VueElement_Header" class="header">
    <el-popover style="display:none" width="476" ref="project-popover" placement="bottom-end" v-model="projectVisible" trigger="click" :visible-arrow="false" popper-class="header-popover">
        <shang-scroller :max-height="510">
            <div style="padding:12px;padding-left: 0;">
                <div class="header-project-main">
                <#if isem?c=="false">
                    <i class="fa fa-map-marker"></i>
                    <a class="header-project-main-link <#if !pcurrent??>header-project-main-selected</#if>" href="#" @click="enterProject($event)">${i18n('page.index.head.projectHome')}</a>
                </#if>
                    <div style="float: right;position: relative;" class="el-input">
                        <input type="text" autocomplete="off"
                               placeholder="${i18n('page.index.head.search')}"
                               style="border-radius: 4px;width: 250px;"
                               class="el-input__inner" v-model="searchProjectText" @keydown.13="searchProject($event)" >
                    </div>
                </div>
            <#if progros?exists>
            <#list progros?keys as key>
                <div>
                    <#if key =="">
                        <div class="header-project-group" data-group-name="-1">未分组</div>
                    <#else>
                        <div class="header-project-group" data-group-name="${key}">${key?html}</div>
                    </#if>
                    <div class="header-project-items clearfix">
                        <#list progros[key] as p>
                            <div class="header-project-item"
                                 data-project-name="${p.id}"
                                 title="${p.name?html}"
                                 v-bind:class="{'new-line': ${p_index} % 3 == 0,'end-line': (${p_index}+1)%3==0,'header-project-item-selected': ${p.id} === <#if pcurrent??>${pcurrent.id}<#else>''</#if>}"
                                 @click="enterProject($event,${p.id})"
                                    >
                                <a class="header-project-link" href="#" @click="enterProject($event,${p.id})" title="${p.name?html}">${p.name?html}</a>
                            </div>
                        </#list>
                    </div>
                </div>
            </#list>
        </#if>
            </div>
        </shang-scroller>
    </el-popover>
    <el-popover style="display:none" ref="notification-popover" placement="bottom-end" width="345" v-model="notificationVisible" trigger="click" :visible-arrow="false" popper-class="header-notification-popover">
        <div class="header-notification-item clearfix" v-for="item in notifications" @click="readMessage">
            <div class="header-notification-item-time">
                {{item.timeAgo}}
            </div>
            <div class="header-notification-item-content">
                <div class="header-notification-item-image"><img alt="" width="34" height="34" src="${_STATIC_COMMON_}/img/default-head.png"></div>
                <div class="header-notification-item-message">
                    {{item.content}}
                </div>
            </div>
        </div>
        <div class="header-notification-more" @click="readMessage">${i18n('page.index.head.allmsg')} <i class="fa fa-angle-right" aria-hidden="true"></i></div>
    </el-popover>
    <el-popover style="display:none" ref="avatar-popover" placement="bottom-end" width="375" v-model="avatarVisible" trigger="click" :visible-arrow="false" popper-class="header-avatar-popover">
        <div class="header-user clearfix">
            <img class="header-user-image" src="${_STATIC_COMMON_}/img/default-head.png"></img>
            <div class="header-user-info">
                <div class="header-user-name" style="color: #333;">${userName}</div>
                <div class="header-logout"><a  @click="exit()">${i18n('page.index.head.exit')}</a></div>
            </div>
        </div>
        <div class="header-user-links">
            <ul class="header-user-links-group">
                <li class="header-user-link">
                    <a @click="showPerson($event)" href="#">${i18n('page.index.head.profile')}</a>
                </li>
            </ul>
            <ul class="header-user-links-group">
                <li class="header-user-link">
                    <a target="_blank" href="https://help.fmone.cn/">${i18n('page.index.head.helpcenter')}</a>
                </li>
                <#--<li class="header-user-link">
                    <a target="_blank" href="http://www.fmone.cn:3000/updates/web.html">${i18n('page.index.head.updatelog')}</a>
                </li>-->
                <#--<li class="header-user-link">-->
                    <#--<a>新手指导</a>-->
                <#--</li>-->
            </ul>
            <ul class="header-user-links-group">
                <li class="header-user-link">
                    <a target="_blank" href="http://www.facilityone.cn/customCase.html">${i18n('page.index.head.customservice')}</a>
                </li>
                <li class="header-user-link">
                    <a target="_blank" href="http://www.facilityone.cn/contactUs.html">${i18n('page.index.head.feedback')}</a>
                </li>
            </ul>
        </div>
    </el-popover>
    <div class="header-logo"  :class="{ 'header-logo-narrow': isNarrow }" :style="styleObject" id="header-logo"></div>
    <div class="header-menu clearfix" :class="{ 'header-menu-narrow': isNarrow }">
        <div class="header-narrow" @click="menuNarrow">
            <i class="header-icon-bar" aria-hidden="true"></i>

        </div>
        <ul class="header-nav">
            <#if screen??>
                <#if screen>
<#--                    <li>-->
<#--                        <a class="dropdown-toggle" href="javascript:void(0)" id="toScreen" onclick="jumpScreen()">-->
<#--                            <i aria-hidden="true" class="header-icon-screen"></i>-->
<#--                        </a>-->
<#--                    </li>-->
                    <a class="header-nav-item" href="javascript:void(0)" id="toScreen" @click="jumpScreen()">
                        <div class="header-notification" style="margin-bottom:-1px">
                            <div style="color:#666">
                                <i class="header-icon-screen" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </#if>
            </#if>
            <#if jump??>
                <#if jump>
                <#--<input type="hidden" id="url" value="${url}"/>-->
                <#--<input type="hidden" id="key" value="${key}"/>-->
                    <#--<a class="header-nav-item" href="javascript:void(0)" id="toSpace" @click="jump()">
                        <div class="header-notification" style="margin-bottom:-1px">
                            <div style="color:#666">
                                <i class="header-icon-amis" aria-hidden="true"></i>空间
                            </div>
                        </div>
                    </a>-->
                </#if>
            </#if>

            <li class="header-nav-item" v-bind:class='{open: projectVisible}' v-popover:project-popover>
                <div class="header-project">
                    <i class="header-icon-project" aria-hidden="true"></i>
                <#if pcurrent??>
                ${pcurrent.name?html}
                <#else>
                ${i18n('page.index.head.projectHome')}
                </#if>
                </div>
            </li>
            <li class="header-nav-item" v-bind:class='{open: notificationVisible}' v-popover:notification-popover>
                <div class="header-notification" style="margin-bottom:-1px">
                    <div v-if="!notificationCount">
                        <i class="header-icon-notice" aria-hidden="true"></i>${i18n('page.index.head.notification')}
                    </div>
                    <el-badge :value="notificationCount" class="item" style="display:none" v-bind:style="{'display':notificationCount?'inline':'none'}" :max="99">
                        <i class="fa fa-bell" aria-hidden="true"></i>${i18n('page.index.head.notification')}
                    </el-badge>
                </div>
            </li>
            <li class="header-nav-item" v-bind:class='{open: avatarVisible}' v-popover:avatar-popover>
                <div class="header-avatar">
                    <div class="header-avatar-image">
                        <img src="${_STATIC_COMMON_}/img/default-head.png" alt="" width="30" height="30">
                    </div>
                    <div class="header-avatar-info">
                    ${userName} <i class="header-icon-down" aria-hidden="true"></i>
                        <input type="hidden" id="user_name" value="${userName}"/>
                        <input type="hidden" id="user_id" value="${userId?c}"/>
                    </div>
                </div>
            </li>
        </ul>
    </div>

</header>
<script type="text/javascript">
    window._AMapSecurityConfig = {
        securityJsCode:'f5a3e3f142b93959dc783c910d301ba6',
    }
</script>
<script type="text/javascript" src="//webapi.amap.com/maps?v=1.4.15&key=6823b9479fcb741eece4dc3c2b942ad9"></script>
<script type="text/javascript" src="//cache.amap.com/lbs/static/PlaceSearchRender.js"></script>
<script type="text/javascript" src="${_STATIC_COMMON_}/js/js-plugin/jquery/jquery-3.6.0.js"></script>
<script>
    $(document).ready(function(){

        var getAccessToken = function () {
            var accesstoken = localStorage.access_token;
            if (!accesstoken) {
                accesstoken = FO.get_uri_param("access_token");
            }
            if(isWhiteUser == "true"){
                accesstoken = FO.get_uri_param("access_token");
            }
            return accesstoken;
        }
        //headpic
        if ("${headPic!}" != "") {
            var path = PUBLIC_PATH+"/common/files/id/${headPic!}/img?access_token=" + getAccessToken();
            $(".header-avatar-image").find("img").attr("src", path);
            $(".header-user-image").attr("src", path);
        }
    });

</script>

