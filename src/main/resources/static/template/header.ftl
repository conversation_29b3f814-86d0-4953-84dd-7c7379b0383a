<style>
    .programsGroup .dropdown-menu{
        width: 476px;
        max-width: 800px;
        max-height: 500px;
        overflow-y: auto;
        overflow-x: hidden;
        border: none;
        background-color: #FFF;
        border: 1px solid #d3dce6;
        box-shadow: 0 2px 4px 0 rgba(0,0,0,.12), 0 0 6px 0 rgba(0,0,0,.04);
    }
    .programsGroup .dropdown-menu ul{
        width: 350px;
        max-width: 800px;
        max-height: 500px;

    }
    .programsGroup .dropdown-menu li a:hover{
        background-color:#fff;
        color: #1ab394;
        border: 1px solid #1ab394;
    }
    .programsGroup .dropdown-menu li a{
        border:1px solid #eee;
        background-color:#eee;
        border-radius: 5px;
    }
    .programsGroup .dropdown-menu li.selected a{
        background-color: #5E87B0;
        color:#FFF;
    }

    .programsGroup .dropdown-menu dd li a{
        border:1px solid #e5e5e5;
        background-color:#fff;
        margin-top: 20px;
        margin-bottom: 10px;
        margin-right: 27px;
        border-radius: 2px;
    }
    .programsGroup .dropdown-menu dd li.selected a{
        background-color: #1ab394;
        color:#FFF;
        border: 1px solid #1ab394;
    }

    .dropdown.user .fa-info{
        margin-left: 4px;
        margin-right: 12px;
    }
    .fa-newspaper-o::before{
        content: "\f133";
    }

    .programsGroup .dropdown-menu dt {
        border-bottom: 1px solid #e5e5e5;
        color: #333;
        font-size: 14px;
        font-weight: 700;
        padding: 8px 0;
    }

    .header-icon-project {
        background-image: url(${baseStaticPath}/resource/img/location.png);
        background-repeat: no-repeat;
        width: 12px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 4px;
    }
    .header-icon-notice {
        background-image: url(${baseStaticPath}/resource/img/notice.png);
        background-repeat: no-repeat;
        width: 17px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 8px;
    }
    .header-icon-down {
        background-image: url(${baseStaticPath}/resource/img/down.png);
        background-repeat: no-repeat;
        width: 10px;
        height: 6px;
        display: inline-block;
        margin-left: 5px;
    }
    .header-user {
        border-bottom: 1px solid #e5e5e5;
        padding: 20px;
        font-size: 14px;
    }
    .clearfix:before, .clearfix:after {
        content: " ";
        display: table;
    }
    .header-user-info {
        margin-left: 130px;
        position: relative;
        height: 96px;
    }
    .header-user-name {
        font-size: 16px;
        padding-top: 20px;
    }
    .header-logout {
        color: #ff5b5b;
        position: absolute;
        bottom: 0;
        right: 0;
    }
    .header-logout a {
        color: #ff5b5b !important;
    }
    .header-user-image {
        width: 96px !important;
        height: 96px !important;
        border-radius: 50% !important;
        border: 1px solid #e5e5e5;
        float: left;
    }
    .header-user-links {
        padding: 0 20px 20px 20px;
    }
    .header-user-links-group {
        border-bottom: 1px solid #e5e5e5;
        padding: 30px 0 8px !important;
    }

    .header-user-link {
        padding: 8px 0;
        list-style: none;
        position: relative;
        display: flex;
    }
    .header-user-link:before {
        content: " ";
        border: 1px solid #999;
        width: 11px;
        height: 11px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 14px;
        color: #999;
        position: absolute;
        top: 13px;
    }
    .header-user-link a {
        font-size: 14px;
        color: #666;
    }

    .header-user-link .header-a {
        padding-left: 24px;
    }
    .header-icon-bar {
        background-image: url(${baseStaticPath}/resource/img/bar.png);
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
        display: inline-block;
        margin-left: 2px;
        margin-top: -1px;
    }
    .pull-left #contentTip:hover {
         white-space:normal;
         height:148px;
         background-color:#F2F9F9;
         transition-property:background-color,height;
         transition-duration:0.2s ;
         transition-timing-function:linear;
    }
    .header-user-link a:hover {
        color: #1ab394 !important;
    }
    #home_wo_workTeam {
        position: absolute;
        border-radius: 4px;
        left: 0;
        top: 50%;
        margin-top: -17px;
    }
    #home_wo_workTeam .ms-sel-ctn input {
        height: 30px;
    }
    #home_wo_workTeam .ms-trigger-search {
        background-color: #1AB394;
        width: 56px !important;
        background-image: url(${baseStaticPath}/resource/img/icon-search.png);
        background-size: 18px 18px;
        background-position: center center;
        background-repeat: no-repeat;
    }
    #home_wo_workTeam .ms-trigger-search .ms-trigger-ico {display: none;}
</style>
<style>
    #workTeamSelector .inner-wrapper::-webkit-scrollbar {
        width: 5px;
        height: 5px;
    }


    /*Track*/
    #workTeamSelector .inner-wrapper::-webkit-scrollbar-track {
        background-color: rgba(255,255,255,0);
        border-radius: 5px;
    }

    /*Handle*/
    #workTeamSelector .inner-wrapper::-webkit-scrollbar-thumb {
        background-color: rgba(0,0,0,0.2);
        border-radius: 5px;
    }

    #workTeamSelector {
        margin-top: 12px;
        position: relative;
        display: inline-block;
        height: 34px;
        width: 300px;
        border: 1px solid #d9d9d9;
        border-right: 0;
        border-radius: 4px 0 0 4px
    }

    #workTeamSelector.trigger {
        border-color: #1ab394!important
    }

    #workTeamSelector .search-ico {
        cursor: pointer;
        background: #1ab394;
        background-image: url(${baseStaticPath}/resource/img/icon-search.png);
        background-size: 18px 18px;
        background-position: center center;
        background-repeat: no-repeat;
        text-align: center;
        display: inline-block;
        height: 34px;
        width: 56px;
        position: absolute;
        right: -56px;
        top: -1px;
        border: 1px solid #1ab394;
        border-radius: 0 4px 4px 0
    }

    #workTeamSelector .inner-wrapper {
        overflow: auto;
        white-space: nowrap
    }

    #workTeamSelector #input-text-area {
        background: 0;
        outline: 0;
        height: 30px;
        border: 0
    }

    #workTeamSelector .input-wrapper {
        display: inline-block;
    }

    #workTeamSelector .item {
        background: #555;
        color: #eee;
        font-size: 13px;
        padding: 3px 5px;
        line-height: 18px;
        border: 1px solid #555;
        margin-right: 6px;
        border: 1px solid black;
        display: inline-block
    }

    #workTeamSelector .item .close-btn {
        display: block;
        float: right;
        cursor: pointer;
        margin: 6px 2px 0 5px;
        width: 7px;
        height: 7px;
        background-image: url(data:image/png;base64,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)
    }

    #workTeamSelector .drop-menu {
        position: absolute;
        background: #eaeaea;
        width: 358px;
        max-height: 292px;
        overflow: auto !important;
        top: 38px;
        font-size: 14px;
        font-family: "微软雅黑","Helvetica Neue",Helvetica,Arial,sans-serif;
        left: 0;
        border: 1px solid #eaeaea
    }

    #workTeamSelector .drop-menu .selection {
        padding: 2px 5px;
        background: #fff;
        cursor: pointer;
        line-height: 25px
    }

    #workTeamSelector .drop-menu .selection:hover {
        background: #eaeaea
    }
    .header-icon-amis{
        background-image: url(${_STATIC_COMMON_}/img/return_amis.png);
        background-repeat: no-repeat;
        width: 17px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 12px;
    }

    .header-icon-screen {
        background-image: url(${_STATIC_COMMON_}/img/screen.png);
        background-repeat: no-repeat;
        width: 17px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 12px;
    }
</style>
<header id="header" class="header skin-black pace-done">
    <div class="logo">
        <div class="logo_facilityone" id="logo_facilityone"></div>
    </div>
    <nav class="navbar navbar-static-top" role="navigation">
        <!-- Sidebar toggle button-->
        <a href="#" class="navbar-btn sidebar-toggle sidebar-collapse" data-toggle="offcanvas" role="button" id="sidebar-collapse">
            <#--<span class="sr-only">Toggle navigation</span>-->
            <#--<span class="icon-bar"></span>-->
            <#--<span class="icon-bar"></span>-->
            <#--<span class="icon-bar"></span>-->
            <i aria-hidden="true" class="header-icon-bar"></i>
        </a>

        <div id="workTeamSelector" class="workteam-selector-wrapper">
            <div class="inner-wrapper">
                <div class="input-wrapper">
                    <input id="input-text-area" type="text" autocomplete="off" placeholder="请输入部门">
                </div>
            </div>
            <div class="search-ico"></div>
            <div class="drop-menu" style="overflow: auto !important;">
<#--                <ul id="treeDepart" class="ztree" ></ul>-->
            </div>
        </div>
        <div class="navbar-right">
            <ul class="nav navbar-nav pull-right">
                <#if screen??>
                    <#if screen>
                        <li>
                            <a class="dropdown-toggle" href="javascript:void(0)" id="toScreen" onclick="jumpScreen()">
                                <i aria-hidden="true" class="header-icon-screen"></i>
                            </a>
                        </li>
                    </#if>
                </#if>

                <#if jump??>
                    <#if jump>
                    <#--<input type="hidden" id="url" value="${url}"/>-->
                    <#--<input type="hidden" id="key" value="${key}"/>]-->
                        <#--<li >
                            <a class="dropdown-toggle" href="javascript:void(0)"  id="toSpace" onclick="jump()">
                                <i aria-hidden="true" class="header-icon-amis"></i>
                                <span class="username">
                           空间
                        </span>
                            </a>
                        </li>-->
                    </#if>
                </#if>

                <li id="header-projects" class="dropdown programsGroup" style="margin-right: 20px;">
                    <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                        <span class="username">
                        <#if pcurrent??>
                                ${pcurrent.name?html}
                            <#else>
                        ${i18n('page.index.head.projectHome')}
                        </#if>
                         </span>
                    </a>
                    <ul class="dropdown-menu">
                        <dl style="margin-left: 10px;padding-top: 5px;position: relative;"><dd>
                            <li class="<#if !pcurrent??>selected</#if> pull-left">
                            <#if isem?c=="false">
                                <a href="javascript:void(0);"> ${i18n('page.index.head.projectHome')} </a>
                            </#if>

                            <#-- 当登录人只有一个项目时，样式调整 -->
                            <#if isem?c=="true">
                                <div style="opacity: 0;margin-right: 27px;height: 30px;line-height: 28px;width: 120px;float: left;padding: 0 6px;margin-top: 20px;margin-bottom: 10px;">&nbsp;&nbsp;</div>
                            </#if>
                                <input type="text"
                                       placeholder="${i18n('page.index.head.search')}"
                                       style="display: block;box-sizing: border-box;width: 250px;height: 34px;color: #1f2d3d;
                                        border: 1px solid #e6e6e6 !important;outline: 0;transform: translate(35px, 20px);
                                        border-radius: 4px;padding: 3px 10px;"
                                        id="header-project-search">
                            </li>
                        </dd>
                        </dl>
                    <#if progros?exists>
                        <#list progros?keys as key>
                            <dl style="margin-left: 10px; padding-top: 5px;clear: both">
                                <dt data-group-name="${key}"> ${key?html}</dt>
                                <dd>
                                    <#list progros[key] as p>
                                        <li class="<#if pcurrent?? && p.id==pcurrent.id>selected</#if> pull-left"
                                            data-project-name="${p.id}"><a href="javascript:void(0);" data-pid="${p.id?c}" title="${p.name?html}">${p.name?html}</a></li>
                                    </#list>
                                </dd>
                            </dl>
                        </#list>
                    </#if>
                    </ul>
                </li>

                <li id="header-message" class="dropdown">
                    <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                    <#--<i class="fa fa-envelope"></i>-->
                        <i aria-hidden="true" class="header-icon-notice"></i>
                        <span>${i18n('page.index.head.notification')}</span>
                        <span class="badge unreadnumber" style="display:none;">0</span>
                    </a>
                    <ul class="dropdown-menu inbox">
                    <#--<li class="dropdown-title">-->
                    <#--<span><i class="fa fa-envelope-o"></i> <span class="unreadnumber">0</span> ${i18n('page.index.head.unreadmsg')}</span>-->
                    <#--</li>-->
                        <div id="unreadmessage" class="messageTip" style="white-space: nowrap;"></div>
                        <li onclick="showMeaageSite()" class="footer">
                        <#--<a>${i18n('page.index.head.allmsg')}<i class="fa fa-arrow-circle-right"></i></a>-->
                            <a style="float: none">${i18n('page.index.head.allmsg')}<i aria-hidden="true" class="fa fa-angle-right"></i></a>
                        </li>
                    </ul>
                </li>

                <li id="header-user" class="dropdown user">
                    <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                        <img src="${_STATIC_COMMON_}/img/default-head.png" alt="">
                        <span class="username">${userName}</span>
                    <#--<i class="s-angle-down"></i>-->
                        <i aria-hidden="true" class="header-icon-down"></i>
                        <input type="hidden" id="user_name" value="${userName}"/>
                        <input type="hidden" id="user_id" value="${userId?c}"/>
                    </a>
                    <ul class="dropdown-menu" style="width: 375px;max-width: 375px;box-shadow: 0 2px 4px 0 rgba(0,0,0,.12), 0 0 6px 0 rgba(0,0,0,.04);border: 1px solid #d3dce6;">
                        <div style="padding: 10px;">
                            <div class="header-user clearfix">
                                <img class="header-user-image" src="${_STATIC_COMMON_}/img/default-head.png"></img>
                                <div class="header-user-info">
                                    <div class="header-user-name">${userName}</div>
                                    <div class="header-logout"><a onclick="exit()">${i18n('page.index.head.exit')}</a></div>
                                </div>
                            </div>
                            <div class="header-user-links">
                                <ul class="header-user-links-group" style="padding: 30px 0 8px !important;">
                                    <li class="header-user-link">
                                        <a class="header-a" onclick="showPerson()">${i18n('page.index.head.profile')}</a>
                                    <#--<a @click="showPerson($event)" href="#">${i18n('page.index.head.profile')}</a>-->
                                    </li>
                                </ul>
                                <ul class="header-user-links-group" style="padding: 30px 0 8px !important;">
                                    <li class="header-user-link">
                                        <a class="header-a" target="_blank" href="https://help.fmone.cn/">${i18n('page.index.head.helpcenter')}</a>
                                    </li>
                                </ul>
                                <ul class="header-user-links-group" style="padding: 30px 0 8px !important;">
                                    <li class="header-user-link">
                                        <a class="header-a" target="_blank" href="http://www.facilityone.cn/customCase.html">${i18n('page.index.head.customservice')}</a>
                                    </li>
                                    <li class="header-user-link">
                                        <a class="header-a" target="_blank" href="http://www.facilityone.cn/contactUs.html">${i18n('page.index.head.feedback')}</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    <#--<li><a onclick="showPerson()"><i class="fa fa-user"></i> ${i18n('page.index.head.profile')}</a></li>-->
                    <#--<li><a onclick="shwoHelpCenter()"><i class="fa fa-info"></i> ${i18n('page.index.head.helpcenter')}</a></li>-->
                    <#--<li><a href="http://www.fmone.cn:3000/updates/web.html" target="_blank"><i class="fa fa-newspaper-o"></i> ${i18n('page.index.head.updatelog')}</a></li>-->

                    </ul>
                </li>
            </ul>
        </div>
    </nav>
</header>
<script type="text/javascript">
    window._AMapSecurityConfig = {
        securityJsCode:'f5a3e3f142b93959dc783c910d301ba6',
    }
</script>
<script type="text/javascript" src="//webapi.amap.com/maps?v=1.4.15&key=6823b9479fcb741eece4dc3c2b942ad9"></script>
<script type="text/javascript" src="//cache.amap.com/lbs/static/PlaceSearchRender.js"></script>
<script type="text/javascript" src="${_STATIC_COMMON_}/js/js-plugin/jquery/jquery-3.6.0.js"></script>
<script>
    $(document).ready(function(){

        //headpic
        if ("${headPic!}" != "") {
            var path = PUBLIC_PATH+"/common/files/id/${headPic!}/img?access_token=" + getAccessToken();
            $(".header-user-image").attr("src", path);
        }

        $.ajax({
            url:'/logo/CustomSettings',
            type: 'get',
            success: function (data) {
                var  data=data.data[0];
                if ($.cookie('mini_sidebar') === '1') {

                    $("#logo_facilityone").css("width", "50px");
                    if (data && data.systemAbbreviatedLogo) {
                        $("#logo_facilityone").css("background", "url("+PUBLIC_PATH+"/logo/" + data.systemAbbreviatedLogo + "/img) 50% 50%");
                    }else {
                        $("#logo_facilityone").css("background", "url("+PUBLIC_PATH+"/resource/img/logo-collapse.png) no-repeat 50% 50% / 36px");

                    }
                }
                else {
                    $("#logo_facilityone").css("width", "220px");
                    if (data && data.systemNormalLogo) {
                        $("#logo_facilityone").css("background", "url("+PUBLIC_PATH+"/logo/" + data.systemNormalLogo + "/img) 30% 50%");
                    }else {
                        $("#logo_facilityone").css("background", "url("+PUBLIC_PATH+"/resource/img/fone-logo.png) no-repeat 30% 50% / 161px");
                    }
                }
            }
        });

        $('#menu-project-silmsrcoll').slimScroll({height:'476px' });

        $('#contentTip').tooltip();

        //首页搜索
        $.ajax({
            'url':'/main/header/menuSearch',
            type:'POST',
            data:JSON.stringify({searchText:''}),
            success:function (res) {
                if(res.data) {
                    window.PROJECT_SEAECH = res.data;
                }
            }
        });

        $('#header-project-search').on('click', function (e) {
            e.stopPropagation();
        });

        $('#header-project-search').on('keydown', function (e) {
            if(e.keyCode && e.keyCode === 13) {
                var reg = new RegExp($('#header-project-search').val(), 'i');

                if(window.PROJECT_SEAECH) {
                    var projectList = window.PROJECT_SEAECH;
                    for(var i = 0; i<projectList.length; i++) {
                        var flag = false;
                        for(var j = 0; j<projectList[i].projects.length; j++) {
                            if(reg.test(projectList[i].projects[j].projectName)) {
                                flag = true;
                                $('[data-project-name='+projectList[i].projects[j].projectId +']').show();
                                $('[data-project-name='+projectList[i].projects[j].projectId +']').parents('dl').show();
                            } else {
                                $('[data-project-name='+projectList[i].projects[j].projectId +']').hide();
                                if(!flag) {
                                    $('[data-project-name='+projectList[i].projects[j].projectId +']').parents('dl').hide();
                                }
                            }
                        }
                        if(!flag) {
                            $('[data-project-name='+projectList[i].projects[0].projectId +']').parent().prev().hide();
                            //$('[data-group-name='+projectList[i].groupId +']').hide();
                        } else {
                            $('[data-project-name='+projectList[i].projects[0].projectId +']').parent().prev().show();
                            //$('[data-group-name='+projectList[i].groupId +']').show();
                        }
                    }
                }
            }
        })
        $("#workTeamSelector").hide();
    });
    window.addEventListener('hashchange', function(event) {
        $("#workTeamSelector").hide();
    })
    function jump() {
        window.location.href = PUBLIC_PATH + "/space/user/to/jump?access_token=" + getAccessToken();
    };

    function jumpScreen() {
        window.location.href = PUBLIC_PATH + "/screen?access_token=" + getAccessToken() + "&current_project=" + getCurrentProject();
    }
</script>
