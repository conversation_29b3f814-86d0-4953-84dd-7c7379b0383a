<footer>
</footer>
<!-- requirejs -->
<script type="text/javascript" src="${_STATIC_COMMON_}/app/tools/require.js${_STATIC_VERSION_}"></script>
<script type="text/javascript" src="${_STATIC_COMMON_}/app/tools/require.config.js${_STATIC_VERSION_}"></script>
<script type="text/javascript">

</script>
<#if environment == "develop">
    <script type="text/javascript" src="${_STATIC_COMMON_}/app/core/bundle.js${_STATIC_VERSION_}"></script>
<#elseif environment =="publish">
    <script type="text/javascript" src="${_STATIC_COMMON_}/dist/core/bundle.js${_STATIC_VERSION_}"></script>
<#else>
    <script type="text/javascript" src="${_STATIC_COMMON_}/app/core/bundle.js${_STATIC_VERSION_}"></script>

</#if>

