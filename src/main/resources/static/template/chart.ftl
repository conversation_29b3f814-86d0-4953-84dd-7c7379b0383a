<div id="chartContent" class="col-lg-12">
    <div class="row">
        <div class="col-md-6">
            <div class="box border ">
                <div class="box-title">
                    <h4><i class="fa fa-adjust"></i>设备</h4>
                    <div class="tools hidden-xs">

                    </div>
                </div>
                <div class="box-body">
                    <div id="pie_chart1" class="chart"></div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="box border ">
                <div class="box-title">
                    <h4><i class="fa fa-adjust"></i>工单</h4>
                    <div class="tools hidden-xs">

                    </div>
                </div>
                <div class="box-body">
                    <div id="pie_chart2" class="chart"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="box border">
                <div class="box-title">
                    <h4><i class="fa fa-bar-chart-o"></i>电量</h4>
                    <div class="tools hidden-xs">

                    </div>
                </div>
                <div class="box-body clearfix">
                    <div id="bar_chart1" style="height:400px;"></div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="box border">
                <div class="box-title">
                    <h4><i class="fa fa-bar-chart-o"></i>水量</h4>
                    <div class="tools">

                    </div>
                </div>
                <div class="box-body">
                    <div id="bar_chart2" style="height:400px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var jsonDate=function(url){
        var redata;
        $.ajax({
            url : url,
            type : "GET",
            contentType : "application/json",
            async : false,
            success : function(data) {
                redata=data.data;
            }
        });
        return redata;

    }
  var Charts=function(){
      return{
       "initPieCharts":function(){
           //echarts--PIE1
           /* 设备 */
           var equipment_url="/eq006/reportdata";
           var data=jsonDate(equipment_url);
           var chart_data=[];
           var data1;//故障
           var data2;//总量
           var data3;//无故障
           for(var i=0;i<data.length;i++){
               if(data[i]["label"]=="故障设备数量"){
                   data1=data[i];
               }else if(data[i]["label"]=="设备总量"){
                   data2=data[i];
               }
           }
           var data3={"name":"未发生故障设备数量","value":data2["data"]-data1["data"]};
           var data1_1={"name":"故障设备数量","value":data1["data"]};
           chart_data.push(data1_1);
           chart_data.push(data3);
           var echarts_pie1 = echarts.init(document.getElementById('pie_chart1'));

           var option = {
               title : {
                   text: '设备故障率',//图表大标题
                   subtext: '',//图表小标题
                   x:'center'//标题位置：居中
               },
               tooltip : {
                   trigger: 'item',
                   formatter: "{a} <br/>{b} : {c} ({d}%)"
               },
               legend: {
                   orient : 'vertical',//项排列方式
                   x : 'right',//项排列位置
                   data:['故障设备数量','未发生故障设备数量']//项
               },
               toolbox: {
                   show : false,//右上角加强功能显示
                   feature : {
                       mark : {show: true},
                       dataView : {show: true, readOnly: false},
                       magicType : {//动态效果
                           show: false,
                           type: ['pie', 'funnel'],
                           option: {
                               funnel: {//漏斗图
                                   x: '25%',
                                   width: '50%',
                                   funnelAlign: 'left',
                                   max: 1548
                               }
                           }
                       },
                       restore : {show: true},
                       saveAsImage : {show: true}
                   }
               },
               calculable : false,//3D拖拽功能
               series : [
                   {
                       name:'设备',
                       type:'pie',
                       radius : '65%',
                       center: ['50%', '60%'],
                       data:chart_data
                   }
               ]
           };
           // 为echarts对象加载数据
           if(chart_data.length>0){
               echarts_pie1.setOption(option);
           }
           /* 工单 */
           var wo_data=[];
           var order_url="/eq007/reportdata"
           var order_date=jsonDate(order_url);
           var data4;//未完成工单
           var data5;//工单总数
           var data6;//已完成工单
           for(var i=0;i<order_date.length;i++){
               if(order_date[i]["label"]=="未完成的工单数"){
                   data4=order_date[i];
               }else if(order_date[i]["label"]=="当天总工单数量"){
                   data5=order_date[i];
               }
           }
           var data6={"name":"已完成的工单数","value":data5["data"]-data4["data"]};
           var data44={"name":"未完成的工单数","value":data4["data"]};
           wo_data.push(data44);
           wo_data.push(data6);

           var echarts_pie2= echarts.init(document.getElementById('pie_chart2'));
           var option_pie2 = {
               title : {
                   text: '工单维修及时率',//图表大标题
                   subtext: '',//图表小标题
                   x:'center'//标题位置：居中
               },
               tooltip : {
                   trigger: 'item',
                   formatter: "{a} <br/>{b} : {c} ({d}%)"
               },
               legend: {
                   orient : 'vertical',//项排列方式
                   x : 'right',//项排列位置
                   data:['已完成的工单数','未完成的工单数']//项
               },
               toolbox: {
                   show : false,//右上角加强功能显示
                   feature : {
                       mark : {show: true},
                       dataView : {show: true, readOnly: false},
                       magicType : {//动态效果
                           show: false,
                           type: ['pie', 'funnel'],
                           option: {
                               funnel: {//漏斗图
                                   x: '25%',
                                   width: '50%',
                                   funnelAlign: 'left',
                                   max: 1548
                               }
                           }
                       },
                       restore : {show: true},
                       saveAsImage : {show: true}
                   }
               },
               calculable : false,//3D拖拽功能
               series : [
                   {
                       name:'工单',
                       type:'pie',
                       radius : '65%',
                       center: ['50%', '60%'],
                       data:wo_data
                   }
               ]
           };
           // 为echarts对象加载数据
           if(wo_data.length>0){
               echarts_pie2.setOption(option_pie2);
               echarts_pie2.on('click',function(param){
                   if(param.dataIndex==0){
                       openMenuPage("/opt005",{"type":"noComplete"});
                   }else if(param.dataIndex==1){
                       openMenuPage("/opt005",{"type":"complete"});
                   }
               });
           }

       },
       "initLintCharts":function(){
           var bar_url="/par007/reportdata";
           var bar_data=jsonDate(bar_url);
           var ele=bar_data["电量"];
           var ele_mon=[];
           var ele_val=[];
           var wat=bar_data["水量"];
           var wat_mon=[];
           var wat_val=[];
               if(ele!=undefined&&ele!=null&&ele.length>0){
                  for(var i=0;i<ele.length;i++){
                      ele_mon[11-i]=ele[i].x;
                      ele_val[11-i]=ele[i].y;

                      //ele_mon.push(ele[i].x);
                      //ele_val.push(ele[i].y);
                  }
               }
               if(wat!=undefined&&wat!=null&&wat.length>0){
                   for(var i=0;i<wat.length;i++){
                       wat_mon[11-i]=wat[i].x;
                       wat_val[11-i]=wat[i].y;

                       //wat_mon.push(wat[i].x);
                      // wat_val.push(wat[i].y);
                   }
               }
           //电量
           var echarts_bar1= echarts.init(document.getElementById('bar_chart1'));
           option_bar1 = {
               title : {
                   text: '用电量',
                   subtext: ''
               },
               tooltip : {
                   trigger: 'axis'
               },
               legend: {
                   data:['电量'],
                   x : 'right',//项排列位置
               },
               toolbox: {
                   show : false,
                   feature : {
                       mark : {show: true},
                       dataView : {show: true, readOnly: false},
                       magicType : {show: true, type: ['line', 'bar']},
                       restore : {show: true},
                       saveAsImage : {show: true}
                   }
               },
               calculable : false,
               xAxis : [
                   {
                       type : 'category',
                       data : ele_mon
                   }
               ],
               yAxis : [ { type : 'value'  ,axisLabel: {formatter: '{value} KW'} }],
               series : [
                   {
                       name:'电量',
                       type:'bar',
                      // barWidth: 50,
                       data:ele_val,
                       markPoint : {
                           data : [
                               {type : 'max', name: '最大值'},
                               {type : 'min', name: '最小值'}
                           ]
                       },
                       markLine : {
                           data : [
                               {type : 'average', name: '平均值'}
                           ]
                       }
                   }
               ]
           };
           if(ele_mon.length>0&&ele_val.length>0){
               echarts_bar1.setOption(option_bar1);
           }

           //水量
           var echarts_bar2= echarts.init(document.getElementById('bar_chart2'));
           option_bar2 = {
               title : {
                   text: '用水量',
                   subtext: ''
               },
               tooltip : {
                   trigger: 'axis'
               },
               legend: {
                   data:['水量'],
                   x : 'right',//项排列位置
               },
               toolbox: {
                   show : false,
                   feature : {
                       mark : {show: true},
                       dataView : {show: true, readOnly: false},
                       magicType : {show: true, type: ['line', 'bar']},
                       restore : {show: true},
                       saveAsImage : {show: true}
                   }
               },
               calculable : false,
               xAxis : [
                   {
                       type : 'category',
                       data : wat_mon
                   }
               ],
               yAxis : [ { type : 'value',   axisLabel: {formatter: '{value} m3'}} ],
               series : [
                   {
                       name:'水量',
                       type:'bar',
                       //barWidth: 50,
                       data:wat_val,
                       markPoint : {
                           data : [
                               {type : 'max', name: '最大值'},
                               {type : 'min', name: '最小值'}
                           ]
                       },
                       markLine : {
                           data : [
                               {type : 'average', name: '平均值'}
                           ]
                       }
                   }
               ]
           };
           if(wat_mon.length>0&&wat_val.length>0){
               echarts_bar2.setOption(option_bar2);
           }
       }
      };
  }();
</script>