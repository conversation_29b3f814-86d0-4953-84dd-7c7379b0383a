<head>
    <meta content="text/html; charset=UTF-8" http-equiv="content-type">
    <meta charset="utf-8">
    <title>${title!}</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no" name="viewport">
    <meta content="" name="description">
    <meta content="FacilityOne" name="author">
    <link rel="shortcut icon" href="${baseStaticPath}/favicon.ico"/>
    <link rel="bookmark" href="${baseStaticPath}/favicon.ico"/>

    <!-- 引入font样式 -->
    <link rel="stylesheet" href="${_STATIC_COMMON_}/css/font-awesome/css/font-awesome.min.css">

    <!-- 引入element样式 -->
    <link rel="stylesheet" href="${_STATIC_COMMON_}/app/components/element.ui/theme-default/index.css">
    <link rel="stylesheet" href="${_STATIC_COMMON_}/app/components/element.ui/theme-default/reset.css">

    <!-- 引入页面样式 -->
    <link rel="stylesheet" href="${_STATIC_COMMON_}/app/components/shang.extensions/smooth-scrollbar.css">
    <link rel="stylesheet" href="${_STATIC_COMMON_}/css/fmone-v2.1/index.css">
    <style>
        [v-cloak] {display: none;}
        .header{
            display: none;
        }
        .main-content.no-visiable {
            margin-left: 0px;
            margin-top: 0;
            padding-top: 0px;
        }
    </style>
    <!-- js debug 模式 -->
    <script>
        window.DEBUG = true;
        var PUBLIC_PATH = '${baseStaticPath}';
        var isWhiteUser = '${isWhiteUser?string ("true","false")}';
    </script>
    <script>
        var user_default_head_pic = "${_STATIC_COMMON_}/img/default-head.png";
    </script>
    <!-- analysis -->
    <script>
        var _hmt = _hmt || [];
        (function() {
            if(!DEBUG){
                var hm = document.createElement("script");
                hm.src = "//hm.baidu.com/hm.js?3b807d23084847d803aa268f129d7e87";
                var s = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(hm, s);
            }
            //不用自动统计
            _hmt.push(['_setAutoPageview', false]);
        })();
    </script>
</head>


