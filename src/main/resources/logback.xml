<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Logback Configuration. See http://logback.qos.ch/manual/index.html -->

<configuration scan="true" scanPeriod="30 seconds" debug="true">

    <property resource="application.properties" />

    <property resource="application.properties" />
    <if condition='"dev".equals(property("spring.profiles.active"))'>
        <then>
            <property resource="application-dev.properties" />
        </then>
    </if>

    <if condition='"test".equals(property("spring.profiles.active"))'>
        <then>
            <property resource="application-test.properties" />
        </then>
    </if>

    <if condition='"prod".equals(property("spring.profiles.active"))'>
        <then>
            <property resource="application-prod.properties" />
        </then>
    </if>

    <!-- <include resource="org/springframework/boot/logging/logback/base.xml" /> -->

    <!-- Simple file output -->
    <if condition='property("logback.file.enabled").contains("true")' >
        <then>
            <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <!-- rollover daily -->
                    <fileNamePattern>${logback.path}/${logback.file.prefix}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                    <maxHistory>7</maxHistory>
                    <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <maxFileSize>20MB</maxFileSize>
                    </timeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
                <!--Support multiple-JVM writing to the same log file -->
                <!--<prudent>false</prudent>-->
                <encoder>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} -|- %thread -|- %-5level -|- %logger{20} -|- %msg%n</pattern>
                </encoder>
            </appender>
        </then>
    </if>

    <if condition='property("logback.file-error.enabled").contains("true")' >
        <then>
            <appender name="FILE-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <filter class="ch.qos.logback.classic.filter.LevelFilter">
                    <level>ERROR</level>
                    <onMatch>ACCEPT</onMatch>
                    <onMismatch>DENY </onMismatch>
                </filter>
                <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${logback.path}/${logback.file.prefix}-error-%d{yyyy-MM-dd}.log</fileNamePattern>
                    <maxHistory>14</maxHistory>
                </rollingPolicy>
                <encoder>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} -|- %thread -|- %-5level -|- %logger{20} -|- %msg%n</pattern>
                </encoder>
            </appender>
        </then>
    </if>
    
    <if condition='property("logback.file-warn.enabled").contains("true")' >
        <then>
            <appender name="FILE-WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <filter class="ch.qos.logback.classic.filter.LevelFilter">
                    <level>WARN</level>
                    <onMatch>ACCEPT</onMatch>
                    <onMismatch>DENY </onMismatch>
                </filter>
                <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${logback.path}/${logback.file.prefix}-warn-%d{yyyy-MM-dd}.log</fileNamePattern>
                    <maxHistory>14</maxHistory>
                </rollingPolicy>
                <encoder>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} -|- %thread -|- %-5level -|- %logger{20} -|- %msg%n</pattern>
                </encoder>
            </appender>
        </then>
    </if>

    <!-- MonitorLog日志 -->
    <!-- <appender name="FILE-MONITOR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logback.path}/monitor-%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} -|- %thread -|- %msg%n</pattern>
        </encoder>
    </appender> -->

    <!-- Sql 日志 -->
    <!-- <appender name="FILE-SQL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logback.path}/sql-%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} -|- %thread -|- %msg%n</pattern>
        </encoder>
    </appender> -->

    <if condition='property("logback.file-email.enabled").contains("true")' >
        <then>
            <appender name="EMAIL-ERROR" class="ch.qos.logback.classic.net.SMTPAppender">
                <filter class="ch.qos.logback.classic.filter.LevelFilter">
                    <level>ERROR</level>
                    <onMatch>ACCEPT</onMatch>
                    <onMismatch>DENY </onMismatch>
                </filter>
                <smtpHost>smtp.qq.com</smtpHost>
                <smtpPort>465</smtpPort>
                <SSL>true</SSL>
                <username><EMAIL></username>
                <password>njfrdhcdjqkagaba</password>
                <from><EMAIL></from>
                <to><EMAIL></to>
                <subject>【fone-shang】: %logger</subject>
                <asynchronousSending>false</asynchronousSending>
                <layout class="ch.qos.logback.classic.html.HTMLLayout" >
                    <pattern>%date%level%thread%logger{0}%line%message</pattern>
                </layout>
            </appender>
        </then>
    </if>

    <!-- Console output -->
    <if condition='property("logback.stdout.enabled").contains("true")' >
        <then>
            <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
                <encoder>
                    <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{5} - %msg%n</pattern>
                </encoder>
                <!--<filter class="ch.qos.logback.classic.filter.ThresholdFilter">-->
                <!--<level>DEBUG</level>-->
                <!--</filter>-->
                <!-- <filter class="ch.qos.logback.classic.filter.LevelFilter">
                     <level>DEBUG</level>
                     <onMatch>ACCEPT</onMatch>
                     <onMismatch>DENY </onMismatch>
                </filter> -->
            </appender>
        </then>
    </if>
    
     <!-- Socket Server output -->
    <if condition='property("logback.socket.enabled").contains("true")' >
        <then>
            <appender name="SOCKET_SERVER" class="ch.qos.logback.classic.net.server.ServerSocketAppender">
                <encoder>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} -|- %thread -|- %-5level -|- %logger{20} -|- %msg%n</pattern>
                </encoder>
                <port>${logback.socket.port}</port>
                <includeCallerData>false</includeCallerData>
            </appender>
        </then>
    </if>
    
    <appender name="XIA_LOG_SOCKET" class="cn.facilityone.xia.log.service.XiaLogSocketAppender">
         <encoder>
             <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} -|- %thread -|- %-5level -|- %logger{20} -|- %msg%n</pattern>
         </encoder>
         <chanel>${logback.taillog.socket.chanel}</chanel>
     </appender>

    <!-- Enable FILE and STDOUT appenders  -->
    <root level="WARN">
        <if condition='property("logback.file.enabled").contains("true")' >
            <then><appender-ref ref="FILE"/></then>
        </if>
        <if condition='property("logback.file-warn.enabled").contains("true")' >
            <then><appender-ref ref="FILE-WARN"/></then>
        </if>
        <if condition='property("logback.file-error.enabled").contains("true")' >
            <then><appender-ref ref="FILE-ERROR"/></then>
        </if>
        <if condition='property("logback.file-email.enabled").contains("true")' >
            <then><appender-ref ref="EMAIL-ERROR"/></then>
        </if>
        <if condition='property("logback.stdout.enabled").contains("true")' >
            <then><appender-ref ref="STDOUT"/></then>
        </if>
        <if condition='property("logback.socket.enabled").contains("true")' >
            <then><appender-ref ref="SOCKET_SERVER"/></then>
        </if>
        <appender-ref ref="XIA_LOG_SOCKET"/>
    </root>

    <!-- xia -->
    <logger name="cn.facilityone" level="DEBUG" />
    <logger name="cn.facilityone.xia.core.exception.mapper" level="ERROR" />
    
     <!-- monitor additivity="false"-->
    <logger name="cn.facilityone.xia.core.common.MonitorLog" level="DEBUG" />

    <!-- spring boot -->
    <logger name="org.springframework.boot" level="INFO"/>
    <logger name="org.springframework.jdbc.core" level="DEBUG"/>

    <!-- Hibernate -->
    <logger name="org.hibernate.SQL" level="DEBUG" />
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder"  level="TRACE" />
    <logger name="org.hibernate.engine.QueryParameters" level="DEBUG"  />
    <logger name="org.hibernate.engine.query.HQLQueryPlan" level="ERROR" />

    <!-- atomikos -->
    <logger name="com.atomikos" level="ERROR" />

</configuration>
