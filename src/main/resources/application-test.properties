#============================================================================
# Configure shang
#============================================================================
shang.system.zhName=F-ONE \u8BBE\u65BD\u670D\u52A1\u7BA1\u7406\u8F6F\u4EF6
shang.system.enName=F-ONE FM System
# base dir of file upload
shang.system.uploadBaseDir=/home/<USER>/upload
#epayment 22,22100,22200,22300,22400,11910
#app epayment 7120,7121,7122,7123,7124,7125,7126
shang.system.notShowMenus=
shang.system.onlyAdminMenus=11910,28,35,35001,35002,35003,35004,35005,35006,35007,35008,35009,35010,35011
shang.system.wechatMenus=2400,2700
shang.system.dingTalkMenus=2400
shang.system.apiSignValidate=true
#WorkOrder and Requirement deletion
shang.system.woDeletonSwitch=true

# wechat service info
shang.wechat.debug=true
shang.wechat.name=F-ONE \u5FAE\u4FE1\u670D\u52A1
shang.wechat.appId=wx82d9c9a75a96b4c0
shang.wechat.appSecret=db6913d080320b6888e43a0adc35df26
shang.wechat.apiAuthToken=facilityonewechatapi
shang.wechat.mediaPath=/wechatmedia
shang.wechat.templateId=R6R6iq8XiVzMjiqn7iJ-RKAFv382ByqRi19bkSOKmxI
shang.wechat.visitortemplateId=2jHTZ8jynX7Q66gmwfx-YYDlbRQtAoSIdgxSS0EuPbA
shang.wechat.domainname=http://soa.wuxi.fmone.cn
#shang.wechat.defaultArticleTitle=30\u79D2\u64CD\u4F5C\u6307\u5357
#shang.wechat.defaultArticleUrl=/api/wechat/h5/guide
#shang.wechat.defaultArticlePicUrl=/resource/mobile/img/news.png

# config company info
shang.company.name=F-ONE
shang.company.phone=021-56979102
shang.company.fax=021-56979102
shang.company.address=\u4E0A\u6D77\u5E02\u95F8\u5317\u533A\u4E2D\u5174\u8DEF1757\u53F7
shang.company.postcode=200070
shang.company.mobile=15206186877
shang.company.linkMan=\u5C0F\u6797
shang.company.email=<EMAIL>

debug : true
#spring.freemarker.check-template-location=false
#xia.persistence.spring-data-jpa.repository-packages=cn.facilityone.sa
#xia.persistence.entity-base-packages=cn.facilityone.sa

#============================================================================
# Configure Spring
#============================================================================
#jta
spring.jta.enable_logging=false
spring.jta.force_shutdown_on_vm_exit=true
spring.jta.log_base_dir=/home/<USER>/project/logs/jta
#spring.jta.console_log_level=DEBUG
spring.jta.max_actives=-10
spring.jta.max_timeout=600000
spring.jta.default_jta_timeout=600000
spring.jta.serial_jta_transactions=false

spring.jta.atomikos.minPoolSize=80
spring.jta.atomikos.maxPoolSize=120
spring.jta.atomikos.borrowConnectionTimeout=180
spring.jta.atomikos.reapTimeout=0
spring.jta.atomikos.maxIdleTime=120
spring.jta.atomikos.maintenanceInterval=60
spring.jta.atomikos.maxLifetime=0
spring.jta.atomikos.testQuery=SELECT 1
##################### datasource #####################
# default to use H2 database, if your want to use another one, please uncomment following lines
# MYSQL JDBC Settings...
spring.datasource.qualifier=xaDataSource
#spring.datasource.xa.dataSourceClassName=com.mysql.jdbc.jdbc2.optional.MysqlXADataSource
#spring.datasource.driverClassName=com.mysql.jdbc.Driver
spring.datasource.xa.dataSourceClassName=com.mysql.cj.jdbc.MysqlXADataSource
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.url=********************************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Fmone@2025
#true when first install,then start must be false
spring.datasource.initialize=false
spring.datasource.sqlScriptEncoding=UTF-8
# false : tomcat pool
spring.datasource.jta=true
#tomcat pool
spring.datasource.maxActive=100
spring.datasource.initialSize=20
spring.datasource.minIdle=20
spring.datasource.maxIdle=100
spring.datasource.maxWait=600000
spring.datasource.numTestsPerEvictionRun=-1
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=true
spring.datasource.testOnReturn=false
spring.datasource.valiatindQuery=select 1
spring.datasource.timeBetweenEvictionRunsMillis=1000
spring.datasource.minEvictableIdleTimeMillis=30000

##################### mybatis #####################
#mybatis
# https://github.com/mybatis/spring-boot-starter
mybatis.config-locations=classpath:mybatis/mybatis-config.xml
mybatis.type-aliases-package=cn.facilityone.shang.*.*.alias
mybatis.mapper-locations=classpath*:cn/facilityone/**/mapper/*.xml
mybatis.configuration.mapUnderscoreToCamelCase=true
#mapper
# https://github.com/abel533/mapper-boot-starter
mapper.mappers=cn.facilityone.xia.persistence.mybatis.XiaMapper
mapper.not-empty=false
mapper.identity=MYSQL

#pagehelper
# https://github.com/pagehelper/pagehelper-spring-boot
pagehelper.helperDialect=mysql
pagehelper.reasonable=true
pagehelper.supportMethodsArguments=true
pagehelper.params=count=countSql

##################### jpa #####################
spring.jpa.openInView=true
spring.jpa.show-sql=false
# Dialect
spring.jpa.database-platform=org.hibernate.dialect.MySQL5Dialect
spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=update

#============================================================================
# Configure xia-core
#============================================================================
#develop publish
xia.core.environment=develop
#acceptable app version, if not in below versions, return error
xia.core.appVersionCheck=false
xia.core.appVersionIOS=1.0.0
xia.core.appVersionAndroid=1.0.0
xia.core.baseUrl=
xia.core.baseStaticPath=
xia.core.monitoringPublishPageUrl=http://publish.community.eegrid.com/PublishPage.aspx
xia.core.whiteUser=icc_sync

xia.jersey.resourcePackage=cn.facilityone.shang
xia.jersey.loginUrl=login
xia.jersey.successUrl=index.html
xia.jersey.unauthorizedUrl=error_500.html
xia.jersey.allAllowed=false
xia.jersey.accessiblePattern=/login/**,/oauth2/**,/api/wechat/**,/wechat/**,/logo/**,/druid/**,/common/license/**,\
  /epayment000/notify/**,/icc/**,/logout,/api/fms/**,/soa/h5/**,/m/v1/user/regist/*,/space/user/jump,/fs/**,\
  /common/files/upload/*,/coe/**,/billboards/home/<USER>/billboards/home/<USER>/mp/**
# trace on http header : OFF, ON_DEMAND, ALL
xia.jersey.tracingLevel=OFF
# no html tag replace
xia.jersey.accessibleContentPath=/**
xia.jersey.jerseyExecludePath=|.*\\.mp4|.*\\.json|.*\\.woff2|/druid/submitLogin
xia.jersey.skipGzipPattern=/api/fms/material/*

# thread pool
xia.thread.pool.size.default=4
xia.thread.pool.size.message=4
xia.thread.pool.size.report=4

#============================================================================
# Configure server
#============================================================================
# HTTP port
#server.port =8084
#server.address=
#server.sessionTimeout
#server.context-path=/f1-shang

#============================================================================
# Management server Configuration
#============================================================================
endpoints.jmx.enabled=false
# close spring boot actuator
management.port=-1
management.context-path=/manage
#management.security.enabled=true is not support now.
management.security.enabled=false

#spring.boot.admin.url=http://***************:8090
#spring.boot.admin.url=http://127.0.0.1:8090
#spring.boot.admin.client.serviceUrl=http://127.0.0.1:8080
#spring.boot.admin.client.name=F-ONE

#info.version=1.0.0
#info.i=2017-06-03 \u4E0A\u7EBFXX\u529F\u80FD
#============================================================================
# Configure xia-message
#============================================================================
xia.message.core.debug=false
# each sender switch
xia.message.core.siteSend=true
xia.message.core.mailSend=true
xia.message.core.smsSend=true
xia.message.core.mpushSend=true
xia.message.core.maxThreadPool=8

# web socket
xia.message.site.hostname=
xia.message.site.chanel=DefaultChannel
xia.message.site.port=9901
# open ssl then view with https
xia.message.site.keystore=
xia.message.site.keystorepwd=

xia.message.email.smtpHost=smtp.qq.com
xia.message.email.systemName=1421695258
xia.message.email.systemPwd=pxitquvnumyyjdhb
xia.message.email.systemEmail=<EMAIL>
xia.message.email.fromAlias=F-ONE\u7CFB\u7EDF\u901A\u77E5
xia.message.email.isSSL=true
xia.message.email.port=465

xia.message.email.smtpHost2=smtp.qq.com
xia.message.email.systemName2=1421695258
xia.message.email.systemPwd2=pxitquvnumyyjdhb
xia.message.email.systemEmail2=<EMAIL>
xia.message.email.fromAlias2=F-ONE\u7CFB\u7EDF\u901A\u77E5
xia.message.email.isSSL2=true
xia.message.email.port2=465

xia.message.email.smtpHost3=smtp.qq.com
xia.message.email.systemName3=1421695258
xia.message.email.systemPwd3=pxitquvnumyyjdhb
xia.message.email.systemEmail3=<EMAIL>
xia.message.email.fromAlias3=F-ONE\u7CFB\u7EDF\u901A\u77E5
xia.message.email.isSSL3=true
xia.message.email.port3=465

xia.message.email.encoding=utf-8
xia.message.email.valid=^([a-zA-Z0-9]*[-._]?[a-zA-Z0-9]+)*@([a-zA-Z0-9]*[-_]?[a-zA-Z0-9]+)+[\\.][A-Za-z]{2,3}([\\.][A-Za-z]{2})?$

#change code than need register info to emay
xia.message.sms.code=0SDK-EAA-6688-JEQTO
xia.message.sms.pwd=357021
xia.message.sms.prefix=\u3010\u4E0A\u6D77\u8D39\u54F2\u3011
### I18n Phone Number Valid ###
xia.message.sms.valid=^((13[0-9])|(14[5-9])|(15[^4,\\D])|(17[0-8])|(18[0-9])|(19[8,9])|166)\\d{8}$

#ali  sms
xia.message.sms.aliAccessKeyId=LTAIKD67IKISkIHW
xia.message.sms.aliAccessSecret=SHHcELNusUi2rlwK2piMpGZxneOltz
xia.message.sms.aliSignName=FacilityONE
xia.message.sms.defaultTemplateCode=SMS_156900768
xia.message.sms.aliMaxAccount=200
xia.message.sms.defaultPushAli=false

xia.message.mpush.secretKeyAndroid=3a5054768690a92c7f4ed65465ec6aa2
xia.message.mpush.accessIdAndroid=**********
xia.message.mpush.secretKeyIOS=49468be69d2322e9cb8e8d61d5c4c6a1
xia.message.mpush.accessIdIOS=**********
xia.message.mpush.defaultTitle=\u4E0A\u6D77\u8D39\u54F2
xia.message.mpush.maxAccount=500
#1 = product   2 = development  blank = development
xia.message.mpush.environment=2
#customer uuid
xia.message.mpush.serverId=12345
################ ali push
xia.message.mpush.defaultPushAli=true
# Android appId
xia.message.mpush.aliPushAndroidAppKey=********
#Ios appId
xia.message.mpush.aliPushIosAppKey=*********
# key
xia.message.mpush.aliAccessKey=LTAI083vKXr9anPs
# secret
xia.message.mpush.aliAccessSecret=wExto9ZLo1DEMaSRQJ13EVeGJS8QUp
# activity
xia.message.mpush.aliAndroidActivity=com.facilityone.wireless.a.common.push.PopupPushActivity

xia.message.mpush.connectionTimeout=3000
xia.message.mpush.readTimeout=5000

#============================================================================
# Enable SSL
#============================================================================
#server.port = 8443
#server.ssl.key-store = classpath:keystoresa.jks
#server.ssl.key-store-password = 1qaz2wsx3edc
#server.ssl.key-password = 1qaz2wsx3edc

#============================================================================
# Configure init datatable with sql
#============================================================================
# base path is classpath,many sql file seperate with COMMA,
#xia.datatable.init.sql=init/quartz_tables_mysql.sql

#============================================================================
# Configure velocity
#============================================================================
spring.velocity.checkTemplateLocation=false

#============================================================================
# Configure freemarker
#============================================================================
spring.freemarker.checkTemplateLocation=false

#============================================================================
# Configure i18n
#============================================================================
#xia.i18n.basename=other1,other2
xia.i18n.debug=false
#xia.i18n.cacheSeconds=720

#============================================================================
# Configure Quartz
#============================================================================
# true = no db save
xia.quartz.start=true
xia.quartz.logdb=true

#============================================================================
# Configure Solr
#============================================================================
spring.data.solr.host=http://localhost:8983/solr

#============================================================================
# Configure Redis
#============================================================================
#spring.redis.host=************
#spring.redis.port=6379
#spring.redis.database=0
#spring.redis.password=111111
#spring.redis.pool.max-idle=100
#spring.redis.pool.min-idle=0
#spring.redis.pool.max-active=50

#============================================================================
# Configure Flyway
#============================================================================
flyway.enabled=true
flyway.initOnMigrate=true
flyway.baselineVersion=2_51
flyway.outOfOrder=true
flyway.locations=cn.facilityone.shang.release.migration
flyway.table=release_flyway

#============================================================================
# Configure Logback
#============================================================================
logback.path=/home/<USER>/project/logs/facilityone
logback.stdout.enabled=true
logback.file.prefix=fone
logback.file.enabled=false
logback.file-warn.enabled=false
logback.file-error.enabled=false
logback.file-error.receivers=<EMAIL>
logback.file-email.enabled=false
logback.web.enable=false
logback.web.url-pattern=/logback
logback.socket.enabled=false
logback.socket.port=9902
logback.taillog.socket.chanel=taillog
logback.realtime.monitor.enabled=true

#============================================================================
# Configure Pay
#============================================================================
xia.pay.start=true
# SANDBOX | PRODUCT
xia.pay.environment=SANDBOX
xia.pay.domain=https://wechat.fmone.cn
xia.pay.ip=*************
xia.pay.title_prefix=\u8D39\u54F2\u7269\u4E1A\u670D\u52A1\u6536\u8D39
# sure the `custom` is primery key in our all custom system
xia.pay.custom=FONE
#validate amonut money
xia.pay.money=3.01

xia.pay.ali.start=true
xia.pay.ali.pid = 2088721451422945
xia.pay.ali.appid =2016080400168717
xia.pay.ali.alipay_public_key = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAumLzxk4EMchQ8m7lNBocXOv3xfaGioLtNNu9esAagHQU0C0RCQN/iMxdAOAt5bjzAAOuLpjyuGM7YqiFQi80POn0vKzMhq9MGmkXfT3/gLsKQCzOyyYqVOxwloMISwLKqpJpd/hi/W1H1MD51DwTFRkF2n916FIwnwjF48os3RdtLxfFWL93fFLMdftjlC5php6kJmorMSASZHi91xbjagtX/C4mQ+0QY61d19MEMt0MMekY/VN9k7pNW5x9XbBsBY3/1czj+ma8VnzO1HlH7Oe4QdbF74zJRRYBYtJ6UXlKmwcqj09OS+2VF3Bh1ltBf8ymujr2VccO88liFFGyKQIDAQAB

xia.pay.wx.start=true
xia.pay.wx.appid=wx9d7221cc936ad396
xia.pay.wx.api_secret=wx9d7221cc936ad396fmoneapisecret
xia.pay.wx.mch_id=1293179401
xia.pay.wx.sub_mch_id=
xia.pay.wx.cert_path=classpath:pay/wxpay/apiclient_cert.p12

#operate log
shang.system.operate.notelog=true

shang.patrol.task.hot.data.months=1
#icc report job
icc.report.job.start=false

#fineReport
shang.fineReport.url=http://************:8075/WebReport

shang.rapid.implementation=true

#webservice
soa.webservice.enabled=true
soa.webservice.url=http://localhost:8080/web_cxf/ylservice/webcxf?wsdl

shang.system.iosProjectId=10

#fehseal
fehseal.api.enable=true
fehseal.api.prefixUrl=https://test-soa.fehorizon.com/
fehseal.api.relatedProject=11
fehseal.api.seedlingProjectId=122
fehseal.api.seedlingBuildingId=501
fehseal.api.key=14065666-4b48-46cf-ac51-68e6a0dde2ca
fehseal.api.appId=0BF83831EA4A1FB853E6D38FF5D43737
fehseal.api.ydhxgcProjectId=11

space.api.shangDomain=http://fehorizon-demo.wuxi.fmone.cn
space.api.spaceUrl=http://fehorizon.wuxi.fmone.cn/f1-space
space.api.syncUsersUrl=${space.api.spaceUrl}/api/shang/users/list
space.api.bindingProjectsUrl=${space.api.spaceUrl}/api/shang/project/bindingIds
space.api.clientId=20220420
space.api.clientSecret=cf66aebe24ebbb898d90109b7fb3845f
space.api.defaultProjectIds=1,10
space.api.userKey=cd54dcdae1b191477022548b47139b47

coe.api.coeUrl=http://*************:21001/f1-coe
#coe.api.coeUrl=http://boyue-fm.fehorizon.com/f1-coe

camera.fusingTime=5
camera.rtmpDomain=rtmp://*************:1935/hls
camera.hlsDomain=http://fehorizon.wuxi.fmone.cn/hls
camera.emptyPacketsCount=0
camera.grabber.rtsp_transport=udp
camera.grabber.stimeout=2000000
camera.recorder.tune=zerolatency
camera.recorder.preset=ultrafast
camera.recorder.crf=28

billboards.projectId=122
#billboards.referer=

# ???????
mp.repair.projectId=11
mp.repair.wxAppId=wxa7637a3fa5f13121
mp.repair.wxTemplateId=W2MLDEA8RPdJXwK-0Pjkdr8lQIXrpWdjxdo1id4kSy8
mp.repair.jzAppId=jeezgzycwykdxwf
mp.repair.jzAppKey=6cb8347a41cf42a47c83f4b9423cd52f
mp.repair.jzUrl=https://m.jeez.cn/openapi/api/message/sendwxmsg