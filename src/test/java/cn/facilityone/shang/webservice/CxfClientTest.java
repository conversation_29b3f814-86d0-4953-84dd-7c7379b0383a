/*
 * Copyright 2006-2020 FacilityONE Inc. All Rights Reserved
 *
 * 注意：
 * 本软件内容仅限于费哲软件内部传阅，禁止外泄以及用于其他商业目的
 * 费哲软件(FacilityONE) : www.facilityone.cn
 */
package cn.facilityone.shang.webservice;

import cn.hutool.json.JSONUtil;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;

/**
 * description goes here.
 *
 * <AUTHOR>
 * @date 2020/7/3 10:53
 * @since 1.0
 **/
public class CxfClientTest {

    public static void main(String[] args) {

        // 创建动态客户端
        JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
        Client client = dcf.createClient("http://localhost:8080/web_cxf/ylservice/webcxf?wsdl");
        // 需要密码的情况需要加上用户名和密码
        // client.getOutInterceptors().add(new ClientLoginInterceptor(USER_NAME, PASS_WORD));
        Object[] objects = new Object[0];
        try {
            // invoke("方法名",参数1,参数2,参数3....);
            Object[] argss = new Object[3];
            argss[0] = "1";
            argss[1] = "2";
            argss[2] = "3";
            objects = client.invoke("sendMsg", argss);
//            System.out.println("返回数据:" + ((Result)objects[0]).getStatus());
            System.out.println("返回数据:" + JSONUtil.parseObj(objects[0]).toString());
            System.out.println("返回数据:" + JSONUtil.parseObj(objects[0]).get("status"));
        } catch (java.lang.Exception e) {
            e.printStackTrace();
        }


    }

}
