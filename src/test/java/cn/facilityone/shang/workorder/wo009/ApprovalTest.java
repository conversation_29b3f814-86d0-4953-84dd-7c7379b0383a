package cn.facilityone.shang.workorder.wo009;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.workorder.wo009.service.ApprovalService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: wayne.fu
 * @Date: 6/23/2015
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
public class ApprovalTest {

    @Autowired
    private ApprovalService approvalService;

    @Test
    public void testFindApprovalForLoginUser() {
        DataTableRequest request = new DataTableRequest();
        request.setPageNumber(0);
        request.setPageSize(10);

        Page result = approvalService.findForLoginUser(request);
        org.junit.Assert.assertNotNull(result.getContent());
    }

}
