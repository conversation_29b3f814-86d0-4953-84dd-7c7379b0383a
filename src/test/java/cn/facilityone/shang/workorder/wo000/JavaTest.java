package cn.facilityone.shang.workorder.wo000;

import cn.facilityone.shang.entity.inventory.MaterialBatch;
import com.google.common.base.Function;
import com.google.common.collect.Ordering;
import org.joda.time.DateTime;
import org.junit.Test;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

import static java.awt.RenderingHints.VALUE_ANTIALIAS_ON;

/**
 * @Author: wayne.fu
 * @Date: 6/25/2015
 */
public class JavaTest {

    @Test
    public void test() {
        BigDecimal b1 = new BigDecimal("123.0");
        BigDecimal b2 = new BigDecimal("123");
        System.out.println(b1 == b2);
        System.out.println(b1.compareTo(b2));
        System.out.println(b1.equals(b2));
        System.out.println(b1.scale());
        System.out.println(b2.scale());

        String[] a = {};
        int ab = a.length;

//        this.add("1111",null,"eweafwefaw");
    }

    private void add(String... strings) {
        List<String> stringList = Arrays.asList(strings);
        for (String string : stringList) {
            System.out.println(string);
        }
    }

    @Test
    public void testSort() {
        List<Long> list = new ArrayList<>();
        list.add(1L);
        list.add(4L);
        list.add(7L);
        list.add(2L);
        list.add(5L);

        Collections.sort(list, new Comparator<Long>() {
            @Override
            public int compare(Long o1, Long o2) {
                return o1.compareTo(o2);
            }
        });

        for (Long l : list) {
            System.out.println(l);
        }
    }


    @Test
    public void testException() {
//        Integer.parseInt(null);
//        Double.parseDouble(null);
        Set<String> strings = Collections.emptySet();
        strings = new HashSet<>();
        strings.add(null);
    }


    @Test
    public void sortTest() {
        MaterialBatch b1 = new MaterialBatch();
        b1.setDueDate(new Date());
        MaterialBatch b2 = new MaterialBatch();
        b2.setDueDate(null);
        MaterialBatch b3 = new MaterialBatch();
        Date d1 = DateTime.now().minus(1000000000).toDate();
        b3.setDueDate(d1);
        MaterialBatch b4 = new MaterialBatch();
        Date d2 = DateTime.now().withDurationAdded(10000000, 5).toDate();
        b4.setDueDate(d2);

        List<MaterialBatch> materialBatches = new ArrayList<>();
        materialBatches.add(b1);
        materialBatches.add(b2);
        materialBatches.add(b3);
        materialBatches.add(b4);

        Collections.sort(materialBatches, new Comparator<MaterialBatch>() {
            @Override
            public int compare(MaterialBatch o1, MaterialBatch o2) {
                Date o1Date = o1.getDueDate();
                Date o2Date = o2.getDueDate();
                if (o1Date == null && o2Date == null) {
                    return 0;
                }

                if (o1Date != null) {
                    if (o2Date == null) {
                        return 1;
                    } else {
                        return o1Date.compareTo(o2Date);
                    }
                } else {
                    return -1;
                }
            }
        });

        for (MaterialBatch batch : materialBatches) {
            System.out.println(batch.getDueDate());
        }
    }


    @Test
    public void testSortedByValue() {
        Map<Long, Integer> value = new HashMap<>();
        value.put(1L, 12);
        value.put(2L, 31);
        value.put(3L, 12);
        value.put(4L, 32);
        value.put(5L, 34);

        Ordering<Map.Entry<Long, ? extends Number>> entryOrdering = Ordering.from(new Comparator<Map.Entry<Long, ? extends Number>>() {
            @Override
            public int compare(Map.Entry<Long, ? extends Number> o1, Map.Entry<Long, ? extends Number> o2) {
                return o1.getValue().doubleValue() >= o2.getValue().doubleValue() ? 1 : -1;
            }
        }).onResultOf(new Function<Map.Entry<Long, ? extends Number>, Map.Entry<Long, ? extends Number>>() {

            @Override
            public Map.Entry<Long, ? extends Number> apply(Map.Entry<Long, ? extends Number> longEntry) {
                return longEntry;
            }
        });
// Desired entries in desired order.  Put them in an ImmutableMap in this order.
//
//        for (Map.Entry<Long, Integer> entry :
//                entryOrdering.sortedCopy(value.entrySet())) {
//            builder.put(entry.getKey(), entry.getValue());
//        }
//        System.out.println(builder.build());
    }


    @Test
    public void addText() {
        try {
            BufferedImage image = ImageIO.read(new File("F:/test/1.png"));
            Graphics2D g = image.createGraphics();
            g.setFont(g.getFont().deriveFont(30f));
            g.setFont(g.getFont().deriveFont(Font.BOLD));
            g.setColor(Color.black);
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, VALUE_ANTIALIAS_ON);
            g.drawString("你好", 100, 385);
            g.dispose();
            ImageIO.write(image, "png", new File("F:/test/test.png"));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void changeSize() {
        BufferedImage bi = null;
        try {
            Image i = ImageIO.read(new File("F:/test/1.png"));//path to image
            bi = new BufferedImage(400, 500, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = (Graphics2D) bi.createGraphics();
            g2d.addRenderingHints(new RenderingHints(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY));
            g2d.setColor(Color.white);
            g2d.fillRect(0, 0, 400, 500);
            g2d.drawImage(i, 0, 0, 400, 400, null);
            g2d.setColor(Color.black);
            g2d.setFont(g2d.getFont().deriveFont(30f));
            g2d.setFont(g2d.getFont().deriveFont(Font.BOLD));
            g2d.addRenderingHints(new RenderingHints(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON));
            g2d.drawString("你好，世界", 50, 480);
            ImageIO.write(bi, "png", new File("F:/test/test1.png"));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testAllChildren() {
        Map<Long, Set<Long>> childrenMap = new HashMap<>();
        Set<Long> ids = new HashSet<>();
        ids.add(2l);
        ids.add(3l);
        childrenMap.put(1l, ids);
        Set<Long> ids2 = new HashSet<>();
        ids2.add(21l);
        ids2.add(22l);
        childrenMap.put(2l, ids2);
        Set<Long> ids3 = new HashSet<>();
        ids3.add(31l);
        ids3.add(32l);
        childrenMap.put(3l, ids3);
        Set<Long> ids4 = new HashSet<>();
        ids4.add(211l);
        ids4.add(212l);
        childrenMap.put(21l, ids4);

        Set<Long> allChildren = childrenMap.get(1l);
        Set<Long> all = getAllChildren(childrenMap, allChildren);
        for (Long id : all) {
            System.out.println(id);
        }

    }

    private Set<Long> getAllChildren(Map<Long, Set<Long>> childrenMap, Set<Long> allChildren) {
        Set<Long> ids = new HashSet<>();
        ids.addAll(allChildren);
        for (Long id : allChildren) {
            if (childrenMap.containsKey(id)) {
                ids.addAll(getAllChildren(childrenMap, childrenMap.get(id)));
            }
        }
        return ids;
    }

    @Test
    public void testHour() {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse("2015-09-11 14:35:00");
            Date date1 = format.parse("2015-09-11 16:59:00");
            double value = Math.round((date1.getTime() - date.getTime()) / (6.0 * 60 * 1000)) / 10.0;
            System.out.println(value);
        } catch (ParseException e) {
            e.printStackTrace();
        }


    }

    @Test
    public void test1(){
        String s = "123.png";
        System.out.println(s.substring(0,s.indexOf("png")-1));
    }

    @Test
    public void test2(){
        String s = ",1,2,2332,";
        String[] result = s.split(",");
        for (String str : result){
            System.out.println(str);
        }
    }

    @Test
    public void test221(){
        int b = 10;
        System.out.println(test123(b));
    }

    private int test123(int b) {

        try {
            System.out.println("1");
            b+=10;
            System.out.println("2");
            return b;
        }catch (Exception e){}finally {
            System.out.println("3");
            b+=10;
            System.out.println("4");
            return b;
        }
    }
}


 class Multiply {
    public static Double multiply(Double a, Double b) {
        if(a == null || b==null){
            return 0d;}
        return a * b;
    }
}
