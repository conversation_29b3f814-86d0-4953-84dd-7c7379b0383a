package cn.facilityone.shang.workorder.wo000;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.common.repository.WorkOrderEquipmentRepository;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.repository.XiaFileRepository;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.organize.Floor;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderEquipment;
import cn.facilityone.shang.entity.workorder.WorkOrderTool;
import cn.facilityone.shang.organize.org003.service.OrganizationService;
import cn.facilityone.shang.organize.org004.repository.FloorRepository;
import cn.facilityone.shang.servicecenter.service001.service.RequirementCenterService;
import cn.facilityone.shang.workorder.common.repository.PriorityRepository;
import cn.facilityone.shang.workorder.common.repository.ServiceTypeRepository;
import cn.facilityone.shang.workorder.common.repository.WorkOrderToolRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 6/9/2015
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
public class WorkOrderTest {

    @Autowired
    private WorkOrderRepository workOrderRepository;

    @Autowired
    private WorkOrderEquipmentRepository workOrderEquipmentRepository;

    @Autowired
    private WorkOrderToolRepository workOrderToolRepository;
    @Autowired
    private RequirementCenterService workOrderService;
    @Autowired
    private PriorityRepository priorityRepository;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private FloorRepository floorRepository;
    @Autowired
    private ServiceTypeRepository serviceTypeRepository;
    @Autowired
    private XiaFileRepository xiaFileRepository;

    @Test
    public void testDeleteEquipment() {

        WorkOrder workOrder = workOrderRepository.findOne(606L);
        Assert.assertNotNull(workOrder);

        workOrder.setWorkOrderEquipments(null);
        workOrderRepository.save(workOrder);

        List<WorkOrderEquipment> equipmentList = workOrderEquipmentRepository.findByWorkOrderId(606L);
        Assert.assertFalse("is not empty----1", equipmentList == null || equipmentList.isEmpty());
        workOrderEquipmentRepository.delete(equipmentList);
        Assert.assertTrue("is not empty----2", equipmentList == null || equipmentList.isEmpty());
    }


    @Test
    public void testDeleteTools() {

        WorkOrder workOrder = workOrderRepository.findOne(606L);
        Assert.assertNotNull(workOrder);

        workOrder.setWorkOrderTools(null);
        workOrderRepository.save(workOrder);

        List<WorkOrderTool> tools = workOrderToolRepository.findByWorkOrderId(606L);
        Assert.assertFalse("is not empty----1", tools == null || tools.isEmpty());
        workOrderToolRepository.delete(tools);
        Assert.assertTrue("is not empty----2", tools == null || tools.isEmpty());
    }

    @Test
    public void createWorkOrderTest() {
        WorkOrder workOrder = new WorkOrder();

        workOrder.setType(WorkOrder.WorkOrderType.SELFCHECK);
        workOrder.setServiceType(serviceTypeRepository.findOne(2L));
        workOrder.setPriority(priorityRepository.findOne(9L));
        workOrder.setRoom(null);
        Floor floor = floorRepository.findOne(601L);
        workOrder.setFloor(floor);
        floor.getBuilding().getId();
        workOrder.setBuilding(floor.getBuilding());
        workOrder.setSource(WorkOrder.SourceType.APP);
        workOrder.setStatus(WorkOrder.WorkOrderStatus.CREATE);
        List<XiaFile> files = new ArrayList<>();
        files.add(xiaFileRepository.findOne(6008L));

        workOrder.setRequestName("TEST-AGAIN--4");

        workOrder.setOrganization(organizationService.findOne(2101L));
        WorkOrder workOrderCreated = workOrderService.createWorkOrder(workOrder, "admin");
        Assert.assertNotNull(workOrderCreated);
    }

    @Test
    public void testLoadEntireWorkOrder(){
        WorkOrder order = workOrderService.findEntireOne(608L);
        Assert.assertNotNull(order);
    }


}
