package cn.facilityone.shang.workorder.wo000;

import sun.applet.Main;

/**
 * @Author: wayne.fu
 * @Date: 9/22/2015
 */
public class ThreadTest {

    public static void  main(String[] args){
        Buffer buffer = new Buffer();
        Writer writer = new Writer(buffer);
        Reader reader = new Reader(buffer);

        writer.start();
        reader.start();

        new Thread(){
            @Override
            public void run() {
                long time = System.currentTimeMillis();
                for (;;){
                    if(System.currentTimeMillis()-time > 5000){
                        System.out.println("not wait");
                        break;
                    }
                }
            }
        }.start();
    }

}

class Reader extends Thread{

    Buffer buffer;

    public Reader(Buffer buffer) {
        this.buffer = buffer;
    }

    @Override
    public void run() {
        buffer.read();
    }
}

class Writer extends Thread{

    Buffer buffer;

    public Writer(Buffer buffer) {
        this.buffer = buffer;
    }

    @Override
    public void run() {
        buffer.write();
    }
}



class Buffer {
    Object lock;

    public Buffer() {
        lock = this;
    }

    public void write() {
        synchronized (lock) {
            System.out.println("start writing..");
            long time = System.currentTimeMillis();
            for (;;) {
              if(System.currentTimeMillis()-time > 1000){
                  break;
              }
            }
            System.out.println("write end");

        }
    }

    public void read() {
        synchronized (lock) {
            System.out.println("read end");
        }
    }
}