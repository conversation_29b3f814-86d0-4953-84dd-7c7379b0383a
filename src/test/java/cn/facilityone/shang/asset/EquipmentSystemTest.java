package cn.facilityone.shang.asset;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.ShangRepositoryTestBase;
import cn.facilityone.shang.asset.asset001.repository.EquipmentSystemRepository;
import cn.facilityone.shang.asset.asset001.service.EquipmentSystemService;
import cn.facilityone.shang.entity.asset.EquipmentSystem;
import cn.facilityone.shang.entity.organize.WorkTeam;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.IsNull.notNullValue;

/**
 * Created by charles.chen on 2015/5/21.
 */
public class EquipmentSystemTest extends ShangRepositoryTestBase {
    @Autowired
    private EquipmentSystemService equipmentSystemService;
    @Autowired
    private  EquipmentSystemRepository equipmentSystemRepository;

    @Test
    @DatabaseSetup("equipmentSystem.xml")
    public void deleteEquipmentSystemTest(){
        equipmentSystemService.delete(9686L);

    }

    @Test
    @ExpectedDatabase(value="equipmentSystem-expected.xml",table = "eq_sys", query = "select deleted, description, sort, sys_fullname, sys_id, sys_level, sys_name, version from eq_sys")
    public void expectedEquipmentSystemTest(){
        equipmentSystemRepository.findAll();

    }
}
