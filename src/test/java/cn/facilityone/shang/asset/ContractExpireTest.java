package cn.facilityone.shang.asset;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.facilityone.shang.ShangRepositoryTestBase;
import cn.facilityone.shang.asset.asset003.repository.ContractExpireRepository;
import cn.facilityone.shang.asset.asset003.service.ContractExpireService;
import cn.facilityone.shang.entity.asset.ContractExpire;

import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;

/**
 * Created by charles.chen on 2015/5/21.
 */
public class ContractExpireTest extends ShangRepositoryTestBase {
    @Autowired
    private ContractExpireService contractExpireService;
    @Autowired
    private ContractExpireRepository contractExpireRepository;

    @Test
    @DatabaseSetup("contractExpire.xml")
    public void testDeleteContractExpire(){
        List<ContractExpire> all = contractExpireRepository.findAll();
    }

    @Test
    @ExpectedDatabase(value="contractExpire-expected.xml",table = "contract_expire", query = "select contract_expire_id, deleted, description, name, ntype, version from contract_expire where deleted=0")
    public void expectedContractExpire(){
        List<ContractExpire> all = contractExpireRepository.findAll();
        //assertThat(all, is(notNullValue()));
    }

    @Test
    public void testJob(){
        //contractExpireService.noticeOncePerDayJob();
    }
}
