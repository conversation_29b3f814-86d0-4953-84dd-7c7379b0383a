package cn.facilityone.shang.asset;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.asset.asset003.service.ContractExpireService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

/**
 * Created by charles.chen on 2015/6/12.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class AssetJobTest {

    @Autowired
    private ContractExpireService contractExpireService;

    @Test
    public void testJob(){
        //contractExpireService.noticeOncePerDayJob();
    }
}
