package cn.facilityone.shang.asset;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Context;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.facilityone.shang.ShangRepositoryTestBase;
import cn.facilityone.shang.asset.asset001.service.EquipmentSystemService;
import cn.facilityone.shang.asset.asset002.repository.EquipmentRepository;
import cn.facilityone.shang.asset.asset002.service.EquipmentService;

import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;

/**
 * Created by charles.chen on 2015/5/21.
 */
public class EquipmentTest extends ShangRepositoryTestBase {
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    private EquipmentRepository equipmentRepository;
    @Autowired
    private EquipmentSystemService equipmentSystemService;
    @Context
    private HttpServletRequest request;

    @Test
    @DatabaseSetup("equipment.xml")
    public void deleteEquipmentSystemFromEquipmentTest(){
        equipmentService.delete(9688L);
    }

    @Test
    @ExpectedDatabase(value="equipment-expected.xml",table = "eq", query = "select asset_code, bim_id, brand, building_id, date_in_service, date_installed, date_manufactured, deleted, description, eq_code, eq_id, eq_name, floor_id, installer_id, manufacturer_id, model, provider_id, room_id, serial_num, site_id, sys_id, version, weight from eq where deleted=0")
    public void textExpectEquipmentSystemFromEquipment(){
        equipmentRepository.findAll();
    }


}
