package cn.facilityone.shang.common;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Whitelist;
import org.junit.Assert;
import org.junit.Test;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by aaron on 15/7/3.
 */
public class JsoupTest {

    @Test
    public void testParse() {
        Document doc = Jsoup.parse("{\"description\":\"</\",\"genWo\":\"false\",\"serviceType\":{\"fullName\":\"\",\"id\":\"\"},\"sort\":\"1\"}");
        String str = doc.text();
        System.out.println(str);
        String sb = Jsoup.parse("\"").text();
        String s = "\"";
        String html = "&amp &quot &reg &icy &hopf &icy; &hopf;";
        Document doc1 = Jsoup.parse(html);
        String s1 = Jsoup.clean(html, Whitelist.relaxed());

        System.out.println(doc1.text());
    }

    @Test
    public void testAddMonths() throws ParseException {
        Date date1 = DateUtils.parseDate("2016-01-31 11:00:00", "yyyy-MM-dd HH:mm:ss");
        Date date2 = DateUtils.addMonths(date1, 1);
        Date date3 = DateUtils.addMonths(date1, 2);
        Assert.assertEquals(date2, DateUtils.parseDate("2016-02-29 11:00:00", "yyyy-MM-dd HH:mm:ss"));
        Assert.assertEquals(date3, DateUtils.parseDate("2016-03-31 11:00:00", "yyyy-MM-dd HH:mm:ss"));
    }

    @Test
    public void testDateCeil() throws ParseException {
        Date date1 = DateUtils.parseDate("2016-02-27 11:00:00", "yyyy-MM-dd HH:mm:ss");
        Assert.assertEquals(DateUtils.addDays(DateUtils.ceiling(date1, Calendar.MONTH),-1), DateUtils.parseDate("2016-02-29 00:00:00", "yyyy-MM-dd HH:mm:ss"));
    }

    @Test
    public void testCommon(){
        String[] strs = {"1","2","3"};
        String[] res = ArrayUtils.addAll(strs, "4");
        Assert.assertEquals(4,res.length);
    }

    @Test
    public void testDate() throws ParseException {
        Date now = new Date();
        Date todayFloor = DateUtils.truncate(now, Calendar.DATE);
        Assert.assertEquals(todayFloor, DateUtils.parseDate("2015-09-14 00:00:00", "yyyy-MM-dd HH:mm:ss"));
        Date todayCeiling = DateUtils.ceiling(now, Calendar.DATE);
        System.out.println(todayCeiling);
        Assert.assertEquals(todayCeiling, DateUtils.parseDate("2015-09-14 23:59:59", "yyyy-MM-dd HH:mm:ss"));
    }

    @Test
    public void testCompare() throws ParseException {

        Date DATE00 = DateUtils.parseDate("2000-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        System.out.println(DATE00.getTime());

        System.out.println(DateFormatUtils.format(DATE00, "HH:mm:ss"));

    }
}
