package cn.facilityone.shang.common.service;

import cn.facilityone.shang.entity.common.Unit;
import org.junit.Assert;
import org.junit.Test;

import javax.measure.converter.UnitConverter;
import javax.measure.unit.NonSI;


/**
 * Created by aaron on 15/4/29.
 */
public class UnitServiceTest {

    // Length support: km,m,dm,cm,mm,µm,nm,fm; in,ft,yd,mi,nmi
    @Test
    public void testConvertLength() {

        // m -> km
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("m");

        Unit destUnit = new Unit();
        destUnit.setSymbol("km");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(10.0, converter.convert(10000), 0);

        // m -> dm
        destUnit.setSymbol("dm");
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(23.3, converter.convert(2.33), 1);


        // m -> fm,µm
        srcUnit.setSymbol("m");
        destUnit.setSymbol("µm");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000000, converter.convert(1), 0);

        // m -> mm
        srcUnit.setSymbol("m");
        destUnit.setSymbol("mm");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000.0, converter.convert(1), 0);

        // m -> cm
        srcUnit.setSymbol("m");
        destUnit.setSymbol("cm");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(100.0, converter.convert(1), 0);

        // m -> in 英寸
        srcUnit.setSymbol("m");
        destUnit.setSymbol("in");
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(39.3700787, converter.convert(1), 7);

        // m -> ft 英尺
        srcUnit.setSymbol("m");
        destUnit.setSymbol("ft");
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(3.2808399, converter.convert(1), 7);

        // m -> yd 码
        srcUnit.setSymbol("m");
        destUnit.setSymbol("yd");
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1.0936133, converter.convert(1), 7);

        // m -> mi 英里
        srcUnit.setSymbol("m");
        destUnit.setSymbol("mi");
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(6.2137119, converter.convert(10000), 7);

        // km -> nmi 海里
        srcUnit.setSymbol("km");
        destUnit.setSymbol("nmi");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(5.399568, converter.convert(10), 7);
    }

    //Area support: km²,ha,a,m²,dm²,cm²,mm²
    @Test
    public void testConvertArea() {
        // km² -> m²
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("km²");

        Unit destUnit = new Unit();
        destUnit.setSymbol("m²");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000000, converter.convert(1), 0);

        // km² -> ha 公顷
        srcUnit.setSymbol("km²");
        destUnit.setSymbol("ha");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(100, converter.convert(1), 0);

        // m² -> dm²
        srcUnit.setSymbol("m²");
        destUnit.setSymbol("dm²");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(100, converter.convert(1), 0);

        // m² -> cm²
        srcUnit.setSymbol("m²");
        destUnit.setSymbol("cm²");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(10000, converter.convert(1), 0);

        // m² -> mm²
        srcUnit.setSymbol("m²");
        destUnit.setSymbol("mm²");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000000, converter.convert(1), 0);


        // km² -> mi²
        srcUnit.setSymbol("km²");
        destUnit.setSymbol("mi²");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(0.3861022, converter.convert(1), 7);

        // km² -> a 亩
        srcUnit.setSymbol("km²");
        destUnit.setSymbol("a");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(10000, converter.convert(1), 7);

    }

    //Volume support:
    @Test
    public void testConvertVolume() {
        // m³ -> dm³
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("m³");

        Unit destUnit = new Unit();
        destUnit.setSymbol("dm³");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 0);

        // m³ -> cm³
        srcUnit.setSymbol("m³");
        destUnit.setSymbol("cm³");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000000, converter.convert(1), 0);

        // m³ -> cm³
        srcUnit.setSymbol("m³");
        destUnit.setSymbol("mm³");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000000000, converter.convert(1), 0);

        // m³ -> L
        srcUnit.setSymbol("m³");
        destUnit.setSymbol("L");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 0);

        // m³ -> dL
        srcUnit.setSymbol("m³");
        destUnit.setSymbol("dL");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(10000, converter.convert(1), 0);

        // m³ -> mL
        srcUnit.setSymbol("m³");
        destUnit.setSymbol("mL");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000000, converter.convert(1), 0);

        // m³ -> cL
        srcUnit.setSymbol("m³");
        destUnit.setSymbol("cL");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(100000, converter.convert(1), 0);

        // m³ -> ft³
        srcUnit.setSymbol("m³");
        destUnit.setSymbol("ft³");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(35.3147248, converter.convert(1), 7);

        // m³ -> gal 美制加仑
        srcUnit.setSymbol("m³");
        destUnit.setSymbol("gal");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(264.1720524, converter.convert(1), 7);


        // m³ -> gal 英制加仑
        srcUnit.setSymbol("m³");
        destUnit.setSymbol("gallon_uk");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(219.9691573, converter.convert(1), 7);

        // m³ -> gal 英制加仑
        srcUnit.setSymbol("m³");
        destUnit.setSymbol("gallon_uk");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(219.9691573, converter.convert(1), 7);

        srcUnit.setSymbol("m³");
        destUnit.setSymbol("oz");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(33814.0227018, converter.convert(1), 7);

        srcUnit.setSymbol("m³");
        destUnit.setSymbol("oz_uk");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(35195.0797279, converter.convert(1), 7);

    }


    //Volume support:
    @Test
    public void testConvertMass() {
        // kg-> g
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("kg");

        Unit destUnit = new Unit();
        destUnit.setSymbol("g");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 0);

        // kg-> mg
        srcUnit.setSymbol("kg");
        destUnit.setSymbol("mg");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000000, converter.convert(1), 7);

        // kg-> lb
        srcUnit.setSymbol("kg");
        destUnit.setSymbol("lb");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(2.2046226, converter.convert(1), 7);

        // lb -> oz NonSI.OUNCE
        srcUnit.setSymbol("lb");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(NonSI.OUNCE);
        Assert.assertEquals(16, converter.convert(1), 0);

    }

    //Temperature support:
    @Test
    public void testConvertTemperature() {
        // °C-> °R
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("°C");

        Unit destUnit = new Unit();
        destUnit.setSymbol("°R");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(493.47, converter.convert(1), 2);

        // °C-> °F
        srcUnit.setSymbol("°C");
        destUnit.setSymbol("°F");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(33.8, converter.convert(1), 2);

        // °C-> K
        srcUnit.setSymbol("°C");
        destUnit.setSymbol("K");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(274.15, converter.convert(1), 2);

    }

    //Pressure support:
    @Test
    public void testConvertPressure() {
        // atm-> °R
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("atm");

        Unit destUnit = new Unit();
        destUnit.setSymbol("Pa");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(101325, converter.convert(1), 0);

        // atm-> kPa
        srcUnit.setSymbol("atm");
        destUnit.setSymbol("kPa");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(101.325, converter.convert(1), 2);

        // hPa-> kPa
        srcUnit.setSymbol("hPa");
        destUnit.setSymbol("kPa");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(0.1, converter.convert(1), 2);


        // atm-> mmHg
        srcUnit.setSymbol("atm");
        destUnit.setSymbol("mmHg");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(760, converter.convert(1), 1);

        // atm-> inHg
        srcUnit.setSymbol("atm");
        destUnit.setSymbol("inHg");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(29.9212598, converter.convert(1), 7);

        // atm-> bar
        srcUnit.setSymbol("atm");
        destUnit.setSymbol("bar");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1.01325, converter.convert(1), 7);
    }

    //Power support:
    @Test
    public void testConvertPower() {
        // W -> KW
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("W");

        Unit destUnit = new Unit();
        destUnit.setSymbol("kW");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1, converter.convert(1000), 0);

        // kW-> hp
        srcUnit.setSymbol("kW");
        destUnit.setSymbol("hp");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1.3410221, converter.convert(1), 7);

        // kW -> J/s
        srcUnit.setSymbol("kW");
        destUnit.setSymbol("J/s");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 1);

        // kW-> N·m/s
        srcUnit.setSymbol("kW");
        destUnit.setSymbol("N·m/s");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 1);

    }

    //Energy support:
    @Test
    public void testConvertEnergy() {
        // W -> KW
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("J");

        Unit destUnit = new Unit();
        destUnit.setSymbol("J");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1, converter.convert(1), 3);

        // kW -> J/s
        srcUnit.setSymbol("J");
        destUnit.setSymbol("hp·h");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(0.3725061, converter.convert(1000000), 7);

        // kW-> hp
        srcUnit.setSymbol("J");
        destUnit.setSymbol("kW·h");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(0.2777778, converter.convert(1000000), 7);


//        // kW-> N·m/s
//        srcUnit.setSymbol("J");
//        destUnit.setSymbol("cal");
//        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
//        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
//        converter = (UnitConverter) su.getConverterTo(du);
//        Assert.assertEquals(238900, converter.convert(1000000), 1);
//
//        // kW-> N·m/s
//        srcUnit.setSymbol("J");
//        destUnit.setSymbol("kcal");
//        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
//        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
//        converter = (UnitConverter) su.getConverterTo(du);
//        Assert.assertEquals(238.9, converter.convert(1000000), 1);

        // kW-> N·m/s
//        srcUnit.setSymbol("J");
//        destUnit.setSymbol("BTU");
//        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
//        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
//        converter = (UnitConverter) su.getConverterTo(du);
//        Assert.assertEquals(947.8171203, converter.convert(1000000), 1);

        // kW-> N·m/s
//        srcUnit.setSymbol("J");
//        destUnit.setSymbol("ft·lb");
//        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
//        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
//        converter = (UnitConverter) su.getConverterTo(du);
//        Assert.assertEquals(737600.0000011, converter.convert(1000000), 1);
    }

    //Force support:
    @Test
    public void testConvertForce() {
        // W -> KW
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("N");

        Unit destUnit = new Unit();
        destUnit.setSymbol("kN");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1, converter.convert(1000), 3);

        srcUnit.setSymbol("N");
        destUnit.setSymbol("kgf");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(101.971621, converter.convert(1000), 7);

        srcUnit.setSymbol("N");
        destUnit.setSymbol("lbf");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(224.808943, converter.convert(1000), 7);

    }

    //Speed support:
    @Test
    public void testConvertSpeed() {
        // W -> KW
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("m/s");

        Unit destUnit = new Unit();
        destUnit.setSymbol("km/s");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1, converter.convert(1000), 0);

        srcUnit.setSymbol("m/s");
        destUnit.setSymbol("km/h");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(3.6, converter.convert(1), 7);

        srcUnit.setSymbol("m/s");
        destUnit.setSymbol("in/s");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(39.370079, converter.convert(1), 7);

        srcUnit.setSymbol("m/s");
        destUnit.setSymbol("mi/h");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(2.236936, converter.convert(1), 7);

    }

    //ElectricPotential support:
    @Test
    public void testConvertElectricPotential() {
        // W -> KW
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("MV");

        Unit destUnit = new Unit();
        destUnit.setSymbol("V");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000000, converter.convert(1), 0);

        srcUnit.setSymbol("kV");
        destUnit.setSymbol("V");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 7);

        srcUnit.setSymbol("V");
        destUnit.setSymbol("mV");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 7);

        srcUnit.setSymbol("mV");
        destUnit.setSymbol("µV");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 7);

    }

    //ElectricCurrent support:
    @Test
    public void testConvertElectricCurrent() {
        // W -> KW
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("MA");

        Unit destUnit = new Unit();
        destUnit.setSymbol("A");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000000, converter.convert(1), 0);

        srcUnit.setSymbol("kA");
        destUnit.setSymbol("A");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 7);

        srcUnit.setSymbol("A");
        destUnit.setSymbol("mA");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 7);

        srcUnit.setSymbol("mA");
        destUnit.setSymbol("µA");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 7);

    }

    //Frequency support:
    @Test
    public void testConvertFrequency() {
        // W -> KW
        Unit srcUnit = new Unit();
        srcUnit.setSymbol("GHz");

        Unit destUnit = new Unit();
        destUnit.setSymbol("MHz");

        javax.measure.unit.Unit su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        javax.measure.unit.Unit du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        UnitConverter converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 0);

        srcUnit.setSymbol("MHz");
        destUnit.setSymbol("kHz");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 7);

        srcUnit.setSymbol("kHz");
        destUnit.setSymbol("Hz");
        su = javax.measure.unit.Unit.valueOf(srcUnit.getSymbol());
        du = javax.measure.unit.Unit.valueOf(destUnit.getSymbol());
        converter = (UnitConverter) su.getConverterTo(du);
        Assert.assertEquals(1000, converter.convert(1), 7);


    }
}
