package cn.facilityone.shang.common;

import java.io.File;

import cn.facilityone.xia.message.common.EmailSenderTool;

public class MailTest4 {

    public static void main(String[] args){
        String customer = "andy";
        String[] users  = {"<EMAIL>"};
        EmailSenderTool etool = new EmailSenderTool();
        try {
            etool.getSender("smtp.qq.com", 465, "<EMAIL>", "费哲-DevOps", "ubdshsnzjosejfcd")
            .send(users, "【LOG-ERROR】-"+customer, customer+" error log", new File("C:\\Users\\<USER>\\logs\\facilityone\\fone-2017-12-27.zip"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
