package cn.facilityone.shang.common.repository;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;

import cn.facilityone.shang.ShangRepositoryTestBase;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.IsNull.notNullValue;

public class TestEmployeeRepository extends ShangRepositoryTestBase{

    @Autowired
    EmployeeRepository employeeRepository;
    

    /**
     * type 注意Database的type类型，默认CLEAN_INSERT
     */
    @Test
    @DatabaseSetup("employee.xml")
    public void searchTest() {
        List<Employee> all = employeeRepository.findAll();
        assertThat(all, is(notNullValue()));
        assertThat(all.size(), is(2));
    }
    
    /**
     * 还有些参数在ExpectedDatabase 
     */
    @Test
    @ExpectedDatabase(value="employee-expected.xml",table = "em", query = "select em_id,version,em_name,number,phone,deleted,type from em where deleted=0")
    public void expectedTest() {
        List<Employee> all = employeeRepository.findAll();
        assertThat(all, is(notNullValue()));
        assertThat(all.size(), is(2));
        employeeRepository.delete(all.get(0));
    }
}
