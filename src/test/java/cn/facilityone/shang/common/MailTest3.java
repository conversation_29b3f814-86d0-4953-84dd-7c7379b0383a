package cn.facilityone.shang.common;

import java.util.Properties;

import javax.mail.Address;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

public class MailTest3 {
    
    //<EMAIL>
    //Dulwich123
    static String email = "<EMAIL>";
    static String pwd = "xU48433!";

    public static void main(String[] args) throws MessagingException {  
        Properties props = new Properties();  
        props.setProperty("mail.debug", "true");  
        props.setProperty("mail.smtp.auth", "true");  
        props.setProperty("mail.smtp.starttls.enable", "true");
          
        // 协议名称设置为smtps，会使用SSL  
        props.setProperty("mail.transport.protocol", "smtp");  
          
        Session session = Session.getInstance(props);  
          
        Message msg = new MimeMessage(session);  
        msg.setText("你好吗？");  
        msg.setSubject("Hello");
        msg.setFrom(new InternetAddress(email));  
          
        Transport transport = session.getTransport();  
        transport.connect("smtp.office365.com",587, email, pwd);  
        transport.sendMessage(msg, new Address[] {new InternetAddress("<EMAIL>")});  
        transport.close();  
    }  
}
