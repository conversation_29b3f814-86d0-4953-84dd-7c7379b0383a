package cn.facilityone.shang.common.entity;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.entity.servercenter.SatisfactionDegree;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;

/**
 * Created by aaron on 15/6/14.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class BatchSaveTest {

    @Autowired
    private EntityManagerFactory entityManagerFactory;

    private EntityManager em;

    @Test
    @XiaTransactional(readOnly = false)
    public void testSaveInBatch() {
        em = entityManagerFactory.createEntityManager();
        for (int i = 1; i < 20; i++) {
            SatisfactionDegree satisfactionDegree = new SatisfactionDegree();
            satisfactionDegree.setDegree("Degree:" + i);
            satisfactionDegree.setValue(i);
            em.persist(satisfactionDegree);
            if (i % 5 == 0) {
                em.flush();
                em.clear();
            }
        }
    }
}
