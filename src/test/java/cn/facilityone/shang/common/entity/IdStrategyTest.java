package cn.facilityone.shang.common.entity;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.entity.organize.Building;
import cn.facilityone.shang.entity.organize.Floor;
import cn.facilityone.shang.organize.org004.repository.BuildingRepository;
import cn.facilityone.shang.organize.org004.repository.FloorRepository;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

/**
 * Created by aaron on 15/3/17.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class IdStrategyTest {

    @Autowired
    private BuildingRepository buildingRepository;

    @Autowired
    private FloorRepository floorRepository;

    @Test
    public void testSave() {
        for (int i = 0; i < 5; i++) {
            Building building = new Building();
            building.setName("Building 1");
            building.setCode("B1");
            building = buildingRepository.save(building);
        }
        Building building = new Building();
        building.setName("Building 1");
        building.setCode("B1");
        building = buildingRepository.save(building);
        for (int i = 0; i < 5; i++) {
            Floor floor = new Floor();
            floor.setName("Floor 1");
            floor.setCode("F1");
            floor.setBuilding(building);
            floorRepository.save(floor);
        }
    }
}
