package cn.facilityone.shang.common.entity;

/**
 * Created by aaron on 15/4/3.
 */

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.asset.asset002.repository.EquipmentRepository;
import cn.facilityone.shang.common.repository.*;
import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.patrol.Patrol;
import cn.facilityone.shang.entity.patrol.PatrolSpot;
import cn.facilityone.shang.entity.patrol.Spot;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderEquipment;
import cn.facilityone.shang.entity.workorder.WorkOrderEquipmentId;
import cn.facilityone.shang.patrol.common.repository.PatrolRepository;
import cn.facilityone.shang.patrol.common.repository.PatrolSpotRepository;
import cn.facilityone.shang.patrol.common.repository.SpotRepository;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class CompositeIdTest {

    @Autowired
    private EquipmentRepository equipmentRepository;

    @Autowired
    private WorkOrderRepository workOrderRepository;

    @Autowired
    private WorkOrderEquipmentRepository workOrderEquipmentRepository;

    @Autowired
    private PatrolRepository patrolRepository;

    @Autowired
    private SpotRepository spotRepository;

    @Autowired
    private PatrolSpotRepository patrolSpotRepository;

    @Test
    public void testSaveWorkOrderEquipment() {
        WorkOrder workOrder = new WorkOrder();
        workOrder.setCode("CM01504020001");
        workOrder.setSource(WorkOrder.SourceType.APP);
        workOrder.setStatus(WorkOrder.WorkOrderStatus.CREATE);
        workOrder = workOrderRepository.save(workOrder);

        Equipment equipment = new Equipment();
        equipment.setCode("SA0010020001");
        equipment.setName("空调箱");
        equipment = equipmentRepository.save(equipment);

        WorkOrderEquipment orderEquipment = new WorkOrderEquipment();
        orderEquipment.setEquipment(equipment);
        orderEquipment.setWorkOrder(workOrder);
        workOrderEquipmentRepository.save(orderEquipment);
    }

    @Test
    public void testDelete(){
        WorkOrderEquipmentId workOrderEquipmentId = new WorkOrderEquipmentId();
        workOrderEquipmentId.setEquipmentId(801L);
        workOrderEquipmentId.setWorkOrderId(801L);
        workOrderEquipmentRepository.delete(workOrderEquipmentId);
    }

    @Test
    @XiaTransactional(readOnly = false)
    public void testUpdate(){
        WorkOrderEquipmentId workOrderEquipmentId = new WorkOrderEquipmentId();
        workOrderEquipmentId.setEquipmentId(1001L);
        workOrderEquipmentId.setWorkOrderId(1001L);
        WorkOrderEquipment workOrderEquipment = workOrderEquipmentRepository.getOne(workOrderEquipmentId);
        workOrderEquipment.setFailureDesc("XXXX");
        workOrderEquipmentRepository.save(workOrderEquipment);
    }

    @Test
    public void testSavePatrolSpot() {
        Patrol patrol = new Patrol();
        patrol.setName("巡检计划");
        patrol = patrolRepository.save(patrol);

        Spot spot = new Spot();
        spot.setName("强电间");
        spot = spotRepository.save(spot);

        PatrolSpot patrolSpot = new PatrolSpot();
        //patrolSpot.setSpotJob(spot);
        patrolSpot.setPatrol(patrol);
        patrolSpotRepository.save(patrolSpot);
    }
}
