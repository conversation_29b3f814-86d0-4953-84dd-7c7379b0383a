package cn.facilityone.shang.common.entity;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.common.repository.AttachmentRepository;
import cn.facilityone.shang.common.repository.XiaFileRepository;
import cn.facilityone.shang.common.repository.PictureRepository;
import cn.facilityone.shang.entity.common.Attachment;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.common.Picture;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import java.util.List;

/**
 * Created by aaron on 15/3/19.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class DiscriminatorTest {

    @Autowired
    private PictureRepository pictureRepository;

    @Autowired
    private XiaFileRepository xiaFileRepository;

    @Autowired
    private AttachmentRepository attachmentRepository;

    @Test
    public void testSave() {
        Picture picture = new Picture();
        picture.setName("Photo");
        picture.setPath("/1/2/3");
        picture.setTableName("File");
        pictureRepository.save(picture);
        XiaFile xiaFile = new XiaFile();
        xiaFile.setName("File");
        xiaFile.setPath("/2/3/4");
        xiaFile.setTableName("File");
        xiaFileRepository.save(xiaFile);
        Attachment attachment = new Attachment();
        attachment.setName("Attachment");
        attachment.setPath("/2/3/4");
        attachment.setTableName("File");
        attachmentRepository.save(attachment);
    }

    @Test
    public void testSearch() {
        //显示file_type=PHOTO的数据
        //List<Picture> pictures = pictureRepository.findByTableName("File");
        //显示所有数据
        List<XiaFile> xiaFiles = xiaFileRepository.findAll();
        //显示file_type=ATTACHMENT的数据
        List<Attachment> attachments = attachmentRepository.findAll();
    }

    @Test
    public void testDelete() {
        Picture picture = new Picture();
        picture.setName("Photo1");
        picture.setPath("/1/2/3");
        picture.setTableName("File");
        picture = pictureRepository.save(picture);
        pictureRepository.delete(picture);
        picture = new Picture();
        picture.setName("Photo2");
        picture.setPath("/1/2/3");
        picture.setTableName("File");
        pictureRepository.save(picture);
        // File Repository can delete Photo
        xiaFileRepository.delete(picture);
    }
}
