package cn.facilityone.shang.common.entity;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.organize.WorkTeam;
import cn.facilityone.shang.organize.org001.repository.WorkTeamRepository;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by aaron on 15/3/17.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class ManyToManyTest {

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private WorkTeamRepository workTeamRepository;

    @Test
    public void testSave() {
        WorkTeam workTeam = new WorkTeam();
        workTeam.setName("工程组");
        workTeam = workTeamRepository.save(workTeam);

        List<WorkTeam> workTeamList = new ArrayList<>();
        workTeamList.add(workTeam);
        Employee employee = new Employee();
        employee.setName("Aaron");
        employee.setPhone("13817858105");
        employee.setWorkTeams(workTeamList);
        employee = employeeRepository.save(employee);
    }

    @Test
    public void testDelete() {
        testSave();
        Employee employee = employeeRepository.getByName("Aaron").get(0);
        //employeeRepository.delete(employee);
        employeeRepository.deleteHardly(employee);
    }

    @Test
    public void testUpdate() {
        testSave();
        WorkTeam workTeam = new WorkTeam();
        workTeam.setName("安全组");
        workTeam = workTeamRepository.save(workTeam);
        List<WorkTeam> workTeamList = new ArrayList<>();
        workTeamList.add(workTeam);

        Employee employee = employeeRepository.getByName("Aaron").get(0);
        employee.setWorkTeams(workTeamList);
        employeeRepository.save(employee);
    }

    @Test
    public void testSearch() {
        testSave();
        Employee employee = employeeRepository.getByName("Aaron").get(0);
        //Lazy Init
        employee.getWorkTeams();
    }
}
