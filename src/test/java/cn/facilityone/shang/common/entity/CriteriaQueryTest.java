package cn.facilityone.shang.common.entity;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.system.sys001.repository.SystemUserRepository;
import cn.facilityone.xia.security.model.User;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Created by aaron on 15/6/14.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class CriteriaQueryTest {

    @Autowired
    private EntityManagerFactory entityManagerFactory;

    @Autowired
    private SystemUserRepository systemUserRepository;

    private EntityManager em;

    @Test
    public void testQuery() {
        em = entityManagerFactory.createEntityManager();
        CriteriaBuilder cb = em.getCriteriaBuilder();

        //Entity User retrieve
        CriteriaQuery<User> query = cb.createQuery(User.class);
        Root<User> root = query.from(User.class);
        root.fetch("roles", JoinType.LEFT);//left join fetch
        query.where(cb.equal(root.get("activated"), 1));// where
        query.select(root);// select entity
        query.orderBy(cb.desc(root.get("userName"))); // sort
        TypedQuery<User> typedQuery = em.createQuery(query);
        typedQuery.setFirstResult(1);// pageable
        typedQuery.setMaxResults(10);// pageable
        List<User> users = typedQuery.getResultList();

        assertNotNull(users);
        assertEquals(10, users.size());

        //Entity A retrieve
        CriteriaQuery<A> queryA = cb.createQuery(A.class);
        Root<User> rootA = queryA.from(User.class);
        queryA.where(cb.equal(root.get("activated"), 1));// where
        queryA.multiselect(root.get("id"), root.get("userName"));// select Class A
        queryA.orderBy(cb.desc(root.get("userName"))); // sort
        TypedQuery<A> typedQueryA = em.createQuery(queryA);
        typedQueryA.setFirstResult(1);// pageable
        typedQueryA.setMaxResults(10);// pageable
        List<A> usersA = typedQueryA.getResultList();

        assertNotNull(usersA);
        assertEquals(10, usersA.size());

        //Page<User> usersList = systemUserRepository.findEntireUserInPage(new PageRequest(0, 10));
        //assertEquals(usersList.getContent().size(), users.size());
    }

    /**
     * Class A for receive data
     */
    public static class A {
        private long id;
        private String name;

        public A(long id, String name) {
            this.id = id;
            this.name = name;
        }
    }
}
