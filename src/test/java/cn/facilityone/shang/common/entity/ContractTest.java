package cn.facilityone.shang.common.entity;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.asset.asset002.repository.ContractMaintenanceRepository;
import cn.facilityone.shang.asset.asset002.repository.ContractWarrantyRepository;
import cn.facilityone.shang.asset.asset002.repository.VendorMaintenanceRepository;
import cn.facilityone.shang.asset.asset002.repository.VendorWarrantyRepository;
import cn.facilityone.shang.asset.asset002.repository.EquipmentRepository;
import cn.facilityone.shang.entity.asset.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by aaron on 15/5/4.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class ContractTest {

    @Autowired
    private ContractWarrantyRepository contractWarrantyRepository;

    @Autowired
    private ContractMaintenanceRepository contractMaintenanceRepository;

    @Autowired
    private VendorWarrantyRepository vendorWarrantyRepository;

    @Autowired
    private VendorMaintenanceRepository vendorMaintenanceRepository;

    @Autowired
    private EquipmentRepository equipmentRepository;

    @Test
    public void testContractWarranty() {

        VendorWarranty warranty = new VendorWarranty();
        warranty.setName("保修单位001");
        warranty = vendorWarrantyRepository.save(warranty);

        ContractWarranty contractWarranty = new ContractWarranty();
        contractWarranty.setCode("BX001");
        contractWarranty.setStartDate(new Date());
        contractWarranty.setDueDate(new Date());
        contractWarranty.setWarranty(warranty);
        contractWarranty = contractWarrantyRepository.save(contractWarranty);


        Equipment equipment = new Equipment();
        equipment.setCode("SA0010020001");
        equipment.setName("空调箱");

        equipment.setContractWarranty(contractWarranty);
        equipment = equipmentRepository.save(equipment);
    }

    @Test
    public void testContractMaintenance() {

        VendorMaintenance maintenance1 = new VendorMaintenance();
        maintenance1.setName("维保单位001");
        maintenance1 = vendorMaintenanceRepository.save(maintenance1);

        VendorMaintenance maintenance2 = new VendorMaintenance();
        maintenance1.setName("维保单位002");
        maintenance2 = vendorMaintenanceRepository.save(maintenance2);

        ContractMaintenance contractMaintenance1 = new ContractMaintenance();
        contractMaintenance1.setCode("WB001");
        contractMaintenance1.setStartDate(new Date());
        contractMaintenance1.setDueDate(new Date());
        contractMaintenance1.setMaintenance(maintenance1);
        contractMaintenance1 = contractMaintenanceRepository.save(contractMaintenance1);

        ContractMaintenance contractMaintenance2 = new ContractMaintenance();
        contractMaintenance2.setCode("WB002");
        contractMaintenance2.setStartDate(new Date());
        contractMaintenance2.setDueDate(new Date());
        contractMaintenance2.setMaintenance(maintenance2);
        contractMaintenance2 = contractMaintenanceRepository.save(contractMaintenance2);

        List<ContractMaintenance> contractMaintenanceList = new ArrayList<ContractMaintenance>(2);
        contractMaintenanceList.add(contractMaintenance1);
        contractMaintenanceList.add(contractMaintenance2);

        Equipment equipment = new Equipment();
        equipment.setCode("SA0010020002");
        equipment.setName("空调箱");

        equipment.setContractMaintenances(contractMaintenanceList);

        equipment = equipmentRepository.save(equipment);
    }
}
