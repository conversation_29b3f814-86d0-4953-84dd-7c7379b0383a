package cn.facilityone.shang.common.entity;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.entity.patrol.Spot;
import cn.facilityone.shang.entity.patrol.SpotContent;
import cn.facilityone.shang.patrol.common.repository.SpotContentRepository;
import cn.facilityone.shang.patrol.common.repository.SpotRepository;
import cn.facilityone.shang.system.sys001.repository.SystemUserRepository;
import cn.facilityone.xia.security.model.Role;
import cn.facilityone.xia.security.model.User;
import cn.facilityone.xia.persistence.repository.XiaSpecification;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import javax.persistence.criteria.*;
import java.util.List;

/**
 * Created by aaron on 15/6/8.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class FetchTest {

    @Autowired
    private SystemUserRepository systemUserRepository;

    @Autowired
    private SpotContentRepository spotContentRepository;

    @Autowired
    private SpotRepository spotRepository;

    //    SystemUserRepository
//    @Query("select u from  #{#entityName} u join fetch u.roles")
//    List<User> findAllUsers();
    @Test
    public void testFetch() {

        // Eager Fetch
        //List<User> users = systemUserRepository.findAllUsers();

        //Lazy Fetch
        List<User> users = systemUserRepository.findAll();
        for (User user : users) {
            for (Role role : user.getRoles()) {
                System.out.println(role.getName());
            }
        }
    }

    @Test
    public void testFetchManyToOne() {
        Page<SpotContent> result = spotContentRepository.findAll(
                new XiaSpecification<SpotContent>() {

                    @Override
                    public Root<SpotContent> toRoot(Root<SpotContent> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                        root.fetch("spotEquipment", JoinType.LEFT);
                        root.fetch("patrolSpot", JoinType.LEFT);
                        return root;
                    }

                    @Override
                    public Predicate toPredicate(Root<SpotContent> root,
                                                 CriteriaQuery<?> criteriaQuery,
                                                 CriteriaBuilder criteriaBuilder) {
                        From<?, ?> spoteqfrom = root.join("spotEquipment", JoinType.LEFT);
                        From<?, ?> equipmentfrom = spoteqfrom.join("equipment", JoinType.LEFT);
                        return criteriaBuilder.equal(equipmentfrom, 118L);
                    }
                }, new PageRequest(0, 10));

        result.getContent();
    }

    @Test
    public void testFetchOneToMany() {
        Page<Spot> result = spotRepository.findAll(
                new XiaSpecification<Spot>() {

                    @Override
                    public Root<Spot> toRoot(Root<Spot> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                        root.fetch("spotEquipments", JoinType.LEFT);
                        root.fetch("spotContents", JoinType.LEFT);//cannot simultaneously fetch multiple bags
                        return root;
                    }

                    @Override
                    public Predicate toPredicate(Root<Spot> root,
                                                 CriteriaQuery<?> criteriaQuery,
                                                 CriteriaBuilder criteriaBuilder) {
                        return null;
                    }
                }, new PageRequest(0, 10));
        result.getContent();
    }
}
