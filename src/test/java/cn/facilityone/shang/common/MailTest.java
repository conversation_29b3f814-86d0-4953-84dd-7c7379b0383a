package cn.facilityone.shang.common;
import java.util.Date;  
import java.util.Properties;  
  
import javax.mail.Message;  
import javax.mail.MessagingException;  
import javax.mail.Session;  
import javax.mail.internet.MimeMessage;  
  
import com.sun.mail.smtp.SMTPTransport;  

public class MailTest {

    public static void main(String[] args)  
    {  
         
        String host="amtp11.ap.jll.com";  
        String from="<EMAIL>";  
        String user="facilitybutler1.ifm";  
        String pwd="F@cil!ty@12";
        
        String to="<EMAIL>";  
        int port=25;
         
        Properties props = new Properties();  
        props.put("mail.smtp.host", host);  
        props.put("mail.from", from);  
        props.put("mail.smtp.auth", "true");  
        Session session = Session.getInstance(props, null);  
  
        try {  
             
            MimeMessage msg = new MimeMessage(session);  
            msg.setFrom();  
            msg.setRecipients(Message.RecipientType.TO,to);  
            msg.setSubject("测试发件箱是否能发邮件");  
            msg.setSentDate(new Date());  
            msg.setText("Hello world!");  
             
            SMTPTransport t =(SMTPTransport)session.getTransport("smtp");  
            try {  
                t.connect(host, port,user, pwd);  
                t.sendMessage(msg, msg.getAllRecipients());  
            }  
            finally {  
                System.out.println("Response: " +t.getLastServerResponse());  
                t.close();  
            }  
             
        } catch (MessagingException mex) {  
            System.out.println("send failed, exception: " + mex);  
        }  
  
    }  
}
