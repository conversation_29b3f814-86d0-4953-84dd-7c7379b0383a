package cn.facilityone.shang.common;

import cn.facilityone.Bootstrap;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import java.util.Locale;

/**
 * Created by aaron on 15/4/15.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class I18nTest {

    @Autowired
    private ReloadableResourceBundleMessageSource messageSource;

    @Test
    public void testGetTableField() {
        Assert.assertEquals("设备编码", messageSource.getMessage("Equipment.code", null, Locale.SIMPLIFIED_CHINESE));
        Assert.assertEquals("Equipment Code", messageSource.getMessage("Equipment.code", null, Locale.US));
    }
}
