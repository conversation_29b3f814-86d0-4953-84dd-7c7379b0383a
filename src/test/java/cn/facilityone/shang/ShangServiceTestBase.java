package cn.facilityone.shang;

import cn.facilityone.Bootstrap;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class ShangServiceTestBase {


    @Test
    public void text() {

    }


}
