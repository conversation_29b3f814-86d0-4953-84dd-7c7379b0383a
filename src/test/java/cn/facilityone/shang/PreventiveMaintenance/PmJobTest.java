package cn.facilityone.shang.PreventiveMaintenance;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.preventive.pm002.service.NoticeActivityService;
import cn.facilityone.shang.preventive.pm002.service.PMDateTodoJobService;
import cn.facilityone.shang.preventive.pm002.service.PmNoticeActivityService;
import cn.facilityone.shang.workorder.common.service.WorkOrderService;
import cn.facilityone.shang.workorder.common.service.WorkOrderTaskService;
import cn.facilityone.shang.workorder.wo003.service.WorkOrderProcessService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import java.util.Date;
import java.util.List;

/**
 * Created by charles.chen on 2015/7/27.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class PmJobTest  {
    @Autowired
    private PMDateTodoJobService pmDateTodoJobService;
    @Autowired
    private WorkOrderTaskService workOrderTaskService;
    @Autowired
    private WorkOrderProcessService workOrderProcessService;
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private PmNoticeActivityService pmNoticeActivityService;
    @Autowired
    private NoticeActivityService noticeActivityService;

    @Test
    public void scanningAllNotice(){

    }

    @Test
    public void testGenerateWorkNoticeActivity(){
        pmNoticeActivityService.generatePmNoticeActivityForWoForJob();
    }


    @Test
    public void testGenerateWoActivity(){
        pmNoticeActivityService.generatePmNoticeActivityForRemindHaveWoForJob();
        pmNoticeActivityService.generatePmNoticeActivityForRemindHaveNoWoForJob(new Date());
    }

    @Test
    public void testGenerateTodo(){
        pmDateTodoJobService.generateTodoByJob();
    }



    @Test
    public void testGenerateWorkOrder(){
        try{
            //生成工单
            List<WorkOrder> workOrders=pmDateTodoJobService.gengerateWorkOrder();
            for(WorkOrder workOrder : workOrders){
                //启动流程
                workOrder=workOrderProcessService.startWorkProcess(workOrder);
                //完成工单
                workOrder=workOrderTaskService.completeCreate(workOrder);
                //自动派工
                workOrderService.autoDispatch(workOrder.getId());
            }
        }catch(Exception e){
            e.printStackTrace();
        }
    }
}
