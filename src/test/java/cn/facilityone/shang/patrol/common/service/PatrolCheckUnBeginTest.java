package cn.facilityone.shang.patrol.common.service;

import cn.facilityone.shang.ShangServiceTestBase;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.util.Date;

/**
 * Created by ca<PERSON>ulin on 15/8/3.
 */
public class PatrolCheckUnBeginTest extends ShangServiceTestBase {

    @Autowired
    private PatrolTaskService patrolTaskService;

    @Test
    public void testCheckUnBegin() throws ParseException {
        Date scan = DateUtils.parseDate("2015-08-21 18:00:00", "yyyy-MM-dd HH:mm:ss");
        patrolTaskService.scanOverduePatrolTask(scan);
    }

}
