package cn.facilityone.shang.patrol.common.service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.facilityone.shang.ShangServiceTestBase;
import cn.facilityone.shang.entity.common.Period;
import cn.facilityone.shang.entity.common.TimeCount;
import cn.facilityone.shang.entity.patrol.PatrolTime;
import cn.facilityone.shang.patrol.common.dto.RemindDTO;
import cn.facilityone.shang.projects.pro002.service.ProjectService;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.security.license.auth.XiaLicenseAuthorization;
import cn.facilityone.xia.security.license.entity.PublicKeyStore;
import cn.facilityone.xia.security.license.entity.XiaLicense;

/**
 * Created by aaron on 15/7/28.
 */
public class PatrolTaskJobServiceImplTest extends ShangServiceTestBase {

    @Autowired
    private PatrolTaskJobServiceImpl patrolTaskJobService;

    @Autowired
    private ProjectService projectService;

    @Test
    public void testFindCandidateRemindPatrolTime() throws ParseException {
        ProjectContext.setProjects(projectService.findAll());
        Date scanDate = DateUtils.parseDate("2015-09-16 04:55:00", "yyyy-MM-dd HH:mm:ss");
        //patrolTaskJobService.sendPatrolTaskReminds(scanDate);

    }

    @Test
    public void testCompare() throws ParseException {
        List<PatrolTime> patrolTimes = new ArrayList<>();
        PatrolTime patrolTime1 = new PatrolTime();
        patrolTime1.setStartTime(DateUtils.parseDate("1990-01-01 00:10:00", "yyyy-MM-dd HH:mm:ss"));
        patrolTimes.add(patrolTime1);

        PatrolTime patrolTime2 = new PatrolTime();
        patrolTime2.setStartTime(DateUtils.parseDate("1990-01-01 00:01:00", "yyyy-MM-dd HH:mm:ss"));
        patrolTimes.add(patrolTime2);

        PatrolTime patrolTime3 = new PatrolTime();
        patrolTime3.setStartTime(DateUtils.parseDate("1990-01-01 08:00:00", "yyyy-MM-dd HH:mm:ss"));
        patrolTimes.add(patrolTime3);

        Collections.sort(patrolTimes, new Comparator<PatrolTime>() {
            @Override
            public int compare(PatrolTime o1, PatrolTime o2) {
                if (o1.getStartTime().getTime() > o2.getStartTime().getTime()) {
                    return 1;
                } else if (o1.getStartTime().getTime() < o2.getStartTime().getTime()) {
                    return -1;
                }
                return 0;
            }
        });

        Assert.assertEquals(patrolTimes.get(0).getStartTime(), DateUtils.parseDate("1990-01-01 00:01:00", "yyyy-MM-dd HH:mm:ss"));
    }

    @Test
    public void testConcatDateAndTime() throws ParseException {
        Date date = DateUtils.parseDate("2015-07-29 08:00:00", "yyyy-MM-dd HH:mm:ss");
        Date time = DateUtils.parseDate("2015-07-02 23:59:59", "yyyy-MM-dd HH:mm:ss");
        date = DateUtils.setHours(date, (int) DateUtils.getFragmentInHours(time, Calendar.DATE));
        date = DateUtils.setMinutes(date, (int) DateUtils.getFragmentInMinutes(time, Calendar.HOUR_OF_DAY));
        date = DateUtils.setSeconds(date, (int) DateUtils.getFragmentInSeconds(time, Calendar.MINUTE));

        Date result = DateUtils.parseDate("2015-07-29 23:59:59", "yyyy-MM-dd HH:mm:ss");
        Assert.assertEquals(date, result);

    }

    @Test
    public void testFindCandidatePatrolTime() throws ParseException {
        Date scanDate = DateUtils.parseDate("2015-07-31 10:10:00", "yyyy-MM-dd HH:mm:ss");
        List<RemindDTO> timeList = patrolTaskJobService.findCandidatePatrolTimeForJob(scanDate);
    }

    @Test
    public void testGenerateTasks() throws ParseException {
        XiaLicenseAuthorization.getInstance().install(new PublicKeyStore(),new XiaLicense());
        ProjectContext.setProjects(projectService.findAll());
        Date scanDate = DateUtils.parseDate("2015-09-26 01:00:00", "yyyy-MM-dd HH:mm:ss");
//        Date scanDate=new Date();
        //patrolTaskJobService.generateTasks(scanDate);
    }

    @Test
    public void testCheckPeriodDate() throws ParseException {
        Date startDateTime = DateUtils.parseDate("2015-06-15 12:00:00", "yyyy-MM-dd HH:mm:ss");
        Date scanDate = DateUtils.parseDate("2015-07-06 11:00:00", "yyyy-MM-dd HH:mm:ss");

        Period period = new Period();
        period.setType(Period.PeriodType.WEEK);
        period.setValue(3L);

        Calendar c = Calendar.getInstance();
        c.setTime(startDateTime);
        while (startDateTime.before(scanDate)) {
            if (Period.PeriodType.DAY.equals(period.getType())) {
                startDateTime = DateUtils.addDays(startDateTime, period.getValue().intValue());
            } else if (Period.PeriodType.WEEK.equals(period.getType())) {
                startDateTime = DateUtils.addWeeks(startDateTime, period.getValue().intValue());
            } else if (Period.PeriodType.MONTH.equals(period.getType())) {
                startDateTime = DateUtils.addMonths(startDateTime, period.getValue().intValue());
            } else {
                Assert.assertTrue(true);
                return;
            }
            if (DateUtils.isSameDay(startDateTime, scanDate)) {
                Assert.assertTrue(true);
                return;
            }

        }
        Assert.assertTrue(false);
    }

    @Test
    public void testHeadDate() throws ParseException {
        Date date = DateUtils.parseDate("2015-06-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        TimeCount timeCount = new TimeCount();
        timeCount.setUnit(TimeUnit.MINUTES);
        timeCount.setValue(20L);
        boolean flag = true;

        int amount = timeCount.getValue().intValue();
        if (flag) {
            amount = -timeCount.getValue().intValue();
        }
        //提前时间必须为整数 例如 30分钟 1小时 1天
        //提前分钟
        if (timeCount.getUnit().equals(timeCount.getUnit().MINUTES)) {
            date = DateUtils.addMinutes(date, amount);
        }
        //提前小时
        else if (timeCount.getUnit().equals(timeCount.getUnit().HOURS)) {
            date = DateUtils.addHours(date, amount);
        }
        //提前天数
        else if (timeCount.getUnit().equals(timeCount.getUnit().DAYS)) {
            date = DateUtils.addDays(date, amount);
        }
        Date date1 = DateUtils.parseDate("2015-05-31 23:40:00", "yyyy-MM-dd HH:mm:ss");
        Assert.assertEquals(date, date1);
    }


}
