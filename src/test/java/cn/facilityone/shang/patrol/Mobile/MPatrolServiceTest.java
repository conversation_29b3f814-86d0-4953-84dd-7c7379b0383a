package cn.facilityone.shang.patrol.Mobile;

import cn.facilityone.shang.ShangRepositoryTestBase;
import cn.facilityone.shang.mobile.v1.patrol.service.MPatrolService;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * Created by caoyulin on 15/7/27.
 */
public class MPatrolServiceTest extends ShangRepositoryTestBase {

    @Autowired
    private MPatrolService mPatrolService;

    @Test
    @DatabaseSetup("equipment.xml")
    public void deleteEquipmentSystemFromEquipmentTest(){
        mPatrolService.findAllPatrolTaskCount(new Date());
    }
}
