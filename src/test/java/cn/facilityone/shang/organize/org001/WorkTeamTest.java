package cn.facilityone.shang.organize.org001;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.IsNull.notNullValue;

import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.asset.asset002.repository.VendorRepository;
import cn.facilityone.shang.asset.asset003.service.ContractExpireService;
import cn.facilityone.shang.entity.common.Vendor;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.organize.WorkTeam;
import cn.facilityone.shang.organize.org001.repository.WorkTeamRepository;
import cn.facilityone.shang.organize.org001.service.WorkTeamService;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.organize.org002.service.EmployeeService;

/**
 * Created by charles.chen on 2015/4/26.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class WorkTeamTest {
    @Autowired
    private WorkTeamService workTeamService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private WorkTeamRepository workTeamRepository;

    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private VendorRepository vendorRepository;
    @Autowired
    private ContractExpireService contractExpireService;




    @Test
    public void testSendMessage(){
       // contractExpireService.noticeOncePerDayJob();
    }
    @Test
    public void findAllTest(){
        List<WorkTeam> workTeamList=workTeamRepository.findAll();
        assertThat(workTeamList, is(notNullValue()));
    }


    @Test
    public void testSaveWorkTeam(){

        Vendor vendor=vendorRepository.findOne(1L);
        System.out.println(vendor.getName());
    }


    @Test
    public void testDeleteMemberInWorkTeam(){
        List<WorkTeam> workTeamList=workTeamRepository.findAll();
        List<Employee> employeeList=employeeRepository.findAll();
        workTeamService.deleteMemberInWorkTeam(workTeamList.get(0).getId(),employeeList.get(0).getId());
    }


    @Test
    public void testAddMemberInWorkTeam(){
        List<WorkTeam> workTeamList=workTeamRepository.findAll();
        List<Employee> employeeList=employeeRepository.findAll();
        workTeamService.addMembers(new Long[]{employeeList.get(0).getId()},workTeamList.get(0).getId());
       WorkTeam workTeam=workTeamRepository.findOne(workTeamList.get(0).getId());
        Assert.assertNotEquals(workTeam.getMembers(), null);

    }

}
