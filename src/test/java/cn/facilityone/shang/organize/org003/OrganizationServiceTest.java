package cn.facilityone.shang.organize.org003;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.ShangRepositoryTestBase;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.organize.Organization;
import cn.facilityone.shang.organize.org003.service.OrganizationService;
import cn.facilityone.shang.organize.org004.repository.FloorRepository;
import com.github.springtestdbunit.DbUnitTestExecutionListener;
import com.github.springtestdbunit.TransactionDbUnitTestExecutionListener;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.SpringApplicationConfiguration;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.test.context.support.DirtiesContextTestExecutionListener;
import org.springframework.test.context.transaction.TransactionConfiguration;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.IsNull.notNullValue;
import static org.hamcrest.core.IsNull.nullValue;

/**
 * Created by alvin.cao on 2015/4/20.
 */

public class OrganizationServiceTest extends ShangRepositoryTestBase {


    @Autowired
    private OrganizationService organizationService;

    /**
     * type 注意Database的type类型，默认CLEAN_INSERT
     */
    @Test
    @DatabaseSetup("organize.xml")
    public void searchTest() {
       List <Organization> organization=organizationService.findChildren(102L);
        assertThat(organization, is(notNullValue()));
        assertThat(organization.size(), is(2));
    }


    @Test
    @ExpectedDatabase(value="employee-expected.xml",table = "em", query = "select em_id,version,em_name,number,phone,deleted,type from em where deleted=0")
    public void testDeleteOrganization(){
        organizationService.deleteOneAndChildren(102L);
        List <Organization> organization=organizationService.findChildren(102L);
        assertThat(organization, is(nullValue()));
        assertThat(organization.size(), is(0));
    }


}
