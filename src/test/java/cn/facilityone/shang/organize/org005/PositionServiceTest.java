//package cn.facilityone.shang.organize.org005;
//
//import cn.facilityone.shang.entity.organize.Position;
//import cn.facilityone.shang.organize.org005.repository.PositionRepository;
//import cn.facilityone.shang.organize.org005.service.PositionService;
//import cn.facilityone.xia.web.rest.common.Result;
//import com.nitorcreations.junit.runners.NestedRunner;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//
//import static com.googlecode.catchexception.CatchException.catchException;
//import static org.mockito.Mockito.mock;
//
///**
//* @Author: wayne.fu
//* @Date: 4/22/2015
//*/
//
//@RunWith(NestedRunner.class)
//public class PositionServiceTest {
//
//    private PositionRepository positionRepository;
//    private PositionService positionService;
//
//    @Before
//    public void setUp() {
//        positionRepository = mock(PositionRepository.class);
//        positionService = mock(PositionService.class);
//    }
//
//    public class AddPosition {
//
//        public class WhenPositionHasSameName {
//
//            String positionNameExist = "清洁工";
//            Position positionExist = null;
//
//            @Before
//            public void setUp() {
//                positionExist = new Position();
//                positionExist.setName(positionNameExist);
//            }
//
//            @Test
//            public void shouldThrowException() {
//                Assert.assertNotNull(positionRepository.findByName(positionNameExist));
//                catchException(positionService).create(positionExist);
//                Assert.assertEquals(Result.CODE_200, Result.data(positionService.create(positionExist)).getCode());
//            }
//
//        }
//
//        public class WhenNoPositionWithTheName {
//
//        }
//    }
//}
