package cn.facilityone.shang.organize.org002;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.organize.WorkTeam;
import cn.facilityone.shang.organize.org001.repository.WorkTeamRepository;
import cn.facilityone.shang.organize.org001.service.WorkTeamService;
import cn.facilityone.shang.organize.org002.dto.EmployeeDTO;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.organize.org002.service.EmployeeService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import java.lang.reflect.InvocationTargetException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * Created by charles.chen on 2015/4/26.
 */
@RunWith(SpringJUnit4ClassRunner.class)
   @ContextConfiguration(classes = Bootstrap.class)
   @TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class EmployeeTest {
    @Autowired
    private WorkTeamService workTeamService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private WorkTeamRepository workTeamRepository;
    @Autowired
    private EmployeeRepository employeeRepository;


    @Test
    public void testSaveEmployee() throws SQLException {
//        List<Long> longList=new ArrayList<>();
//        longList.add(1L);
//        longList.add(2L);
//        List<SmsDTO> smsDTOs=employeeRepository.findSmsSenderById(longList.toArray(new Long[longList.size()]));
//        Iterator it=smsDTOs.iterator();
//        while(it.hasNext()){
//            Object[] obj=(Object[])it.next();
//            System.out.println(obj[0]);
//        }

    }

    @Test
    public void testUpdateEmployee() throws InvocationTargetException, IllegalAccessException {
        //List<WorkTeam> workTeamList=workTeamRepository.findAll();
        List<Employee> employeeList=employeeRepository.findAll();
        EmployeeDTO employeeDTO=employeeService.findOne(employeeList.get(0).getId());
        employeeDTO.getEmployee().setName("JDK");
        employeeService.update(employeeDTO);
        Assert.assertEquals(employeeService.findOne(employeeList.get(0).getId()).getEmployee().getName(), "JDK");
    }


    @Test
    public void testDeleteEmployee(){
        List<Employee> employeeList=employeeRepository.findAll();
        int result=employeeService.deleteEmployee(employeeList.get(0).getId(),false);
        Assert.assertEquals(result, 0);
    }

    @Test
    public void testEmployeeIsSuper(){
        List<Employee> employeeList=employeeRepository.findAll();
        List<WorkTeam> workTeamList=workTeamRepository.findWorkTeamsByEmployeeId(employeeList.get(0).getId());
        Assert.assertNotNull(workTeamList);
    }
    @Test
    public void testUserNamehasExit(){
        List<Employee> employeeList=employeeRepository.findAll();
        boolean result=employeeService.UserNamehasExit(employeeList.get(0).getName());
        Assert.assertFalse(result);
    }

    @Test
    public void testEmployeePage(){
        Page<Employee> employeeList=employeeRepository.findEntitysPageHardly(new Pageable() {
            @Override
            public int getPageNumber() {
                return 0;
            }

            @Override
            public int getPageSize() {
                return 0;
            }

            @Override
            public int getOffset() {
                return 0;
            }

            @Override
            public Sort getSort() {
                return null;
            }

            @Override
            public Pageable next() {
                return null;
            }

            @Override
            public Pageable previousOrFirst() {
                return null;
            }

            @Override
            public Pageable first() {
                return null;
            }

            @Override
            public boolean hasPrevious() {
                return false;
            }
        });
        Assert.assertEquals(employeeList.getContent().size(), 23);
    }
}
