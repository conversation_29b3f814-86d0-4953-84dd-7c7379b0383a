package cn.facilityone.shang.stock.stcok005;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.common.service.UploadFileService;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.stock.stock005.service.InventoryVerificationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: wayne.fu
 * @Date: 8/12/2015
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
public class TestInventoryVerification {

    @Autowired
    private InventoryVerificationService inventoryVerificationService;
    @Autowired
    private UploadFileService uploadFileService;

    @Test
    public void testCheckFile() {
        XiaFile file = uploadFileService.findOne(34401L);
        inventoryVerificationService.checkUploadFileData(file);
    }
}
