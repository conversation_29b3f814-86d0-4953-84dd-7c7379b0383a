package cn.facilityone.shang.stock.stock003;

import cn.facilityone.Bootstrap;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.inventory.MaterialBatchChange;
import cn.facilityone.shang.stock.stock003.service.MaterialBatchChangeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by charles.chen on 2015/8/14.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = Bootstrap.class)
public class StockInventoryTest {

    @Autowired
    private MaterialBatchChangeService materialBatchChangeService;

    @Test
    public void testFindPage() {


    }
}
