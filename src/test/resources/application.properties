#============================================================================
# Configure f1-sa
#============================================================================
shang.system.zhName=F-ONE \u8BBE\u65BD\u670D\u52A1\u7BA1\u7406\u8F6F\u4EF6
shang.system.enName=F-ONE FM System

# base dir of file upload
shang.system.uploadBaseDir=${user.dir}/src/test/resources/upload
# base dir of file upload
f1-sa.data.upload.baseDir = ${user.dir}/src/main/resources/static/upload

#message init dir
f1-sa.message.template.baseDir = ${user.dir}/src/main/java

# config company info
f1-sa.config.company.name=\u4E0A\u5B89\u7269\u4E1A
f1-sa.config.company.phone=021-56979102
f1-sa.config.company.fax=021-56979102
f1-sa.config.company.address=\u4E0A\u6D77\u5E02\u95F8\u5317\u533A\u4E2D\u5174\u8DEF1757\u53F7
f1-sa.config.company.postcode=200070
f1-sa.config.company.mobile=15206186877
f1-sa.config.company.linkMan=\u5C0F\u6797
f1-sa.config.company.email=<EMAIL>

debug : true
#spring.freemarker.check-template-location=false
#xia.persistence.spring-data-jpa.repository-packages=cn.facilityone.sa
#xia.persistence.entity-base-packages=cn.facilityone.sa

#jta
spring.jta.enable_logging=true
spring.jta.force_shutdown_on_vm_exit=true
spring.jta.log_base_dir=${user.home}/.xia/logs/jta-test
spring.jta.console_log_level=DEBUG
spring.jta.max_actives=-10
spring.jta.max_timeout=600000
spring.jta.default_jta_timeout=600000

# default to use H2 database, if your want to use another one, please uncomment following lines
# MYSQL JDBC Settings...
spring.datasource.qualifier=xaDataSource
spring.datasource.xa.dataSourceClassName=com.mysql.jdbc.jdbc2.optional.MysqlXADataSource
spring.datasource.driverClassName=com.mysql.jdbc.Driver
#spring.datasource.url=*****************************************************************************************************************
spring.datasource.url=******************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root

#true when first install,then start must be false
spring.datasource.initialize=false
spring.datasource.sqlScriptEncoding=UTF-8

spring.jpa.openInView=true
spring.jpa.show-sql=true

# Dialect 
spring.jpa.database-platform=org.hibernate.dialect.MySQL5Dialect

spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=update

xia.jersey.resourcePackage=cn.facilityone.shang
xia.jersey.loginUrl=login
xia.jersey.successUrl=index.html
xia.jersey.unauthorizedUrl=error_500.html
xia.jersey.allAllowed=false
xia.jersey.accessiblePattern=/login/**,/oauth2/**

# HTTP port 
#server.port = 80
#server.address=
#server.sessionTimeout

#============================================================================
# Management server Configuration
#============================================================================
management.context-path=/manage
#management.port=8081
#management.security.enabled=true is not support now.
management.security.enabled=false

#============================================================================
# Configure xia-message 
#============================================================================
xia.message.core.debug=true

xia.message.site.hostname=
xia.message.site.chanel=DefaultChannel
xia.message.site.port=9901

xia.message.email.smtpHost=smtp.qq.com
xia.message.email.systemName=1421695258
xia.message.email.systemPwd=andy.ding2015
xia.message.email.systemEmail=<EMAIL>
xia.message.email.encoding=utf-8
xia.message.email.valid=^([a-zA-Z0-9]*[-_]?[a-zA-Z0-9]+)*@([a-zA-Z0-9]*[-_]?[a-zA-Z0-9]+)+[\\.][A-Za-z]{2,3}([\\.][A-Za-z]{2})?$

xia.message.sms.code=6SDK-EMY-6688-KGTMM
xia.message.sms.pwd=102020
xia.message.sms.valid=^((13[0-9])|(15[^4,\\D])|(18[0,5-9]))\\d{8}$

#============================================================================
# Enable SSL
#============================================================================
#server.port = 8443
#server.ssl.key-store = classpath:keystoresa.jks
#server.ssl.key-store-password = 1qaz2wsx3edc
#server.ssl.key-password = 1qaz2wsx3edc

#============================================================================
# Configure init datatable with sql
#============================================================================
# base path is classpath,many sql file seperate with COMMA,
#xia.datatable.init.sql=init/quartz_tables_mysql.sql

#============================================================================
# Configure velocity
#============================================================================
spring.velocity.checkTemplateLocation=false

#============================================================================
# Configure freemarker
#============================================================================
spring.freemarker.checkTemplateLocation=false

 #============================================================================
 # Configure i18n
 #============================================================================
xia.i18n.basename=i18n/strings
xia.i18n.defaultLanguage=zh
xia.i18n.defaultCountry=CN

#============================================================================
# Configure Quartz
#============================================================================
# true = no db save
xia.quartz.debug=true