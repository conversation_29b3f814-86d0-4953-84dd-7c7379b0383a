<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="processDefinitions" id="processDefinitions">
  <process id="order" name="工单流程" isExecutable="true">
    <startEvent id="start" name="启动流程"></startEvent>
    <userTask id="accept" name="执行人-接单/退单"></userTask>
    <sequenceFlow id="flow2" sourceRef="start" targetRef="workorder-create"></sequenceFlow>
    <userTask id="workorder-create" name="创建工单"></userTask>
    <sequenceFlow id="flow11" sourceRef="workorder-create" targetRef="is-auto-dispatching"></sequenceFlow>
    <exclusiveGateway id="is-auto-dispatching" name="是否自动派工"></exclusiveGateway>
    <sequenceFlow id="flow42" name="自动" sourceRef="is-auto-dispatching" targetRef="dispatching-auto">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isAutoDispatching == 'true'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="scheduling-dispatching" name="排程-派工"></userTask>
    <userTask id="execute" name="处理工单"></userTask>
    <sequenceFlow id="flow36" name="验证" sourceRef="is-checking" targetRef="checking">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isChecking == 'true'}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="dispatching-auto" name="自动派工" activiti:expression="#{activitiAutoDispatchingService.execute(execution)}"></serviceTask>
    <sequenceFlow id="flow43" sourceRef="dispatching-auto" targetRef="accept"></sequenceFlow>
    <userTask id="checking" name="验证"></userTask>
    <sequenceFlow id="flow45" sourceRef="checking" targetRef="checking-result"></sequenceFlow>
    <exclusiveGateway id="checking-result" name="是否验证通过"></exclusiveGateway>
    <sequenceFlow id="flow47" name="验证不通过" sourceRef="checking-result" targetRef="execute">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${checkingResult == 'false-execute'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow51" name="不验证" sourceRef="is-checking" targetRef="archive">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isChecking == 'false'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="is-checking" name="是否验证"></exclusiveGateway>
    <userTask id="archive" name="归档"></userTask>
    <sequenceFlow id="flow55" name="验证通过" sourceRef="checking-result" targetRef="archive">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${checkingResult == 'true'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow60" sourceRef="is-auto-dispatching" targetRef="scheduling-dispatching">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isAutoDispatching == 'false'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow61" sourceRef="complete" targetRef="is-checking"></sequenceFlow>
    <userTask id="complete" name="完成处理"></userTask>
    <exclusiveGateway id="is-complete-success" name="是否完成"></exclusiveGateway>
    <sequenceFlow id="flow66" sourceRef="execute" targetRef="is-complete-success"></sequenceFlow>
    <sequenceFlow id="flow67" name="正常完成" sourceRef="is-complete-success" targetRef="complete">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isCompleteSuccess == 'complete'}]]></conditionExpression>
    </sequenceFlow>
    <endEvent id="endevent1" name="End"></endEvent>
    <sequenceFlow id="flow69" sourceRef="archive" targetRef="endevent1"></sequenceFlow>
    <userTask id="pause" name="暂停"></userTask>
    <sequenceFlow id="flow70" name="暂停" sourceRef="is-complete-success" targetRef="pause">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isCompleteSuccess == 'pause'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="stop" name="终止"></userTask>
    <sequenceFlow id="flow71" name="终止" sourceRef="is-complete-success" targetRef="stop">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isCompleteSuccess == 'stop'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="is-continue" name="是否继续"></exclusiveGateway>
    <sequenceFlow id="flow75" sourceRef="pause" targetRef="is-continue"></sequenceFlow>
    <sequenceFlow id="flow76" name="继续工作" sourceRef="is-continue" targetRef="execute">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isContinue == 'true'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow77" name="不继续" sourceRef="is-continue" targetRef="scheduling-dispatching">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isContinue == 'false-dispatching'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow78" sourceRef="stop" targetRef="is-checking"></sequenceFlow>
    <sequenceFlow id="flow81" name="验证不通过" sourceRef="checking-result" targetRef="scheduling-dispatching">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${checkingResult == 'false-create'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="is-all-reject" name="是否接单"></exclusiveGateway>
    <sequenceFlow id="flow82" sourceRef="accept" targetRef="is-all-reject"></sequenceFlow>
    <sequenceFlow id="flow83" name="至少一人接单" sourceRef="is-all-reject" targetRef="execute">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isAllReject == 'false'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow84" name="所有人退单" sourceRef="is-all-reject" targetRef="scheduling-dispatching">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isAllReject == 'true'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="is-stop" name="是否终止"></exclusiveGateway>
    <sequenceFlow id="flow85" sourceRef="scheduling-dispatching" targetRef="is-stop"></sequenceFlow>
    <sequenceFlow id="flow86" sourceRef="is-stop" targetRef="accept">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isStop == 'false'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow87" name="终止" sourceRef="is-stop" targetRef="stop">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isStop == 'true'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow88" name="所有人退单" sourceRef="is-complete-success" targetRef="scheduling-dispatching">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isCompleteSuccess == 'allReject'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_order">
    <bpmndi:BPMNPlane bpmnElement="order" id="BPMNPlane_order">
      <bpmndi:BPMNShape bpmnElement="start" id="BPMNShape_start">
        <omgdc:Bounds height="35.0" width="35.0" x="376.0" y="20.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="accept" id="BPMNShape_accept">
        <omgdc:Bounds height="55.0" width="105.0" x="341.0" y="400.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="workorder-create" id="BPMNShape_workorder-create">
        <omgdc:Bounds height="55.0" width="105.0" x="341.0" y="92.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="is-auto-dispatching" id="BPMNShape_is-auto-dispatching">
        <omgdc:Bounds height="40.0" width="40.0" x="373.0" y="180.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="scheduling-dispatching" id="BPMNShape_scheduling-dispatching">
        <omgdc:Bounds height="55.0" width="105.0" x="341.0" y="258.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="execute" id="BPMNShape_execute">
        <omgdc:Bounds height="55.0" width="105.0" x="341.0" y="580.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="dispatching-auto" id="BPMNShape_dispatching-auto">
        <omgdc:Bounds height="55.0" width="105.0" x="540.0" y="173.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="checking" id="BPMNShape_checking">
        <omgdc:Bounds height="55.0" width="105.0" x="540.0" y="866.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="checking-result" id="BPMNShape_checking-result">
        <omgdc:Bounds height="40.0" width="40.0" x="572.0" y="968.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="is-checking" id="BPMNShape_is-checking">
        <omgdc:Bounds height="40.0" width="40.0" x="374.0" y="873.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="archive" id="BPMNShape_archive">
        <omgdc:Bounds height="55.0" width="105.0" x="342.0" y="961.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="complete" id="BPMNShape_complete">
        <omgdc:Bounds height="55.0" width="105.0" x="342.0" y="780.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="is-complete-success" id="BPMNShape_is-complete-success">
        <omgdc:Bounds height="40.0" width="40.0" x="374.0" y="680.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endevent1" id="BPMNShape_endevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="377.0" y="1050.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="pause" id="BPMNShape_pause">
        <omgdc:Bounds height="55.0" width="105.0" x="138.0" y="673.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="stop" id="BPMNShape_stop">
        <omgdc:Bounds height="55.0" width="105.0" x="138.0" y="780.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="is-continue" id="BPMNShape_is-continue">
        <omgdc:Bounds height="40.0" width="40.0" x="170.0" y="587.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="is-all-reject" id="BPMNShape_is-all-reject">
        <omgdc:Bounds height="40.0" width="40.0" x="373.0" y="490.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="is-stop" id="BPMNShape_is-stop">
        <omgdc:Bounds height="40.0" width="40.0" x="374.0" y="331.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow2" id="BPMNEdge_flow2">
        <omgdi:waypoint x="393.0" y="55.0"></omgdi:waypoint>
        <omgdi:waypoint x="393.0" y="92.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow11" id="BPMNEdge_flow11">
        <omgdi:waypoint x="393.0" y="147.0"></omgdi:waypoint>
        <omgdi:waypoint x="393.0" y="180.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow42" id="BPMNEdge_flow42">
        <omgdi:waypoint x="413.0" y="200.0"></omgdi:waypoint>
        <omgdi:waypoint x="540.0" y="200.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="473.0" y="200.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow36" id="BPMNEdge_flow36">
        <omgdi:waypoint x="414.0" y="893.0"></omgdi:waypoint>
        <omgdi:waypoint x="540.0" y="893.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="455.0" y="900.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow43" id="BPMNEdge_flow43">
        <omgdi:waypoint x="592.0" y="228.0"></omgdi:waypoint>
        <omgdi:waypoint x="592.0" y="427.0"></omgdi:waypoint>
        <omgdi:waypoint x="446.0" y="427.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow45" id="BPMNEdge_flow45">
        <omgdi:waypoint x="592.0" y="921.0"></omgdi:waypoint>
        <omgdi:waypoint x="592.0" y="968.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow47" id="BPMNEdge_flow47">
        <omgdi:waypoint x="612.0" y="988.0"></omgdi:waypoint>
        <omgdi:waypoint x="706.0" y="987.0"></omgdi:waypoint>
        <omgdi:waypoint x="706.0" y="607.0"></omgdi:waypoint>
        <omgdi:waypoint x="446.0" y="607.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="60.0" x="629.0" y="970.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow51" id="BPMNEdge_flow51">
        <omgdi:waypoint x="394.0" y="913.0"></omgdi:waypoint>
        <omgdi:waypoint x="394.0" y="961.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="36.0" x="350.0" y="920.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow55" id="BPMNEdge_flow55">
        <omgdi:waypoint x="572.0" y="988.0"></omgdi:waypoint>
        <omgdi:waypoint x="447.0" y="988.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="48.0" x="503.0" y="970.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow60" id="BPMNEdge_flow60">
        <omgdi:waypoint x="393.0" y="220.0"></omgdi:waypoint>
        <omgdi:waypoint x="393.0" y="258.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow61" id="BPMNEdge_flow61">
        <omgdi:waypoint x="394.0" y="835.0"></omgdi:waypoint>
        <omgdi:waypoint x="394.0" y="873.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow66" id="BPMNEdge_flow66">
        <omgdi:waypoint x="393.0" y="635.0"></omgdi:waypoint>
        <omgdi:waypoint x="394.0" y="680.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow67" id="BPMNEdge_flow67">
        <omgdi:waypoint x="394.0" y="720.0"></omgdi:waypoint>
        <omgdi:waypoint x="394.0" y="780.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="48.0" x="371.0" y="739.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow69" id="BPMNEdge_flow69">
        <omgdi:waypoint x="394.0" y="1016.0"></omgdi:waypoint>
        <omgdi:waypoint x="394.0" y="1050.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow70" id="BPMNEdge_flow70">
        <omgdi:waypoint x="374.0" y="700.0"></omgdi:waypoint>
        <omgdi:waypoint x="243.0" y="700.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="319.0" y="680.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow71" id="BPMNEdge_flow71">
        <omgdi:waypoint x="394.0" y="720.0"></omgdi:waypoint>
        <omgdi:waypoint x="190.0" y="780.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="332.0" y="724.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow75" id="BPMNEdge_flow75">
        <omgdi:waypoint x="190.0" y="673.0"></omgdi:waypoint>
        <omgdi:waypoint x="190.0" y="627.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow76" id="BPMNEdge_flow76">
        <omgdi:waypoint x="210.0" y="607.0"></omgdi:waypoint>
        <omgdi:waypoint x="253.0" y="607.0"></omgdi:waypoint>
        <omgdi:waypoint x="341.0" y="607.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="48.0" x="241.0" y="614.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow77" id="BPMNEdge_flow77">
        <omgdi:waypoint x="190.0" y="587.0"></omgdi:waypoint>
        <omgdi:waypoint x="190.0" y="285.0"></omgdi:waypoint>
        <omgdi:waypoint x="253.0" y="285.0"></omgdi:waypoint>
        <omgdi:waypoint x="341.0" y="285.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="36.0" x="199.0" y="545.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow78" id="BPMNEdge_flow78">
        <omgdi:waypoint x="190.0" y="835.0"></omgdi:waypoint>
        <omgdi:waypoint x="190.0" y="892.0"></omgdi:waypoint>
        <omgdi:waypoint x="374.0" y="893.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow81" id="BPMNEdge_flow81">
        <omgdi:waypoint x="592.0" y="1008.0"></omgdi:waypoint>
        <omgdi:waypoint x="592.0" y="1030.0"></omgdi:waypoint>
        <omgdi:waypoint x="592.0" y="1063.0"></omgdi:waypoint>
        <omgdi:waypoint x="753.0" y="1063.0"></omgdi:waypoint>
        <omgdi:waypoint x="753.0" y="285.0"></omgdi:waypoint>
        <omgdi:waypoint x="446.0" y="285.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="60.0" x="630.0" y="1036.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow82" id="BPMNEdge_flow82">
        <omgdi:waypoint x="393.0" y="455.0"></omgdi:waypoint>
        <omgdi:waypoint x="393.0" y="490.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow83" id="BPMNEdge_flow83">
        <omgdi:waypoint x="393.0" y="530.0"></omgdi:waypoint>
        <omgdi:waypoint x="393.0" y="580.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="72.0" x="403.0" y="530.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow84" id="BPMNEdge_flow84">
        <omgdi:waypoint x="373.0" y="510.0"></omgdi:waypoint>
        <omgdi:waypoint x="275.0" y="509.0"></omgdi:waypoint>
        <omgdi:waypoint x="275.0" y="285.0"></omgdi:waypoint>
        <omgdi:waypoint x="341.0" y="285.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="60.0" x="290.0" y="490.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow85" id="BPMNEdge_flow85">
        <omgdi:waypoint x="393.0" y="313.0"></omgdi:waypoint>
        <omgdi:waypoint x="394.0" y="331.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow86" id="BPMNEdge_flow86">
        <omgdi:waypoint x="394.0" y="371.0"></omgdi:waypoint>
        <omgdi:waypoint x="393.0" y="400.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow87" id="BPMNEdge_flow87">
        <omgdi:waypoint x="374.0" y="351.0"></omgdi:waypoint>
        <omgdi:waypoint x="112.0" y="350.0"></omgdi:waypoint>
        <omgdi:waypoint x="112.0" y="810.0"></omgdi:waypoint>
        <omgdi:waypoint x="138.0" y="807.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="304.0" y="359.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow88" id="BPMNEdge_flow88">
        <omgdi:waypoint x="414.0" y="700.0"></omgdi:waypoint>
        <omgdi:waypoint x="531.0" y="700.0"></omgdi:waypoint>
        <omgdi:waypoint x="531.0" y="286.0"></omgdi:waypoint>
        <omgdi:waypoint x="446.0" y="285.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="100.0" x="435.0" y="680.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>