*******************************************************************************
*                                                                                                                    *
*                                 FacilityONE                                                              *
*                                                                                                                    *
*******************************************************************************

=====================================================
  0 Directory structure
=====================================================

  * Windows
    
    D:/FacilityoneOne-FMSystem
      - www /
          - upload / 文件上传目录
          - before / 上一次的运行代码
          - system / 真实运行代码
          - history /   每次发布的版本记录
          
      - bin /
          - java /
          - server /
          - server-search /
          - db /
          - navicat /
          
      - logs /
      - license.txt
      - start.bat
      - stop.bat
      - install.bat
      - uninstall.bat
      - README.txt
    
  * Linux
  
    /opt/FacilityoneOne-FMSystem
      -
      - 
  
=====================================================
  1 Install And Cofig Required
=====================================================

  * java install
  
  * solr

  * mysql-server install，
  
    启动Navicat，新建数据库fo-fm

  * tomcat-8/bin/startup.bat  

    set JAVA_OPTS= -Xms1024m -Xmx2048m -XX:PermSize=256M -XX:MaxPermSize=512m
    set JAVA_HOME=E:\FMSystem\jdk1.7
    set CATALINA_HOME=D:\FO-System\tomcat-8-solr
    
 * tomcat-8/bin/startup.sh  
 
    export JAVA_OPTS="-Xms2048M -Xmx2048M -Xmn682M -XX:MaxPermSize=96M"

  * tomcat-8/bin/service.bat  install FacilityoneOne-FMSystem

    set JAVA_OPTS= -Xms512m -Xmx1024m -XX:PermSize=256M -XX:MaxPermSize=512m
    set JAVA_HOME=E:\FMSystem\jdk1.7
    set CATALINA_HOME=D:\FO-System\tomcat-8

  * tomcat-8/conf/server.xml

    <Context path="" docBase="E:\FMSystem\f1system\f1" debug="0" reloadable="true" crossContext="true" />
    <Context path="/wechatmedia" docBase="E:\FMSystem\f1system\upload" debug="0" debug="0" reloadable="true" crossContext="true" />

  * f1system/f1/WEB-INF/classes/application.properties

    f1-sa.data.upload.baseDir = E:/FMSystem/f1system/upload
    spring.jta.log_base_dir=E:/FMSystem/f1system/log/jta
    spring.datasource.*=*（Database config）
    xia.message.site.port=9901（WebSite Message Port Config）
    spring.data.solr.host=http://*************:8983/solr
    xia.message.core.debug=false
    xia.quartz.debug=false
    shang.wechat.mediaPath=/wechatmedia
    shang.wechat.defaultArticleUrl=IP

  * f1system/f1/WEB-INF/classes/logback.xml

    <fileNamePattern>E:/FMSystem/f1system/log/shang-%d{yyyy-MM-dd}.%i.log</fileNamePattern>

  * f1system/f1/WEB-INF/classes/static/index.ftl

    window.DEBUG=false

  * 字体 - （二维码图片文字字体问题）
  
    g2d.setFont(new Font("宋体", Font.PLAIN, 12));  
    体文件地址是“C:\WINDOWS\Fonts\simsun.ttc”，上传到服务器地址是：“/usr/jdk/instances/jdk1.5.0/jre/lib/fonts/simsun.ttc” , simsun.ttf
   
   http://blog.csdn.net/jgwei/article/details/7739583
   
  * 百度流量监控码
     
  * （windows）更改创建startup.bat快捷方式到桌面，并更改图标

===============================================================================
  2 DB Required
===============================================================================
  
  * 导入data-xaDataSource.sql

===============================================================================
  3 Solr Required
===============================================================================
  
  * solr/README.m

===============================================================================
  4 Config-Optional
===============================================================================

  * f1system/f1/WEB-INF/classes/application.properties

    xia.message.email.*=* (Email Server config)
    xia.message.sms.*=*（Sms Interface config）

===============================================================================
  5 Windows Service Start
===============================================================================

  * 打包时
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
  * bin/service.bat 修改
    CATALINDA_HOME
    JAVA_HOME
    JDK
    Xms Xmx XX:PermSize XX:MaxPermSize
    
  * bin/service.bat install FOFMSystem 
  
    FOFMSystem-search（solr）
    
  * cmd services.msc 设置fmsystem开机启动

===============================================================================
  5 https with ssl
===============================================================================

* 申请ssl认证，获得domain.jks认证文件（保存好密钥文件，密码）

* apache-tomcat-443  

  * file : apache-tomcat-443/config/wechat.fmone.cn.jks
    
  * server.xml

    ```
     <Connector port="80" enableLookups="false" redirectPort="443"/> 

    ```

    ```
     <Connector port="443" protocol="org.apache.coyote.http11.Http11NioProtocol"
               maxThreads="150" SSLEnabled="true" scheme="https" secure="true"
               keystoreFile="/opt/apache-tomcat-80/conf/wechat.fmone.cn.jks" keystorePass="fone20160418ABC" 
               clientAuth="false" sslProtocol="TLS" />
    ```

    ```
     <Connector port="8009" enableLookups="false" redirectPort="443" protocol="AJP/1.3" /> 

    ```

  * application.properties
        
        * xia.message.site.keystore=/opt/apache-tomcat-443/conf/wechat.fmone.cn.jks
        * xia.message.site.keystorepwd=fone20160418ABC
        
  
  * 强制https访问  在tomcat\conf\web.xml中的</welcome-file-list>后面加上这样一段：

  ```
<login-config>  
    <!-- Authorization setting for SSL -->  
    <auth-method>CLIENT-CERT</auth-method>  
    <realm-name>Client Cert Users-only Area</realm-name>  
</login-config>  
<security-constraint>  
    <!-- Authorization setting for SSL -->  
    <web-resource-collection >  
        <web-resource-name >SSL</web-resource-name>  
        <url-pattern>/*</url-pattern>  
    </web-resource-collection>  
    <user-data-constraint>  
        <transport-guarantee>CONFIDENTIAL</transport-guarantee>  
    </user-data-constraint>  
</security-constraint> 
  ```
  *帆软
    *版本：9.0
    *存放目录(费哲云盘)：/homes/F-ONE-WX/开发部门/后台部门/软件