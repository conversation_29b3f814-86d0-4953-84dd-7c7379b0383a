# Created by .gitignore support plugin (hsz.mobi)

### Eclipse template
*.pydevproject
.metadata
.gradle
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
*.project
*.classpath

# External tool builders
.externalToolBuilders/

# Locally stored "Eclipse launch configurations"
*.launch

# CDT-specific
.cproject

# PDT-specific
.buildpath

# sbteclipse plugin

.target

# TeXlipse plugin
.texlipse

*.iml
.idea/

target/
classes/

log.roo
*.lck
*.log
*.epoch
*.vpp~*
*.vpp.working
*.orig
*.vpp.bak~*
src/test/resources/upload/
src/main/resources/static/upload/

.xml/
/bin/

.DS_Store

# for ignore style test of front end
src/main/java/cn/facilityone/shang/stylepreview
src/main/resources/static/business/stylepreview
src/main/resources/static/page-preview.ftl

*.iws
*.ipr

